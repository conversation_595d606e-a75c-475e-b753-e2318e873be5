package availability

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/query"
	mock "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/service/mocks"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

func TestManager_NewManager(t *testing.T) {
	// 由于 NewManager 会初始化真实的 repository，这里我们跳过这个测试
	// 在实际环境中，应该使用依赖注入或者 mock 来测试
	t.Skip("Skipping test that requires database connection")
}

func TestManager_NewManagerWithRepo(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockPetScopeRepository(ctrl)
	manager := NewManagerWithRepository(mockRepo)
	assert.NotNil(t, manager)
	assert.NotNil(t, manager.typeBreedLogic)
	assert.NotNil(t, manager.petSizeLogic)
	assert.NotNil(t, manager.coatTypeLogic)
	assert.NotNil(t, manager.petCodeLogic)
	assert.Equal(t, mockRepo, manager.petScopeRepo)
}

func TestManager_Save(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockPetScopeRepository(ctrl)
	manager := NewManagerWithRepository(mockRepo)
	ctx := context.Background()
	mockQuery := &query.Query{}

	// 设置 mock 的期望行为
	mockRepo.EXPECT().WithQuery(mockQuery).Return(mockRepo).AnyTimes()
	mockRepo.EXPECT().BatchCreate(ctx, gomock.Any()).Return(nil).AnyTimes()

	tests := []struct {
		name         string
		availability *PetAvailability
		expectError  bool
	}{
		{
			name: "save with all pet availability types",
			availability: &PetAvailability{
				ServiceID: 123,
				PetTypeBreed: &offeringpb.AvailablePetTypeBreed{
					IsAll: true,
				},
				PetSize: &offeringpb.AvailablePetSize{
					IsAll: true,
				},
				CoatType: &offeringpb.AvailableCoatType{
					IsAll: true,
				},
				PetCode: &offeringpb.AvailablePetCode{
					RuleType: offeringpb.AvailabilityRuleType_NO_RESTRICTION,
				},
			},
			expectError: false,
		},
		{
			name: "save with partial pet availability types",
			availability: &PetAvailability{
				ServiceID: 456,
				PetTypeBreed: &offeringpb.AvailablePetTypeBreed{
					IsAll: false,
					AvailablePetTypes: []*offeringpb.AvailablePetType{
						{
							PetTypeId: 1,
							IsAll:     true,
							BreedIds:  []int64{},
						},
					},
				},
				PetSize:  nil,
				CoatType: nil,
				PetCode:  nil,
			},
			expectError: false,
		},
		{
			name: "save with nil availability should not error",
			availability: &PetAvailability{
				ServiceID:    789,
				PetTypeBreed: nil,
				PetSize:      nil,
				CoatType:     nil,
				PetCode:      nil,
			},
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := manager.Save(ctx, mockQuery, tt.availability)
			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestManager_Update(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockPetScopeRepository(ctrl)
	manager := NewManagerWithRepository(mockRepo)
	ctx := context.Background()
	mockQuery := &query.Query{}

	availability := &PetAvailability{
		ServiceID: 123,
		PetTypeBreed: &offeringpb.AvailablePetTypeBreed{
			IsAll: true,
		},
		PetSize: &offeringpb.AvailablePetSize{
			IsAll: true,
		},
		CoatType: &offeringpb.AvailableCoatType{
			IsAll: true,
		},
		PetCode: &offeringpb.AvailablePetCode{
			RuleType: offeringpb.AvailabilityRuleType_NO_RESTRICTION,
		},
	}

	// 设置 mock 的期望行为
	mockRepo.EXPECT().WithQuery(mockQuery).Return(mockRepo).Times(2) // Update调用DeleteByServiceID和Save，Save会再次调用WithQuery
	mockRepo.EXPECT().DeleteByServiceID(ctx, int64(123)).Return(nil)
	mockRepo.EXPECT().BatchCreate(ctx, gomock.Any()).Return(nil)

	err := manager.Update(ctx, mockQuery, availability)
	assert.NoError(t, err)
}

func TestManager_DeleteByServiceID(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockPetScopeRepository(ctrl)
	manager := NewManagerWithRepository(mockRepo)
	ctx := context.Background()
	mockQuery := &query.Query{}
	serviceID := int64(123)

	// 设置 mock 的期望行为
	mockRepo.EXPECT().WithQuery(mockQuery).Return(mockRepo)
	mockRepo.EXPECT().DeleteByServiceID(ctx, serviceID).Return(nil)

	err := manager.DeleteByServiceID(ctx, mockQuery, serviceID)
	assert.NoError(t, err)
}

func TestManager_GetByServiceID(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockPetScopeRepository(ctrl)
	manager := NewManagerWithRepository(mockRepo)
	ctx := context.Background()
	serviceID := int64(123)

	// 设置 mock 的期望行为
	mockRepo.EXPECT().List(ctx, gomock.Any()).Return([]*model.ServicePetAvailabilityScope{}, nil)

	result, err := manager.GetByServiceID(ctx, serviceID)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, serviceID, result.ServiceID)
}

func TestManager_ProtoToModel_Integration(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockPetScopeRepository(ctrl)
	manager := NewManagerWithRepository(mockRepo)
	serviceID := int64(123)

	// 测试完整的转换流程
	availability := &PetAvailability{
		ServiceID: serviceID,
		PetTypeBreed: &offeringpb.AvailablePetTypeBreed{
			IsAll: false,
			AvailablePetTypes: []*offeringpb.AvailablePetType{
				{
					PetTypeId: 1,
					IsAll:     true,
					BreedIds:  []int64{},
				},
			},
		},
		PetSize: &offeringpb.AvailablePetSize{
			IsAll:      false,
			PetSizeIds: []int64{1, 2},
		},
		CoatType: &offeringpb.AvailableCoatType{
			IsAll:       false,
			CoatTypeIds: []int64{1},
		},
		PetCode: &offeringpb.AvailablePetCode{
			RuleType:   offeringpb.AvailabilityRuleType_INCLUDE,
			PetCodeIds: []int64{1, 2, 3},
		},
	}

	// 测试各个 logic 的 ProtoToModel 方法
	typeBreedScopes := manager.typeBreedLogic.ProtoToModel(serviceID, availability.PetTypeBreed)
	assert.Len(t, typeBreedScopes, 1)
	assert.Equal(t, offeringpb.PetAvailabilityScopeType_SPECIFIC_TYPE, typeBreedScopes[0].ScopeType)
	assert.Equal(t, int64(1), typeBreedScopes[0].TargetID)

	petSizeScopes := manager.petSizeLogic.ProtoToModel(serviceID, availability.PetSize)
	assert.Len(t, petSizeScopes, 2)
	assert.Equal(t, offeringpb.PetAvailabilityScopeType_SPECIFIC_SIZE, petSizeScopes[0].ScopeType)
	assert.Equal(t, int64(1), petSizeScopes[0].TargetID)

	coatTypeScopes := manager.coatTypeLogic.ProtoToModel(serviceID, availability.CoatType)
	assert.Len(t, coatTypeScopes, 1)
	assert.Equal(t, offeringpb.PetAvailabilityScopeType_SPECIFIC_COAT_TYPE, coatTypeScopes[0].ScopeType)
	assert.Equal(t, int64(1), coatTypeScopes[0].TargetID)

	petCodeScopes := manager.petCodeLogic.ProtoToModel(serviceID, availability.PetCode)
	assert.Len(t, petCodeScopes, 3)
	assert.Equal(t, offeringpb.PetAvailabilityScopeType_SPECIFIC_CODE, petCodeScopes[0].ScopeType)
	assert.Equal(t, int64(1), petCodeScopes[0].TargetID)
}
