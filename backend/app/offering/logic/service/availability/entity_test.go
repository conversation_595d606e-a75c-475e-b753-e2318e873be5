package availability

import (
	"testing"

	"github.com/stretchr/testify/assert"

	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

func TestPetAvailability_Structure(t *testing.T) {
	// 测试 PetAvailability 结构的基本功能
	availability := &PetAvailability{
		ServiceID: 123,
		PetTypeBreed: &offeringpb.AvailablePetTypeBreed{
			IsAll: true,
		},
		PetSize: &offeringpb.AvailablePetSize{
			IsAll: true,
		},
		CoatType: &offeringpb.AvailableCoatType{
			IsAll: true,
		},
		PetCode: &offeringpb.AvailablePetCode{
			RuleType: offeringpb.AvailabilityRuleType_NO_RESTRICTION,
		},
	}

	assert.Equal(t, int64(123), availability.ServiceID)
	assert.NotNil(t, availability.PetTypeBreed)
	assert.NotNil(t, availability.PetSize)
	assert.NotNil(t, availability.CoatType)
	assert.NotNil(t, availability.PetCode)
	assert.True(t, availability.PetTypeBreed.IsAll)
	assert.True(t, availability.PetSize.IsAll)
	assert.True(t, availability.CoatType.IsAll)
	assert.Equal(t, offeringpb.AvailabilityRuleType_NO_RESTRICTION, availability.PetCode.RuleType)
}

func TestPetAvailability_PartialFields(t *testing.T) {
	// 测试部分字段为 nil 的情况
	availability := &PetAvailability{
		ServiceID:    456,
		PetTypeBreed: nil,
		PetSize:      nil,
		CoatType:     nil,
		PetCode:      nil,
	}

	assert.Equal(t, int64(456), availability.ServiceID)
	assert.Nil(t, availability.PetTypeBreed)
	assert.Nil(t, availability.PetSize)
	assert.Nil(t, availability.CoatType)
	assert.Nil(t, availability.PetCode)
}

func TestPetAvailability_MixedFields(t *testing.T) {
	// 测试混合字段的情况
	availability := &PetAvailability{
		ServiceID: 789,
		PetTypeBreed: &offeringpb.AvailablePetTypeBreed{
			IsAll: false,
			AvailablePetTypes: []*offeringpb.AvailablePetType{
				{
					PetTypeId: 1,
					IsAll:     true,
					BreedIds:  []int64{},
				},
			},
		},
		PetSize: nil, // 部分字段为 nil
		CoatType: &offeringpb.AvailableCoatType{
			IsAll:       false,
			CoatTypeIds: []int64{1, 2},
		},
		PetCode: nil, // 部分字段为 nil
	}

	assert.Equal(t, int64(789), availability.ServiceID)
	assert.NotNil(t, availability.PetTypeBreed)
	assert.Nil(t, availability.PetSize)
	assert.NotNil(t, availability.CoatType)
	assert.Nil(t, availability.PetCode)

	// 验证 PetTypeBreed 字段
	assert.False(t, availability.PetTypeBreed.IsAll)
	assert.Len(t, availability.PetTypeBreed.AvailablePetTypes, 1)
	assert.Equal(t, int64(1), availability.PetTypeBreed.AvailablePetTypes[0].PetTypeId)
	assert.True(t, availability.PetTypeBreed.AvailablePetTypes[0].IsAll)

	// 验证 CoatType 字段
	assert.False(t, availability.CoatType.IsAll)
	assert.Len(t, availability.CoatType.CoatTypeIds, 2)
	assert.Equal(t, int64(1), availability.CoatType.CoatTypeIds[0])
	assert.Equal(t, int64(2), availability.CoatType.CoatTypeIds[1])
}

func TestPetAvailability_Integration(t *testing.T) {
	// 测试 PetAvailability 和 PetInfo 的集成使用场景

	// 创建一个完整的 PetAvailability 配置
	availability := &PetAvailability{
		ServiceID: 1001,
		PetTypeBreed: &offeringpb.AvailablePetTypeBreed{
			IsAll: false,
			AvailablePetTypes: []*offeringpb.AvailablePetType{
				{
					PetTypeId: 1,
					IsAll:     false,
					BreedIds:  []int64{1, 2},
				},
				{
					PetTypeId: 2,
					IsAll:     true,
					BreedIds:  []int64{},
				},
			},
		},
		PetSize: &offeringpb.AvailablePetSize{
			IsAll:      false,
			PetSizeIds: []int64{1, 2, 3},
		},
		CoatType: &offeringpb.AvailableCoatType{
			IsAll:       true,
			CoatTypeIds: []int64{},
		},
		PetCode: &offeringpb.AvailablePetCode{
			RuleType:   offeringpb.AvailabilityRuleType_EXCLUDE,
			PetCodeIds: []int64{1, 2},
		},
	}

	// 验证配置的正确性
	assert.Equal(t, int64(1001), availability.ServiceID)

	// 验证 PetTypeBreed 配置
	assert.False(t, availability.PetTypeBreed.IsAll)
	assert.Len(t, availability.PetTypeBreed.AvailablePetTypes, 2)

	// 第一个宠物类型：指定品种
	firstPetType := availability.PetTypeBreed.AvailablePetTypes[0]
	assert.Equal(t, int64(1), firstPetType.PetTypeId)
	assert.False(t, firstPetType.IsAll)
	assert.ElementsMatch(t, []int64{1, 2}, firstPetType.BreedIds)

	// 第二个宠物类型：所有品种
	secondPetType := availability.PetTypeBreed.AvailablePetTypes[1]
	assert.Equal(t, int64(2), secondPetType.PetTypeId)
	assert.True(t, secondPetType.IsAll)
	assert.Empty(t, secondPetType.BreedIds)

	// 验证 PetSize 配置
	assert.False(t, availability.PetSize.IsAll)
	assert.ElementsMatch(t, []int64{1, 2, 3}, availability.PetSize.PetSizeIds)

	// 验证 CoatType 配置
	assert.True(t, availability.CoatType.IsAll)
	assert.Empty(t, availability.CoatType.CoatTypeIds)

	// 验证 PetCode 配置
	assert.Equal(t, offeringpb.AvailabilityRuleType_EXCLUDE, availability.PetCode.RuleType)
	assert.ElementsMatch(t, []int64{1, 2}, availability.PetCode.PetCodeIds)
}
