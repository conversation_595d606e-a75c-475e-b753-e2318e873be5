package service

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"

	"github.com/MoeGolibrary/moego/backend/app/offering/logic/association"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/service/attribute"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/service/availability"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/service/obsetting"
	mock4 "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/association/mocks"
	model2 "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/service"
	mock3 "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/service/mocks"
	mock2 "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/serviceattribute/mocks"
	obsettingmocks "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/serviceobsetting/mocks"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	organizationpb "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
)

// testHelper 测试辅助结构体
type testHelper struct {
	ctrl                  *gomock.Controller
	mockServiceRepo       *mock3.MockRepository
	mockBasicRepo         *mock2.MockRepository
	mockRolloverRepo      *mock2.MockAutoRollOverRepository
	mockLodgingRepo       *mock2.MockLodgingScopeRepository
	mockStaffRepo         *mock2.MockStaffScopeRepository
	mockOBSettingRepo     *obsettingmocks.MockRepository
	mockBusinessScopeRepo *mock3.MockBusinessScopeRepository
	mockAssociationRepo   *mock4.MockRepository
	mockPetScopeRepo      *mock3.MockPetScopeRepository
	logic                 *Logic
}

// newTestHelper 创建测试辅助实例
func newTestHelper(t *testing.T) *testHelper {
	ctrl := gomock.NewController(t)

	mockServiceRepo := mock3.NewMockRepository(ctrl)
	mockBasicRepo := mock2.NewMockRepository(ctrl)
	mockRolloverRepo := mock2.NewMockAutoRollOverRepository(ctrl)
	mockLodgingRepo := mock2.NewMockLodgingScopeRepository(ctrl)
	mockStaffRepo := mock2.NewMockStaffScopeRepository(ctrl)
	mockOBSettingRepo := obsettingmocks.NewMockRepository(ctrl)
	mockBusinessScopeRepo := mock3.NewMockBusinessScopeRepository(ctrl)
	mockAssociationRepo := mock4.NewMockRepository(ctrl)
	mockPetScopeRepo := mock3.NewMockPetScopeRepository(ctrl)

	manager := attribute.NewManagerWithRepositories(
		mockBasicRepo, mockRolloverRepo, mockLodgingRepo, mockStaffRepo)

	obSettingLogic := obsetting.NewWithRepository(mockOBSettingRepo)
	businessScopeLogic := NewBusinessScopeWithRepository(mockBusinessScopeRepo)
	associationLogic := association.NewLogicWithRepository(mockAssociationRepo)
	availabilityManager := availability.NewManagerWithRepository(mockPetScopeRepo)

	logic := &Logic{
		repo:                mockServiceRepo,
		manager:             manager,
		availabilityManager: availabilityManager,
		obSettingLogic:      obSettingLogic,
		businessScopeLogic:  businessScopeLogic,
		associationLogic:    associationLogic,
	}

	return &testHelper{
		ctrl:                  ctrl,
		mockServiceRepo:       mockServiceRepo,
		mockBasicRepo:         mockBasicRepo,
		mockRolloverRepo:      mockRolloverRepo,
		mockLodgingRepo:       mockLodgingRepo,
		mockStaffRepo:         mockStaffRepo,
		mockOBSettingRepo:     mockOBSettingRepo,
		mockBusinessScopeRepo: mockBusinessScopeRepo,
		mockAssociationRepo:   mockAssociationRepo,
		mockPetScopeRepo:      mockPetScopeRepo,
		logic:                 logic,
	}
}

// finish 清理测试资源
func (h *testHelper) finish() {
	h.ctrl.Finish()
}

// setupAttributeMocks 设置属性相关的 mock 期望
// 根据 service attributes 的值来决定是否设置相关 repo 的 mock 期望
func (h *testHelper) setupAttributeMocks(serviceIDs []int64, serviceAttributes *offeringpb.ServiceAttributes) {
	// 只有当 serviceAttributes 不为空且包含相关属性时，才设置 BasicProcessor 的 mock
	if serviceAttributes != nil && (serviceAttributes.Duration != nil || serviceAttributes.MaxDuration != nil) {
		// BasicProcessor 现在使用 upsert 逻辑，需要为每个 serviceID 设置 ListByServiceID 调用
		for _, serviceID := range serviceIDs {
			h.mockBasicRepo.EXPECT().ListByServiceID(gomock.Any(), serviceID).Return([]*model2.ServiceAttribute{}, nil)

			// 为每个属性设置 Create 的 mock 调用（新记录）
			if serviceAttributes.Duration != nil {
				h.mockBasicRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)
			}
			if serviceAttributes.MaxDuration != nil {
				h.mockBasicRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)
			}
		}
	}

	// 只有当 serviceAttributes 不为空且包含 AutoRollover 时，才设置 AutoRolloverProcessor 的 mock
	if serviceAttributes != nil && serviceAttributes.AutoRollover != nil {
		for _, serviceID := range serviceIDs {
			h.mockRolloverRepo.EXPECT().GetByServiceID(gomock.Any(), serviceID).Return(nil, nil)
			h.mockRolloverRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)
		}
	}

	// 只有当 serviceAttributes 不为空且包含 AvailableStaff 时，才设置 StaffProcessor 的 mock
	if serviceAttributes != nil && serviceAttributes.AvailableStaff != nil {
		for _, serviceID := range serviceIDs {
			h.mockStaffRepo.EXPECT().GetByServiceID(gomock.Any(), serviceID).Return(nil, nil)
			h.mockStaffRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)
		}
	}

	// 只有当 serviceAttributes 不为空且包含 AvailableLodgingType 时，才设置 LodgingProcessor 的 mock
	if serviceAttributes != nil && serviceAttributes.AvailableLodgingType != nil {
		for _, serviceID := range serviceIDs {
			h.mockLodgingRepo.EXPECT().GetByServiceID(gomock.Any(), serviceID).Return(nil, nil)
			h.mockLodgingRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)
		}
	}
}

// setupListServicesAttributeMocks 设置 ListServices 测试中的属性相关 mock 期望
// 在 ListServices 中，代码调用 manager.ListByServiceIDs，这会调用各个 processor 的 ListByServiceIDs 方法
func (h *testHelper) setupListServicesAttributeMocks(serviceIDs []int64) {
	// 为 BasicProcessor 设置 ListByServiceIDs 的 mock 期望
	h.mockBasicRepo.EXPECT().ListByServiceIDs(gomock.Any(), serviceIDs).Return([]*model2.ServiceAttribute{}, nil)

	// 为 AutoRolloverProcessor 设置 ListByServiceIDs 的 mock 期望
	h.mockRolloverRepo.EXPECT().ListByServiceIDs(gomock.Any(), serviceIDs).Return([]*model2.ServiceAutoRollover{}, nil)

	// 为 StaffProcessor 设置 ListByServiceIDs 的 mock 期望
	h.mockStaffRepo.EXPECT().ListByServiceIDs(gomock.Any(), serviceIDs).Return([]*model2.ServiceStaffScope{}, nil)

	// 为 LodgingProcessor 设置 ListByServiceIDs 的 mock 期望
	h.mockLodgingRepo.EXPECT().ListByServiceIDs(gomock.Any(), serviceIDs).Return([]*model2.ServiceLodgingScope{}, nil)
}

func TestLogic_CreateService(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 创建 protobuf 请求对象
	createDef := &offeringpb.ServiceCreateDef{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		CareTypeId:       1,
		CategoryId:       lo.ToPtr(int64(2)),
		Name:             "Test Service Template",
		Description:      stringPtr("Test Description"),
		ColorCode:        "#FF0000",
		Images:           []string{"image1.jpg", "image2.jpg"},
		Source:           offeringpb.OfferingSource_MOEGO,
		Status:           offeringpb.Service_ACTIVE,
		AvailableBusiness: &offeringpb.AvailableBusiness{
			IsAll:       true,
			BusinessIds: []int64{},
		},
	}

	// 新增：CreateService 前会进行名称唯一性校验，需期望调用 List
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*model2.Service{}, int64(0), nil)

	helper.mockServiceRepo.EXPECT().Create(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, m *model2.Service) error {
		m.ID = 1
		now := time.Now()
		m.CreateTime = &now
		m.UpdateTime = &now
		return nil
	})

	// 设置期望 - 更新 sort 为 ID
	helper.mockServiceRepo.EXPECT().Update(gomock.Any(), gomock.Any()).DoAndReturn(
		func(ctx context.Context, service *model2.Service) error {
			assert.Equal(t, int64(1), service.Sort) // 验证 sort 被设置为 ID
			return nil
		})

	// 设置期望 - BusinessScope 的 upsert 逻辑
	// 先尝试查询现有记录（返回 nil，表示需要创建新记录）
	helper.mockBusinessScopeRepo.EXPECT().GetByServiceID(gomock.Any(), int64(1)).Return(nil, nil)
	// 创建新记录
	helper.mockBusinessScopeRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)

	// 当 serviceAttributes 为空时，不需要设置属性相关的 mock
	// 因为 BasicProcessor 的 Save 方法会直接返回

	id, err := helper.logic.CreateService(context.Background(), createDef)

	assert.NoError(t, err)
	assert.Equal(t, int64(1), id)
}

func TestLogic_CreateService_WithAttributes(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 新增：CreateService 前会进行名称唯一性校验，需期望调用 List
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*model2.Service{}, int64(0), nil)

	helper.mockServiceRepo.EXPECT().Create(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, m *model2.Service) error {
		m.ID = 1
		now := time.Now()
		m.CreateTime = &now
		m.UpdateTime = &now
		return nil
	})

	// 设置期望 - 更新 sort 为 ID
	helper.mockServiceRepo.EXPECT().Update(gomock.Any(), gomock.Any()).DoAndReturn(
		func(ctx context.Context, service *model2.Service) error {
			assert.Equal(t, int64(1), service.Sort) // 验证 sort 被设置为 ID
			return nil
		})

	// 设置期望 - BusinessScope 的 upsert 逻辑
	// 先尝试查询现有记录（返回 not found，表示需要创建新记录）
	helper.mockBusinessScopeRepo.EXPECT().GetByServiceID(gomock.Any(), int64(1)).Return(nil, nil)
	// 创建新记录
	helper.mockBusinessScopeRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)

	// 设置属性相关的 mock，因为 BasicProcessor 现在会调用 ListByServiceID
	// BasicProcessor 现在使用 upsert 逻辑，需要设置 ListByServiceID 调用
	helper.mockBasicRepo.EXPECT().ListByServiceID(gomock.Any(), int64(1)).Return([]*model2.ServiceAttribute{}, nil)

	// 为 duration 属性设置 Create 调用（因为这是新记录）
	helper.mockBasicRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)

	// 注意：在 CreateService 过程中，ListByServiceIDs 不会被调用
	// 只有在 GetService 或 ListServices 过程中才会调用 ListByServiceIDs

	// 创建 protobuf 请求对象
	createDef := &offeringpb.ServiceCreateDef{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		CareTypeId:       1,
		CategoryId:       lo.ToPtr(int64(2)),
		Name:             "Test Service Template",
		Description:      stringPtr("Test Description"),
		ColorCode:        "#FF0000",
		Images:           []string{"image1.jpg", "image2.jpg"},
		Source:           offeringpb.OfferingSource_MOEGO,
		Status:           offeringpb.Service_ACTIVE,
		AvailableBusiness: &offeringpb.AvailableBusiness{
			IsAll:       true,
			BusinessIds: []int64{},
		},
		Attributes: &offeringpb.ServiceAttributes{
			Duration: lo.ToPtr(int32(60)),
		},
	}

	id, err := helper.logic.CreateService(context.Background(), createDef)

	assert.NoError(t, err)
	assert.Equal(t, int64(1), id)
}

func TestLogic_CreateService_WithComplexAttributes(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 新增：CreateService 前会进行名称唯一性校验，需期望调用 List
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*model2.Service{}, int64(0), nil)

	helper.mockServiceRepo.EXPECT().Create(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, m *model2.Service) error {
		m.ID = 1
		now := time.Now()
		m.CreateTime = &now
		m.UpdateTime = &now
		return nil
	})

	// 设置期望 - 更新 sort 为 ID
	helper.mockServiceRepo.EXPECT().Update(gomock.Any(), gomock.Any()).DoAndReturn(
		func(ctx context.Context, service *model2.Service) error {
			assert.Equal(t, int64(1), service.Sort) // 验证 sort 被设置为 ID
			return nil
		})

	attributes := &offeringpb.ServiceAttributes{
		Duration: lo.ToPtr(int32(60)),
		AutoRollover: &offeringpb.AutoRollover{
			TargetServiceId: 2,
			AfterMinute:     30,
		},
	}
	// 使用 setupAttributeMocks 设置属性相关的 mock 期望
	// 由于 attributes 包含 Duration 和 AutoRollover，需要设置相应的 mock
	helper.setupAttributeMocks([]int64{1}, attributes)

	// 设置期望 - BusinessScope 的 upsert 逻辑
	// 先尝试查询现有记录（返回 not found，表示需要创建新记录）
	helper.mockBusinessScopeRepo.EXPECT().GetByServiceID(gomock.Any(), int64(1)).Return(nil, nil)
	// 创建新记录
	helper.mockBusinessScopeRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)

	// 创建 protobuf 请求对象
	createDef := &offeringpb.ServiceCreateDef{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		CareTypeId:       1,
		CategoryId:       lo.ToPtr(int64(2)),
		Name:             "Test Service Template",
		Description:      stringPtr("Test Description"),
		ColorCode:        "#FF0000",
		Images:           []string{"image1.jpg", "image2.jpg"},
		Source:           offeringpb.OfferingSource_MOEGO,
		Status:           offeringpb.Service_ACTIVE,
		AvailableBusiness: &offeringpb.AvailableBusiness{
			IsAll:       true,
			BusinessIds: []int64{},
		},
		Attributes: attributes,
	}

	id, err := helper.logic.CreateService(context.Background(), createDef)

	assert.NoError(t, err)
	assert.Equal(t, int64(1), id)
}

func TestLogic_CreateService_Error(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 新增：CreateService 前会进行名称唯一性校验，需期望调用 List
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*model2.Service{}, int64(0), nil)

	helper.mockServiceRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(errors.New("database error"))

	// 创建 protobuf 请求对象
	createDef := &offeringpb.ServiceCreateDef{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		CareTypeId:       1,
		CategoryId:       lo.ToPtr(int64(2)),
		Name:             "Test Service Template",
		Description:      stringPtr("Test Description"),
		ColorCode:        "#FF0000",
		Images:           []string{"image1.jpg", "image2.jpg"},
		Source:           offeringpb.OfferingSource_MOEGO,
		Status:           offeringpb.Service_ACTIVE,
		AvailableBusiness: &offeringpb.AvailableBusiness{
			IsAll:       true,
			BusinessIds: []int64{},
		},
	}

	id, err := helper.logic.CreateService(context.Background(), createDef)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "database error")
	assert.Equal(t, int64(0), id)
}

func TestLogic_CreateService_DuplicateName(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	createDef := &offeringpb.ServiceCreateDef{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		CategoryId:       lo.ToPtr(int64(2)),
		Name:             "Test Service Template",
	}

	// 名称唯一性校验：模拟已存在同名服务
	filter := &service.ListServiceFilter{
		OrganizationType: createDef.GetOrganizationType(),
		OrganizationID:   createDef.GetOrganizationId(),
		Keyword:          lo.ToPtr(createDef.GetName()),
	}
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), filter, nil).Return([]*model2.Service{{ID: 99}}, int64(1), nil)

	id, err := helper.logic.CreateService(context.Background(), createDef)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "service name already exists")
	assert.Equal(t, int64(0), id)
}

func TestLogic_CreateService_AttributeError(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 新增：CreateService 前会进行名称唯一性校验，需期望调用 List
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*model2.Service{}, int64(0), nil)

	// 1. 设置 Service 相关的 mock 调用
	helper.mockServiceRepo.EXPECT().Create(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, m *model2.Service) error {
		m.ID = 1
		now := time.Now()
		m.CreateTime = &now
		m.UpdateTime = &now
		return nil
	})

	helper.mockServiceRepo.EXPECT().Update(gomock.Any(), gomock.Any()).DoAndReturn(
		func(ctx context.Context, service *model2.Service) error {
			assert.Equal(t, int64(1), service.Sort) // 验证 sort 被设置为 ID
			return nil
		})

	// 2. 不需要设置 BusinessScope 相关的 mock 调用，因为属性保存失败后会提前返回错误

	// 3. 设置 BasicProcessor 相关的 mock 调用
	helper.mockBasicRepo.EXPECT().ListByServiceID(gomock.Any(), int64(1)).Return([]*model2.ServiceAttribute{}, nil)
	helper.mockBasicRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(errors.New("attribute save error"))

	// 创建 protobuf 请求对象
	createDef := &offeringpb.ServiceCreateDef{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		CareTypeId:       1,
		CategoryId:       lo.ToPtr(int64(2)),
		Name:             "Test Service Template",
		Description:      stringPtr("Test Description"),
		ColorCode:        "#FF0000",
		Images:           []string{"image1.jpg", "image2.jpg"},
		Source:           offeringpb.OfferingSource_MOEGO,
		Status:           offeringpb.Service_ACTIVE,
		AvailableBusiness: &offeringpb.AvailableBusiness{
			IsAll:       true,
			BusinessIds: []int64{},
		},
		Attributes: &offeringpb.ServiceAttributes{
			Duration: lo.ToPtr(int32(60)),
		},
	}

	id, err := helper.logic.CreateService(context.Background(), createDef)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "attribute save error")
	assert.Equal(t, int64(0), id)
}

func TestLogic_GetService(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	now := time.Now()
	mockModel := &model2.Service{
		ID:               1,
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationID:   123,
		CareTypeID:       1,
		CategoryID:       2,
		Name:             "Test Service Template",
		Description:      stringPtr("Test Description"),
		ColorCode:        "#FF0000",
		Sort:             1,
		Images:           []string{"image1.jpg", "image2.jpg"},
		Source:           offeringpb.OfferingSource_MOEGO,
		Status:           offeringpb.Service_ACTIVE,
		CreateTime:       &now,
		UpdateTime:       &now,
	}

	helper.mockServiceRepo.EXPECT().Get(gomock.Any(), int64(1)).Return(mockModel, nil)
	helper.mockServiceRepo.EXPECT().Get(gomock.Any(), int64(2)).Return(nil, errors.New("not found"))

	// 为attribute manager的mock设置期望
	// BasicProcessor 只调用一次 ListByServiceIDs
	helper.mockBasicRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1}).Return([]*model2.ServiceAttribute{}, nil)
	helper.mockRolloverRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1}).Return([]*model2.ServiceAutoRollover{}, nil)
	// 为新的 staff 和 lodging processor 设置 mock 期望
	helper.mockStaffRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1}).Return([]*model2.ServiceStaffScope{}, nil)
	helper.mockLodgingRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1}).Return([]*model2.ServiceLodgingScope{}, nil)
	helper.mockBusinessScopeRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1}).Return([]*model2.ServiceBusinessScope{
		{
			ID:                   1,
			ServiceID:            1,
			IsAllBusiness:        false,
			AvailableBusinessIds: []int64{1},
		},
	}, nil)
	helper.mockAssociationRepo.EXPECT().GetByServiceID(gomock.Any(), int64(1)).Return([]*model2.ServiceAssociation{}, nil)

	// Mock expectations for pet availability
	helper.mockPetScopeRepo.EXPECT().List(gomock.Any(), gomock.Any()).Return(nil, nil)

	result, err := helper.logic.GetService(context.Background(), 1)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, int64(1), result.GetId())
	assert.Equal(t, "Test Service Template", result.GetName())
	assert.Equal(t, organizationpb.OrganizationType_COMPANY, result.GetOrganizationType())
	assert.Equal(t, int64(123), result.GetOrganizationId())
	assert.Equal(t, int64(1), result.GetCareTypeId())
	assert.Equal(t, int64(2), result.GetCategoryId())
	assert.Equal(t, "Test Description", result.GetDescription())
	assert.Equal(t, "#FF0000", result.GetColorCode())
	assert.Equal(t, int64(1), result.GetSort())
	assert.Equal(t, []string{"image1.jpg", "image2.jpg"}, result.GetImages())
	assert.Equal(t, offeringpb.OfferingSource_MOEGO, result.GetSource())
	assert.Equal(t, offeringpb.Service_ACTIVE, result.GetStatus())

	_, err = helper.logic.GetService(context.Background(), 2)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "not found")
}

func TestLogic_GetService_WithAttributes(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	now := time.Now()
	mockModel := &model2.Service{
		ID:               1,
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationID:   123,
		CareTypeID:       1,
		CategoryID:       2,
		Name:             "Test Service Template",
		Description:      stringPtr("Test Description"),
		ColorCode:        "#FF0000",
		Sort:             1,
		Images:           []string{"image1.jpg", "image2.jpg"},
		Source:           offeringpb.OfferingSource_MOEGO,
		Status:           offeringpb.Service_ACTIVE,
		CreateTime:       &now,
		UpdateTime:       &now,
	}

	helper.mockServiceRepo.EXPECT().Get(gomock.Any(), int64(1)).Return(mockModel, nil)

	// 为attribute manager的mock设置期望
	// BasicProcessor 只调用一次 ListByServiceIDs
	helper.mockBasicRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1}).Return([]*model2.ServiceAttribute{}, nil)
	helper.mockRolloverRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1}).Return([]*model2.ServiceAutoRollover{}, nil)
	// 为新的 staff 和 lodging processor 设置 mock 期望
	helper.mockStaffRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1}).Return([]*model2.ServiceStaffScope{}, nil)
	helper.mockLodgingRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1}).Return([]*model2.ServiceLodgingScope{}, nil)
	helper.mockBusinessScopeRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1}).Return([]*model2.ServiceBusinessScope{
		{
			ID:                   1,
			ServiceID:            1,
			IsAllBusiness:        false,
			AvailableBusinessIds: []int64{1},
		},
	}, nil)
	helper.mockAssociationRepo.EXPECT().GetByServiceID(gomock.Any(), int64(1)).Return([]*model2.ServiceAssociation{}, nil)

	// Mock expectations for pet availability
	helper.mockPetScopeRepo.EXPECT().List(gomock.Any(), gomock.Any()).Return(nil, nil)

	result, err := helper.logic.GetService(context.Background(), 1)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, int64(1), result.GetId())
	assert.Equal(t, false, result.GetAvailableBusiness().GetIsAll())
	assert.Equal(t, []int64{1}, result.GetAvailableBusiness().GetBusinessIds())
	// 由于我们使用的是mock repository，Attributes可能为空，这是正常的
}

func TestLogic_GetService_NotFound(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	helper.mockServiceRepo.EXPECT().Get(gomock.Any(), int64(999)).Return(nil, errors.New("service not found"))

	result, err := helper.logic.GetService(context.Background(), 999)
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "service not found")
}

func TestLogic_UpdateService(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	helper.mockServiceRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)

	// 设置 BusinessScope 相关的 mock 期望
	helper.mockBusinessScopeRepo.EXPECT().GetByServiceID(gomock.Any(), int64(1)).Return(nil, nil)
	helper.mockBusinessScopeRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)

	// 为attribute manager的mock设置期望
	helper.mockBasicRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1), gomock.Any()).Return(int64(0), nil).AnyTimes()
	helper.mockRolloverRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1)).Return(int64(0), nil).AnyTimes()
	// 为新的 staff 和 lodging processor 设置删除期望
	helper.mockStaffRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1)).Return(int64(0), nil).AnyTimes()
	helper.mockLodgingRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1)).Return(int64(0), nil).AnyTimes()

	// 创建 protobuf 请求对象
	updateDef := &offeringpb.ServiceUpdateDef{
		Id:          1,
		CategoryId:  lo.ToPtr(int64(2)),
		Name:        lo.ToPtr("Updated Service Template"),
		Description: lo.ToPtr("Updated Description"),
		ColorCode:   lo.ToPtr("#00FF00"),
		Sort:        lo.ToPtr(int64(2)),
		Images:      []string{"updated1.jpg", "updated2.jpg"},
		Status:      lo.ToPtr(offeringpb.Service_INACTIVE),
		AvailableBusiness: &offeringpb.AvailableBusiness{
			IsAll:       true,
			BusinessIds: []int64{},
		},
		Attributes: &offeringpb.ServiceAttributes{},
	}

	err := helper.logic.UpdateService(context.Background(), updateDef)

	assert.NoError(t, err)
	assert.Equal(t, int64(1), updateDef.Id)
	assert.Equal(t, "Updated Service Template", *updateDef.Name)
	assert.Equal(t, "Updated Description", *updateDef.Description)
	assert.Equal(t, "#00FF00", *updateDef.ColorCode)
	assert.Equal(t, int64(2), *updateDef.Sort)
	assert.Equal(t, []string{"updated1.jpg", "updated2.jpg"}, updateDef.Images)
	assert.Equal(t, offeringpb.Service_INACTIVE, *updateDef.Status)
}

func TestLogic_UpdateService_WithAttributes(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	helper.mockServiceRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)

	// 设置属性相关的mock期望
	// 不需要删除旧属性，因为 manager.Save 会处理 upsert 逻辑

	// 保存新属性
	// BasicProcessor 只处理 Duration 属性，所以只需要一次 Create 调用
	helper.mockBasicRepo.EXPECT().ListByServiceID(gomock.Any(), int64(1)).Return([]*model2.ServiceAttribute{}, nil)
	helper.mockBasicRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)
	// AutoRolloverProcessor 会先调用 GetByServiceID 检查是否存在现有记录，然后创建新记录
	helper.mockRolloverRepo.EXPECT().GetByServiceID(gomock.Any(), int64(1)).Return(nil, nil)
	helper.mockRolloverRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)

	// 设置 BusinessScope 相关的 mock 期望
	helper.mockBusinessScopeRepo.EXPECT().GetByServiceID(gomock.Any(), int64(1)).Return(nil, nil)
	helper.mockBusinessScopeRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)

	// 创建 protobuf 请求对象
	updateDef := &offeringpb.ServiceUpdateDef{
		Id:          1,
		CategoryId:  lo.ToPtr(int64(2)),
		Name:        lo.ToPtr("Updated Service Template"),
		Description: lo.ToPtr("Updated Description"),
		ColorCode:   lo.ToPtr("#00FF00"),
		Sort:        lo.ToPtr(int64(2)),
		Images:      []string{"updated1.jpg", "updated2.jpg"},
		Status:      lo.ToPtr(offeringpb.Service_INACTIVE),
		Attributes: &offeringpb.ServiceAttributes{
			Duration: lo.ToPtr(int32(90)),
			AutoRollover: &offeringpb.AutoRollover{
				TargetServiceId: 3,
				AfterMinute:     45,
			},
		},
		AvailableBusiness: &offeringpb.AvailableBusiness{
			IsAll:       true,
			BusinessIds: []int64{},
		},
	}

	err := helper.logic.UpdateService(context.Background(), updateDef)

	assert.NoError(t, err)
}

func TestLogic_UpdateService_Error(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	helper.mockServiceRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(errors.New("update error"))

	// 创建 protobuf 请求对象
	updateDef := &offeringpb.ServiceUpdateDef{
		Id:          1,
		CategoryId:  lo.ToPtr(int64(2)),
		Name:        lo.ToPtr("Updated Service Template"),
		Description: lo.ToPtr("Updated Description"),
		ColorCode:   lo.ToPtr("#00FF00"),
		Sort:        lo.ToPtr(int64(2)),
		Images:      []string{"updated1.jpg", "updated2.jpg"},
		Status:      lo.ToPtr(offeringpb.Service_INACTIVE),
	}

	err := helper.logic.UpdateService(context.Background(), updateDef)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "update error")
}

func TestLogic_UpdateService_AttributeError(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	helper.mockServiceRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)

	// 模拟属性保存失败
	helper.mockBasicRepo.EXPECT().ListByServiceID(gomock.Any(), int64(1)).Return([]*model2.ServiceAttribute{}, nil)
	helper.mockBasicRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(errors.New("attribute save error"))

	// 创建 protobuf 请求对象
	updateDef := &offeringpb.ServiceUpdateDef{
		Id:          1,
		CategoryId:  lo.ToPtr(int64(2)),
		Name:        lo.ToPtr("Updated Service Template"),
		Description: lo.ToPtr("Updated Description"),
		ColorCode:   lo.ToPtr("#00FF00"),
		Sort:        lo.ToPtr(int64(2)),
		Images:      []string{"updated1.jpg", "updated2.jpg"},
		Status:      lo.ToPtr(offeringpb.Service_INACTIVE),
		Attributes: &offeringpb.ServiceAttributes{
			Duration: lo.ToPtr(int32(90)),
			AutoRollover: &offeringpb.AutoRollover{
				TargetServiceId: 3,
				AfterMinute:     45,
			},
		},
		AvailableBusiness: &offeringpb.AvailableBusiness{
			IsAll:       true,
			BusinessIds: []int64{},
		},
	}

	err := helper.logic.UpdateService(context.Background(), updateDef)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "attribute save error")
}

func TestLogic_DeleteService(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 为attribute manager的mock设置期望
	helper.mockBasicRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1), gomock.Any()).Return(int64(0), nil).AnyTimes()
	helper.mockRolloverRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1)).Return(int64(0), nil).AnyTimes()
	// 为新的 staff 和 lodging processor 设置删除期望
	helper.mockStaffRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1)).Return(int64(0), nil).AnyTimes()
	helper.mockLodgingRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1)).Return(int64(0), nil).AnyTimes()
	helper.mockBusinessScopeRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1)).Return(int64(1), nil).AnyTimes()
	helper.mockAssociationRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1)).Return(nil).AnyTimes()

	// Mock expectations for pet availability
	helper.mockPetScopeRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.mockPetScopeRepo)
	helper.mockPetScopeRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1)).Return(nil)

	// 最后删除服务
	helper.mockServiceRepo.EXPECT().Delete(gomock.Any(), int64(1)).Return(nil)

	err := helper.logic.DeleteService(context.Background(), 1)
	assert.NoError(t, err)
}

func TestLogic_DeleteService_ServiceError(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 模拟属性删除成功，但服务删除失败
	helper.mockBasicRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1), gomock.Any()).Return(int64(0), nil).AnyTimes()
	helper.mockRolloverRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1)).Return(int64(0), nil).AnyTimes()
	// 为新的 staff 和 lodging processor 设置删除期望
	helper.mockStaffRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1)).Return(int64(0), nil).AnyTimes()
	helper.mockLodgingRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1)).Return(int64(0), nil).AnyTimes()
	helper.mockBusinessScopeRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1)).Return(int64(1), nil).AnyTimes()
	helper.mockAssociationRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1)).Return(nil).AnyTimes()

	// Mock expectations for pet availability
	helper.mockPetScopeRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.mockPetScopeRepo)
	helper.mockPetScopeRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1)).Return(nil)

	// 最后删除服务（会失败）
	helper.mockServiceRepo.EXPECT().Delete(gomock.Any(), int64(1)).Return(errors.New("service delete error"))

	err := helper.logic.DeleteService(context.Background(), 1)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "service delete error")
}

func TestLogic_DeleteService_AttributeError(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 模拟属性删除失败
	helper.mockBasicRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1), gomock.Any()).Return(int64(0), errors.New("attribute deletion error"))
	helper.mockRolloverRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1)).Return(int64(0), nil).AnyTimes()
	// 为新的 staff 和 lodging processor 设置删除期望
	helper.mockStaffRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1)).Return(int64(0), nil).AnyTimes()
	helper.mockLodgingRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1)).Return(int64(0), nil).AnyTimes()

	err := helper.logic.DeleteService(context.Background(), 1)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "attribute deletion error")
}

// Helper function to create string pointer
func stringPtr(s string) *string {
	return &s
}

// TestLogic_ListServices_Basic 测试基本的服务列表查询
func TestLogic_ListServices_Basic(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 构造请求
	req := &offeringpb.ListServicesRequest{
		OrganizationId:   123,
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		Pagination: &offeringpb.PaginationRef{
			Offset: 0,
			Limit:  10,
		},
	}

	// 模拟服务列表
	now := time.Now()
	mockServices := []*model2.Service{
		{
			ID:               1,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   123,
			CareTypeID:       1,
			CategoryID:       2,
			Name:             "Service 1",
			Description:      stringPtr("Description 1"),
			ColorCode:        "#FF0000",
			Sort:             1,
			Images:           []string{"image1.jpg"},
			Source:           offeringpb.OfferingSource_MOEGO,
			Status:           offeringpb.Service_ACTIVE,
			CreateTime:       &now,
			UpdateTime:       &now,
		},
		{
			ID:               2,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   123,
			CareTypeID:       1,
			CategoryID:       3,
			Name:             "Service 2",
			Description:      stringPtr("Description 2"),
			ColorCode:        "#00FF00",
			Sort:             2,
			Images:           []string{"image2.jpg"},
			Source:           offeringpb.OfferingSource_MOEGO,
			Status:           offeringpb.Service_ACTIVE,
			CreateTime:       &now,
			UpdateTime:       &now,
		},
	}

	// 设置mock期望
	pagination := &offeringpb.PaginationRef{
		Offset: 0,
		Limit:  10,
	}
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), pagination).Return(mockServices, int64(2), nil)

	// 使用辅助函数设置属性相关的mock期望
	helper.setupListServicesAttributeMocks([]int64{1, 2})

	result, err := helper.logic.ListServices(context.Background(), req)

	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, int32(2), result.Total)
	assert.Len(t, result.Services, 2)
	assert.Equal(t, "Service 1", result.Services[0].Service.Name)
	assert.Equal(t, "Service 2", result.Services[1].Service.Name)
	assert.Equal(t, req.Pagination, result.Pagination)
}

// TestLogic_ListServices_WithBusinessFilter 测试使用business_id过滤的服务列表查询
func TestLogic_ListServices_WithBusinessFilter(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 构造请求，使用business_id过滤
	req := &offeringpb.ListServicesRequest{
		OrganizationId:   456,
		OrganizationType: organizationpb.OrganizationType_BUSINESS,
		Pagination: &offeringpb.PaginationRef{
			Offset: 0,
			Limit:  10,
		},
	}

	// 模拟服务列表
	now := time.Now()
	mockServices := []*model2.Service{
		{
			ID:               1,
			OrganizationType: organizationpb.OrganizationType_BUSINESS,
			OrganizationID:   456,
			CareTypeID:       1,
			CategoryID:       2,
			Name:             "Business Service",
			Description:      stringPtr("Business Description"),
			ColorCode:        "#FF0000",
			Sort:             1,
			Images:           []string{"image1.jpg"},
			Source:           offeringpb.OfferingSource_MOEGO,
			Status:           offeringpb.Service_ACTIVE,
			CreateTime:       &now,
			UpdateTime:       &now,
		},
	}

	// 设置mock期望
	pagination := &offeringpb.PaginationRef{
		Offset: 0,
		Limit:  10,
	}
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), pagination).Return(mockServices, int64(1), nil)
	// 使用辅助函数设置属性相关的mock期望
	helper.setupListServicesAttributeMocks([]int64{1})

	result, err := helper.logic.ListServices(context.Background(), req)

	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, int32(1), result.Total)
	assert.Len(t, result.Services, 1)
	assert.Equal(t, "Business Service", result.Services[0].Service.Name)
	assert.Equal(t, organizationpb.OrganizationType_BUSINESS, result.Services[0].Service.OrganizationType)
	assert.Equal(t, int64(456), result.Services[0].Service.OrganizationId)
}

// TestLogic_ListServices_WithAllFilters 测试使用所有过滤条件的服务列表查询
func TestLogic_ListServices_WithAllFilters(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 构造请求，使用所有过滤条件
	req := &offeringpb.ListServicesRequest{
		OrganizationId:   123,
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		Filter: &offeringpb.ListServicesRequest_Filter{
			CategoryIds: []int64{2, 3},
			CareTypeIds: []int64{1, 2},
			Statuses:    []offeringpb.Service_Status{offeringpb.Service_ACTIVE},
		},
		Pagination: &offeringpb.PaginationRef{
			Offset: 10,
			Limit:  20,
		},
	}

	// 模拟服务列表
	now := time.Now()
	mockServices := []*model2.Service{
		{
			ID:               1,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   123,
			CareTypeID:       1,
			CategoryID:       2,
			Name:             "Filtered Service",
			Description:      stringPtr("Filtered Description"),
			ColorCode:        "#FF0000",
			Sort:             1,
			Images:           []string{"image1.jpg"},
			Source:           offeringpb.OfferingSource_MOEGO,
			Status:           offeringpb.Service_ACTIVE,
			CreateTime:       &now,
			UpdateTime:       &now,
		},
	}

	// 设置mock期望
	pagination := &offeringpb.PaginationRef{
		Offset: 10,
		Limit:  20,
	}
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), pagination).Return(mockServices, int64(1), nil)
	// 使用辅助函数设置属性相关的mock期望
	helper.setupListServicesAttributeMocks([]int64{1})

	result, err := helper.logic.ListServices(context.Background(), req)

	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, int32(1), result.Total)
	assert.Len(t, result.Services, 1)
	assert.Equal(t, "Filtered Service", result.Services[0].Service.Name)
	assert.Equal(t, req.Pagination, result.Pagination)
}

// TestLogic_ListServices_WithOnlineBookingSettings 测试包含在线预约设置的服务列表查询
func TestLogic_ListServices_WithOnlineBookingSettings(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 构造请求，包含在线预约设置
	includeOBSetting := true
	req := &offeringpb.ListServicesRequest{
		OrganizationId:   123,
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		Pagination: &offeringpb.PaginationRef{
			Offset: 0,
			Limit:  10,
		},
		ExtraInfoOptions: &offeringpb.ExtraInfoOptions{
			IncludeObSetting: &includeOBSetting,
		},
	}

	// 模拟服务列表
	now := time.Now()
	mockServices := []*model2.Service{
		{
			ID:               1,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   123,
			CareTypeID:       1,
			CategoryID:       2,
			Name:             "Service with OB Setting",
			Description:      stringPtr("Description"),
			ColorCode:        "#FF0000",
			Sort:             1,
			Images:           []string{"image1.jpg"},
			Source:           offeringpb.OfferingSource_MOEGO,
			Status:           offeringpb.Service_ACTIVE,
			CreateTime:       &now,
			UpdateTime:       &now,
		},
	}

	// 设置mock期望
	pagination := &offeringpb.PaginationRef{
		Offset: 0,
		Limit:  10,
	}
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), pagination).Return(mockServices, int64(1), nil)
	// 使用辅助函数设置属性相关的mock期望
	helper.setupListServicesAttributeMocks([]int64{1})
	helper.mockOBSettingRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1}).Return([]*model2.ServiceObSetting{
		{
			ID:               1,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   123,
			ServiceID:        1,
			IsAvailable:      true,
			ShowBasePrice:    2, // SHOW_BASE_PRICE_MODE_SHOW_FIXED_PRICE
			IsAllStaff:       true,
			Alias_:           "OB Alias",
			CreatedAt:        &now,
			UpdatedAt:        &now,
		},
	}, nil)

	result, err := helper.logic.ListServices(context.Background(), req)

	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, int32(1), result.Total)
	assert.Len(t, result.Services, 1)
	assert.NotNil(t, result.Services[0].ObSetting)
	assert.Equal(t, "OB Alias", result.Services[0].ObSetting.Alias)
	assert.True(t, result.Services[0].ObSetting.IsAvailable)
}

// TestLogic_ListServices_EmptyResult 测试空结果的服务列表查询
func TestLogic_ListServices_EmptyResult(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 构造请求
	req := &offeringpb.ListServicesRequest{
		OrganizationId:   999,
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		Pagination: &offeringpb.PaginationRef{
			Offset: 0,
			Limit:  10,
		},
	}

	// 设置mock期望，返回空结果
	pagination := &offeringpb.PaginationRef{
		Offset: 0,
		Limit:  10,
	}
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), pagination).Return([]*model2.Service{}, int64(0), nil)

	// 使用辅助函数设置属性相关的mock期望（空服务列表）
	helper.setupListServicesAttributeMocks([]int64{})

	result, err := helper.logic.ListServices(context.Background(), req)

	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, int32(0), result.Total)
	assert.Len(t, result.Services, 0)
}

// TestLogic_ListServices_DefaultPagination 测试使用默认分页参数的服务列表查询
func TestLogic_ListServices_DefaultPagination(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 构造请求，不包含分页信息
	req := &offeringpb.ListServicesRequest{
		OrganizationId:   123,
		OrganizationType: organizationpb.OrganizationType_COMPANY,
	}

	// 设置mock期望，使用默认分页参数
	pagination := &offeringpb.PaginationRef{
		Offset: 0,
		Limit:  200,
	}
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), pagination).Return([]*model2.Service{}, int64(0), nil)

	// 使用辅助函数设置属性相关的mock期望（空服务列表）
	helper.setupListServicesAttributeMocks([]int64{})

	result, err := helper.logic.ListServices(context.Background(), req)

	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, int32(0), result.Total)
	assert.Len(t, result.Services, 0)
	assert.Nil(t, result.Pagination) // 当请求中没有分页信息时，响应中也不应该有
}

// TestLogic_ListServices_RepositoryError 测试数据库错误的情况
func TestLogic_ListServices_RepositoryError(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 构造请求
	req := &offeringpb.ListServicesRequest{
		OrganizationId:   123,
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		Pagination: &offeringpb.PaginationRef{
			Offset: 0,
			Limit:  10,
		},
	}

	// 设置mock期望，模拟数据库错误
	pagination := &offeringpb.PaginationRef{
		Offset: 0,
		Limit:  10,
	}
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), pagination).Return(nil, int64(0), errors.New("database error"))

	result, err := helper.logic.ListServices(context.Background(), req)

	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "database error")
}

// TestLogic_ListServices_MultipleServicesWithAttributes 测试多个服务带属性的情况
func TestLogic_ListServices_MultipleServicesWithAttributes(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 构造请求
	req := &offeringpb.ListServicesRequest{
		OrganizationId:   123,
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		Pagination: &offeringpb.PaginationRef{
			Offset: 0,
			Limit:  10,
		},
	}

	// 模拟多个服务
	now := time.Now()
	mockServices := []*model2.Service{
		{
			ID:               1,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   123,
			CareTypeID:       1,
			CategoryID:       2,
			Name:             "Service 1",
			Description:      stringPtr("Description 1"),
			ColorCode:        "#FF0000",
			Sort:             1,
			Images:           []string{"image1.jpg"},
			Source:           offeringpb.OfferingSource_MOEGO,
			Status:           offeringpb.Service_ACTIVE,
			CreateTime:       &now,
			UpdateTime:       &now,
		},
		{
			ID:               2,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   123,
			CareTypeID:       1,
			CategoryID:       3,
			Name:             "Service 2",
			Description:      stringPtr("Description 2"),
			ColorCode:        "#FF0000",
			Sort:             2,
			Images:           []string{"image2.jpg"},
			Source:           offeringpb.OfferingSource_MOEGO,
			Status:           offeringpb.Service_ACTIVE,
			CreateTime:       &now,
			UpdateTime:       &now,
		},
		{
			ID:               3,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   123,
			CareTypeID:       2,
			CategoryID:       4,
			Name:             "Service 3",
			Description:      stringPtr("Description 3"),
			ColorCode:        "#FF0000",
			Sort:             3,
			Images:           []string{"image3.jpg"},
			Source:           offeringpb.OfferingSource_MOEGO,
			Status:           offeringpb.Service_INACTIVE,
			CreateTime:       &now,
			UpdateTime:       &now,
		},
	}

	// 设置mock期望
	pagination := &offeringpb.PaginationRef{
		Offset: 0,
		Limit:  10,
	}
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), pagination).Return(mockServices, int64(3), nil)
	// 使用辅助函数设置属性相关的mock期望
	helper.setupListServicesAttributeMocks([]int64{1, 2, 3})

	result, err := helper.logic.ListServices(context.Background(), req)

	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, int32(3), result.Total)
	assert.Len(t, result.Services, 3)

	// 验证第一个服务有属性
	assert.Equal(t, "Service 1", result.Services[0].Service.Name)
	assert.NotNil(t, result.Services[0].Service.Attributes)

	// 验证第二个服务有属性
	assert.Equal(t, "Service 2", result.Services[1].Service.Name)
	assert.NotNil(t, result.Services[1].Service.Attributes)

	// 验证第三个服务有属性（即使是空的）
	assert.Equal(t, "Service 3", result.Services[2].Service.Name)
	assert.NotNil(t, result.Services[2].Service.Attributes)
}

// TestLogic_ListServices_WithNullAttributeValues 测试包含空属性值的情况
func TestLogic_ListServices_WithNullAttributeValues(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 构造请求
	req := &offeringpb.ListServicesRequest{
		OrganizationId:   123,
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		Pagination: &offeringpb.PaginationRef{
			Offset: 0,
			Limit:  10,
		},
	}

	// 模拟服务列表
	now := time.Now()
	mockServices := []*model2.Service{
		{
			ID:               1,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   123,
			CareTypeID:       1,
			CategoryID:       2,
			Name:             "Service with Null Attributes",
			Description:      stringPtr("Description"),
			ColorCode:        "#FF0000",
			Sort:             1,
			Images:           []string{"image1.jpg"},
			Source:           offeringpb.OfferingSource_MOEGO,
			Status:           offeringpb.Service_ACTIVE,
			CreateTime:       &now,
			UpdateTime:       &now,
		},
	}

	// 设置mock期望
	pagination := &offeringpb.PaginationRef{
		Offset: 0,
		Limit:  10,
	}
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), pagination).Return(mockServices, int64(1), nil)
	// 使用辅助函数设置属性相关的mock期望
	helper.setupListServicesAttributeMocks([]int64{1})

	result, err := helper.logic.ListServices(context.Background(), req)

	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, int32(1), result.Total)
	assert.Len(t, result.Services, 1)
	assert.Equal(t, "Service with Null Attributes", result.Services[0].Service.Name)
	assert.NotNil(t, result.Services[0].Service.Attributes)
}

// TestLogic_ListServices_StringToValue_EdgeCases 测试stringToValue函数的边界情况
func TestLogic_ListServices_StringToValue_EdgeCases(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 构造请求
	req := &offeringpb.ListServicesRequest{
		OrganizationId:   123,
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		Pagination: &offeringpb.PaginationRef{
			Offset: 0,
			Limit:  10,
		},
	}

	// 模拟服务列表
	now := time.Now()
	mockServices := []*model2.Service{
		{
			ID:               1,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   123,
			CareTypeID:       1,
			CategoryID:       2,
			Name:             "Service with Edge Cases",
			Description:      stringPtr("Description"),
			ColorCode:        "#FF0000",
			Sort:             1,
			Images:           []string{"image1.jpg"},
			Source:           offeringpb.OfferingSource_MOEGO,
			Status:           offeringpb.Service_ACTIVE,
			CreateTime:       &now,
			UpdateTime:       &now,
		},
	}

	// 设置mock期望
	pagination := &offeringpb.PaginationRef{
		Offset: 0,
		Limit:  10,
	}
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), pagination).Return(mockServices, int64(1), nil)
	// 使用辅助函数设置属性相关的mock期望
	helper.setupListServicesAttributeMocks([]int64{1})

	result, err := helper.logic.ListServices(context.Background(), req)

	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, int32(1), result.Total)
	assert.Len(t, result.Services, 1)
	assert.Equal(t, "Service with Edge Cases", result.Services[0].Service.Name)
	assert.NotNil(t, result.Services[0].Service.Attributes)
}

// TestLogic_DeleteService_Error 测试DeleteService失败的情况
func TestLogic_DeleteService_Error(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	helper.mockBasicRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1), gomock.Any()).Return(int64(0), errors.New("attribute deletion error"))
	helper.mockRolloverRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1)).Return(int64(0), nil).AnyTimes()
	// 为新的 staff 和 lodging processor 设置删除期望
	helper.mockStaffRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1)).Return(int64(0), nil).AnyTimes()
	helper.mockLodgingRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1)).Return(int64(0), nil).AnyTimes()

	err := helper.logic.DeleteService(context.Background(), 1)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "attribute deletion error")
}

// TestLogic_ListServices_OnlineBookingSettingError 测试在线预约设置加载错误的情况
func TestLogic_ListServices_OnlineBookingSettingError(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 构造请求，包含在线预约设置
	includeOBSetting := true
	req := &offeringpb.ListServicesRequest{
		OrganizationId:   123,
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		Pagination: &offeringpb.PaginationRef{
			Offset: 0,
			Limit:  10,
		},
		ExtraInfoOptions: &offeringpb.ExtraInfoOptions{
			IncludeObSetting: &includeOBSetting,
		},
	}

	// 模拟服务列表
	now := time.Now()
	mockServices := []*model2.Service{
		{
			ID:               1,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   123,
			CareTypeID:       1,
			CategoryID:       2,
			Name:             "Service with OB Setting Error",
			Description:      stringPtr("Description"),
			ColorCode:        "#FF0000",
			Sort:             1,
			Images:           []string{"image1.jpg"},
			Source:           offeringpb.OfferingSource_MOEGO,
			Status:           offeringpb.Service_ACTIVE,
			CreateTime:       &now,
			UpdateTime:       &now,
		},
	}

	// 设置mock期望
	pagination := &offeringpb.PaginationRef{
		Offset: 0,
		Limit:  10,
	}
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), pagination).Return(mockServices, int64(1), nil)
	// 使用辅助函数设置属性相关的mock期望
	helper.setupListServicesAttributeMocks([]int64{1})
	helper.mockOBSettingRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1}).Return(nil, errors.New("ob setting loading error"))

	result, err := helper.logic.ListServices(context.Background(), req)

	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "ob setting loading error")
}

// TestLogic_ListServices_EmptyServiceIDs 测试空服务ID列表的情况
func TestLogic_ListServices_EmptyServiceIDs(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 构造请求，包含在线预约设置但服务列表为空
	includeOBSetting := true
	req := &offeringpb.ListServicesRequest{
		OrganizationId:   123,
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		Pagination: &offeringpb.PaginationRef{
			Offset: 0,
			Limit:  10,
		},
		ExtraInfoOptions: &offeringpb.ExtraInfoOptions{
			IncludeObSetting: &includeOBSetting,
		},
	}

	// 设置mock期望，返回空服务列表
	pagination := &offeringpb.PaginationRef{
		Offset: 0,
		Limit:  10,
	}
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), pagination).Return([]*model2.Service{}, int64(0), nil)

	// 使用辅助函数设置属性相关的mock期望（空服务列表）
	helper.setupListServicesAttributeMocks([]int64{})

	result, err := helper.logic.ListServices(context.Background(), req)

	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, int32(0), result.Total)
	assert.Len(t, result.Services, 0)
}

// TestLogic_ListServices_InvalidAttributeValue 测试无效属性值的情况
func TestLogic_ListServices_InvalidAttributeValue(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 构造请求
	req := &offeringpb.ListServicesRequest{
		OrganizationId:   123,
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		Pagination: &offeringpb.PaginationRef{
			Offset: 0,
			Limit:  10,
		},
	}

	// 模拟服务列表
	now := time.Now()
	mockServices := []*model2.Service{
		{
			ID:               1,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   123,
			CareTypeID:       1,
			CategoryID:       2,
			Name:             "Service with Invalid Attribute",
			Description:      stringPtr("Description"),
			ColorCode:        "#FF0000",
			Sort:             1,
			Images:           []string{"image1.jpg"},
			Source:           offeringpb.OfferingSource_MOEGO,
			Status:           offeringpb.Service_ACTIVE,
			CreateTime:       &now,
			UpdateTime:       &now,
		},
	}

	// 设置mock期望
	pagination := &offeringpb.PaginationRef{
		Offset: 0,
		Limit:  10,
	}
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), pagination).Return(mockServices, int64(1), nil)
	// 使用辅助函数设置属性相关的mock期望
	helper.setupListServicesAttributeMocks([]int64{1})

	result, err := helper.logic.ListServices(context.Background(), req)

	// 即使有无效属性值，也应该成功返回，只是跳过无效属性
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, int32(1), result.Total)
	assert.Len(t, result.Services, 1)
	assert.Equal(t, "Service with Invalid Attribute", result.Services[0].Service.Name)
}

func TestLogic_CreateService_WithSort(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 新增：CreateService 前会进行名称唯一性校验，需期望调用 List
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*model2.Service{}, int64(0), nil)

	// 准备测试数据
	createDef := &offeringpb.ServiceCreateDef{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   1,
		CategoryId:       lo.ToPtr(int64(10)),
		Name:             "Test Service",
		ColorCode:        "#FF0000",
		Status:           offeringpb.Service_ACTIVE,
		AvailableBusiness: &offeringpb.AvailableBusiness{
			IsAll:       true,
			BusinessIds: []int64{},
		},
	}

	// 设置期望 - 创建服务
	helper.mockServiceRepo.EXPECT().Create(gomock.Any(), gomock.Any()).DoAndReturn(
		func(ctx context.Context, service *model2.Service) error {
			// 设置 ID，模拟数据库自增
			service.ID = 123
			return nil
		})

	// 设置期望 - 更新 sort 为 ID
	helper.mockServiceRepo.EXPECT().Update(gomock.Any(), gomock.Any()).DoAndReturn(
		func(ctx context.Context, service *model2.Service) error {
			assert.Equal(t, int64(123), service.Sort) // 验证 sort 被设置为 ID
			return nil
		})

	// 设置期望 - BusinessScope 的 upsert 逻辑
	// 先尝试查询现有记录（返回 not found，表示需要创建新记录）
	helper.mockBusinessScopeRepo.EXPECT().GetByServiceID(gomock.Any(), int64(123)).Return(nil, nil)
	// 创建新记录
	helper.mockBusinessScopeRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)

	// 执行测试
	serviceID, err := helper.logic.CreateService(context.Background(), createDef)

	// 验证结果
	assert.NoError(t, err)
	assert.Greater(t, serviceID, int64(0))
}

func TestLogic_CreateService_FirstServiceInCategory(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 新增：CreateService 前会进行名称唯一性校验，需期望调用 List
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*model2.Service{}, int64(0), nil)

	// 准备测试数据
	createDef := &offeringpb.ServiceCreateDef{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   1,
		CategoryId:       lo.ToPtr(int64(10)),
		Name:             "First Service",
		ColorCode:        "#FF0000",
		Status:           offeringpb.Service_ACTIVE,
		AvailableBusiness: &offeringpb.AvailableBusiness{
			IsAll:       true,
			BusinessIds: []int64{},
		},
	}

	// 设置期望 - 创建服务
	helper.mockServiceRepo.EXPECT().Create(gomock.Any(), gomock.Any()).DoAndReturn(
		func(ctx context.Context, service *model2.Service) error {
			// 设置 ID，模拟数据库自增
			service.ID = 456
			return nil
		})

	// 设置期望 - 更新 sort 为 ID
	helper.mockServiceRepo.EXPECT().Update(gomock.Any(), gomock.Any()).DoAndReturn(
		func(ctx context.Context, service *model2.Service) error {
			assert.Equal(t, int64(456), service.Sort) // 验证 sort 被设置为 ID
			return nil
		})

	// 设置期望 - BusinessScope 的 upsert 逻辑
	// 先尝试查询现有记录（返回 not found，表示需要创建新记录）
	helper.mockBusinessScopeRepo.EXPECT().GetByServiceID(gomock.Any(), int64(456)).Return(nil, nil)
	// 创建新记录
	helper.mockBusinessScopeRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)

	// 执行测试
	serviceID, err := helper.logic.CreateService(context.Background(), createDef)

	// 验证结果
	assert.NoError(t, err)
	assert.Greater(t, serviceID, int64(0))
}

// ==================== ListAvailableServices 测试 ====================

func TestLogic_ListAvailableServices_Basic(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 准备测试数据
	req := &offeringpb.ListAvailableServicesRequest{
		OrganizationType:          organizationpb.OrganizationType_COMPANY,
		OrganizationId:            1,
		BusinessId:                100,
		EnableBusinessScopeFilter: false,
		Pagination: &offeringpb.PaginationRef{
			Offset: 0,
			Limit:  10,
		},
	}

	// 模拟数据库返回的服务列表
	mockServices := []*model2.Service{
		{
			ID:               1,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   1,
			Name:             "Test Service",
			Status:           offeringpb.Service_ACTIVE,
			Sort:             1,
		},
	}

	// 设置 mock 期望
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), nil).Return(mockServices, int64(1), nil)
	helper.setupListServicesAttributeMocks([]int64{1})

	// 执行测试
	resp, err := helper.logic.ListAvailableServices(context.Background(), req)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Len(t, resp.Services, 1)
	assert.Equal(t, int32(1), resp.Total)
	assert.Equal(t, req.Pagination, resp.Pagination)
	assert.Equal(t, int64(1), resp.Services[0].Id)
}

func TestLogic_ListAvailableServices_RepositoryError(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 准备测试数据
	req := &offeringpb.ListAvailableServicesRequest{
		OrganizationType:          organizationpb.OrganizationType_COMPANY,
		OrganizationId:            1,
		BusinessId:                100,
		EnableBusinessScopeFilter: false,
	}

	// 模拟 repository 错误
	expectedErr := errors.New("database connection failed")
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), nil).Return(nil, int64(0), expectedErr)

	// 执行测试
	resp, err := helper.logic.ListAvailableServices(context.Background(), req)

	// 验证结果
	assert.Error(t, err)
	assert.Equal(t, expectedErr, err)
	assert.Nil(t, resp)
}

func TestLogic_ListAvailableServices_WithoutBusinessScopeFilter(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 准备测试数据
	req := &offeringpb.ListAvailableServicesRequest{
		OrganizationType:          organizationpb.OrganizationType_COMPANY,
		OrganizationId:            1,
		BusinessId:                100,
		EnableBusinessScopeFilter: false,
		Filter: &offeringpb.ListAvailableServicesRequest_Filter{
			CareTypeIds: []int64{1},
		},
		Pagination: &offeringpb.PaginationRef{
			Offset: 0,
			Limit:  5,
		},
	}

	// 模拟数据库返回的服务列表
	mockServices := []*model2.Service{
		{
			ID:               1,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   1,
			Name:             "Test Service",
			Status:           offeringpb.Service_ACTIVE,
			Sort:             1,
		},
	}

	// 设置 mock 期望 - 不调用 business scope 相关方法
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), nil).Return(mockServices, int64(1), nil)
	helper.setupListServicesAttributeMocks([]int64{1})

	// 执行测试
	resp, err := helper.logic.ListAvailableServices(context.Background(), req)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Len(t, resp.Services, 1)
	assert.Equal(t, int32(1), resp.Total)
}

func TestLogic_ListAvailableServices_BusinessScopeFilter_AllBusiness(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 准备测试数据
	req := &offeringpb.ListAvailableServicesRequest{
		OrganizationType:          organizationpb.OrganizationType_COMPANY,
		OrganizationId:            1,
		BusinessId:                100,
		EnableBusinessScopeFilter: true,
		Pagination: &offeringpb.PaginationRef{
			Offset: 0,
			Limit:  10,
		},
	}

	// 模拟数据库返回的服务列表
	mockServices := []*model2.Service{
		{
			ID:               1,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   1,
			Name:             "Service for All Business",
			Status:           offeringpb.Service_ACTIVE,
			Sort:             1,
		},
	}

	// 模拟 business scope 数据 - 适用于所有 business
	mockScopes := []*model2.ServiceBusinessScope{
		{
			ServiceID:            1,
			IsAllBusiness:        true,
			AvailableBusinessIds: []int64{},
		},
	}

	// 设置 mock 期望
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), nil).Return(mockServices, int64(1), nil)
	helper.mockBusinessScopeRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1}).Return(mockScopes, nil)
	helper.setupListServicesAttributeMocks([]int64{1})

	// 执行测试
	resp, err := helper.logic.ListAvailableServices(context.Background(), req)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Len(t, resp.Services, 1)
	assert.Equal(t, int64(1), resp.Services[0].Id)
}

func TestLogic_ListAvailableServices_BusinessScopeFilter_SpecificBusiness(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 准备测试数据
	req := &offeringpb.ListAvailableServicesRequest{
		OrganizationType:          organizationpb.OrganizationType_COMPANY,
		OrganizationId:            1,
		BusinessId:                100,
		EnableBusinessScopeFilter: true,
		Pagination: &offeringpb.PaginationRef{
			Offset: 0,
			Limit:  10,
		},
	}

	// 模拟数据库返回的服务列表
	mockServices := []*model2.Service{
		{
			ID:               1,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   1,
			Name:             "Service for Business 100",
			Status:           offeringpb.Service_ACTIVE,
			Sort:             1,
		},
		{
			ID:               2,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   1,
			Name:             "Service for Business 200",
			Status:           offeringpb.Service_ACTIVE,
			Sort:             2,
		},
		{
			ID:               3,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   1,
			Name:             "Service for Business 300",
			Status:           offeringpb.Service_ACTIVE,
			Sort:             3,
		},
	}

	// 模拟 business scope 数据
	mockScopes := []*model2.ServiceBusinessScope{
		{
			ServiceID:            1,
			IsAllBusiness:        false,
			AvailableBusinessIds: []int64{100, 150}, // 包含目标 business 100
		},
		{
			ServiceID:            2,
			IsAllBusiness:        false,
			AvailableBusinessIds: []int64{200, 250}, // 不包含目标 business 100
		},
		{
			ServiceID:            3,
			IsAllBusiness:        false,
			AvailableBusinessIds: []int64{300, 350}, // 不包含目标 business 100
		},
	}

	// 设置 mock 期望
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), nil).Return(mockServices, int64(1), nil)
	helper.mockBusinessScopeRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1, 2, 3}).Return(mockScopes, nil)

	// 注意：loadServicesWithAttributes 是在 business scope 过滤之前调用的，所以需要处理原始的 3 个 services
	helper.setupListServicesAttributeMocks([]int64{1})

	// 执行测试
	resp, err := helper.logic.ListAvailableServices(context.Background(), req)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	// 只有 service 1 应该通过过滤（business 100 在它的 business_ids 中）
	assert.Len(t, resp.Services, 1)
	assert.Equal(t, int64(1), resp.Services[0].Id)
	assert.Equal(t, int32(1), resp.Total)
}

func TestLogic_ListAvailableServices_BusinessScopeFilter_NoScopeConfig(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 准备测试数据
	req := &offeringpb.ListAvailableServicesRequest{
		OrganizationType:          organizationpb.OrganizationType_COMPANY,
		OrganizationId:            1,
		BusinessId:                100,
		EnableBusinessScopeFilter: true,
		Pagination: &offeringpb.PaginationRef{
			Offset: 0,
			Limit:  10,
		},
	}

	// 模拟数据库返回的服务列表
	mockServices := []*model2.Service{
		{
			ID:               1,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   1,
			Name:             "Service without scope config",
			Status:           offeringpb.Service_ACTIVE,
			Sort:             1,
		},
	}

	// 模拟 business scope 数据 - 没有配置 scope
	mockScopes := []*model2.ServiceBusinessScope{}

	// 设置 mock 期望
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), nil).Return(mockServices, int64(1), nil)
	helper.mockBusinessScopeRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1}).Return(mockScopes, nil)
	helper.setupListServicesAttributeMocks([]int64{1})

	// 执行测试
	resp, err := helper.logic.ListAvailableServices(context.Background(), req)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	// 没有 scope 配置的服务应该默认通过过滤
	assert.Len(t, resp.Services, 1)
	assert.Equal(t, int64(1), resp.Services[0].Id)
}

func TestLogic_ListAvailableServices_WithPagination(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 准备测试数据
	req := &offeringpb.ListAvailableServicesRequest{
		OrganizationType:          organizationpb.OrganizationType_COMPANY,
		OrganizationId:            1,
		BusinessId:                100,
		EnableBusinessScopeFilter: false,
		Pagination: &offeringpb.PaginationRef{
			Offset: 5,
			Limit:  3,
		},
	}

	// 模拟数据库返回的服务列表（超过分页限制）
	mockServices := []*model2.Service{
		{ID: 1, Sort: 1},
		{ID: 2, Sort: 2},
		{ID: 3, Sort: 3},
		{ID: 4, Sort: 4},
		{ID: 5, Sort: 5},
		{ID: 6, Sort: 6},
		{ID: 7, Sort: 7},
		{ID: 8, Sort: 8},
	}

	// 设置 mock 期望
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), nil).Return(mockServices, int64(1), nil)
	// 注意：loadServicesWithAttributes 是在分页之后调用的，所以需要处理分页后的 3 个
	helper.setupListServicesAttributeMocks([]int64{6, 7, 8})

	// 执行测试
	resp, err := helper.logic.ListAvailableServices(context.Background(), req)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	// 分页后应该只返回 3 个服务（offset=5, limit=3）
	assert.Len(t, resp.Services, 3)
	assert.Equal(t, int32(3), resp.Total)
	assert.Equal(t, req.Pagination, resp.Pagination)

	// 验证分页后的服务 ID（按 sort 排序后，offset=5 开始取 3 个）
	assert.Equal(t, int64(6), resp.Services[0].Id)
	assert.Equal(t, int64(7), resp.Services[1].Id)
	assert.Equal(t, int64(8), resp.Services[2].Id)
}

func TestLogic_ListAvailableServices_BusinessScopeError(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 准备测试数据
	req := &offeringpb.ListAvailableServicesRequest{
		OrganizationType:          organizationpb.OrganizationType_COMPANY,
		OrganizationId:            1,
		BusinessId:                100,
		EnableBusinessScopeFilter: true,
	}

	// 模拟数据库返回的服务列表
	mockServices := []*model2.Service{
		{
			ID:               1,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   1,
			Name:             "Test Service",
			Status:           offeringpb.Service_ACTIVE,
			Sort:             1,
		},
	}

	// 模拟 business scope 查询错误
	expectedErr := errors.New("business scope query failed")
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), nil).Return(mockServices, int64(1), nil)
	helper.mockBusinessScopeRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1}).Return(nil, expectedErr)

	// 执行测试
	resp, err := helper.logic.ListAvailableServices(context.Background(), req)

	// 验证结果
	assert.Error(t, err)
	assert.Equal(t, expectedErr, err)
	assert.Nil(t, resp)
}

func TestLogic_ListAvailableServices_AttributeError(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 准备测试数据
	req := &offeringpb.ListAvailableServicesRequest{
		OrganizationType:          organizationpb.OrganizationType_COMPANY,
		OrganizationId:            1,
		BusinessId:                100,
		EnableBusinessScopeFilter: false,
	}

	// 模拟数据库返回的服务列表
	mockServices := []*model2.Service{
		{
			ID:               1,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   1,
			Name:             "Test Service",
			Status:           offeringpb.Service_ACTIVE,
			Sort:             1,
		},
	}

	// 模拟属性查询错误
	expectedErr := errors.New("attribute query failed")
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), nil).Return(mockServices, int64(1), nil)

	// 设置属性 mock 返回错误 - 需要为所有 processor 设置 mock 期望
	// 由于 errgroup 的实现，任何一个 processor 失败都会导致整个操作失败
	helper.mockBasicRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1}).Return(nil, expectedErr)
	helper.mockRolloverRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1}).Return(nil, expectedErr)
	helper.mockStaffRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1}).Return(nil, expectedErr)
	helper.mockLodgingRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1}).Return(nil, expectedErr)

	// 执行测试
	resp, err := helper.logic.ListAvailableServices(context.Background(), req)

	// 验证结果
	assert.Error(t, err)
	// 由于 errgroup 的实现，错误消息会被包装，格式为 "failed to list attributes with processor %T: %w"
	assert.Contains(t, err.Error(), "attribute query failed")
	assert.Nil(t, resp)
}

func TestLogic_ListAvailableServices_EmptyResult(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 准备测试数据
	req := &offeringpb.ListAvailableServicesRequest{
		OrganizationType:          organizationpb.OrganizationType_COMPANY,
		OrganizationId:            1,
		BusinessId:                100,
		EnableBusinessScopeFilter: false,
	}

	// 模拟数据库返回空列表
	mockServices := []*model2.Service{}

	// 设置 mock 期望
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), nil).Return(mockServices, int64(1), nil)
	helper.setupListServicesAttributeMocks([]int64{})

	// 执行测试
	resp, err := helper.logic.ListAvailableServices(context.Background(), req)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Len(t, resp.Services, 0)
	assert.Equal(t, int32(0), resp.Total)
}

func TestLogic_ListAvailableServices_WithKeywordFilter(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 准备测试数据
	req := &offeringpb.ListAvailableServicesRequest{
		OrganizationType:          organizationpb.OrganizationType_COMPANY,
		OrganizationId:            1,
		BusinessId:                100,
		EnableBusinessScopeFilter: false,
		Filter: &offeringpb.ListAvailableServicesRequest_Filter{
			Keyword: lo.ToPtr("Spa"),
		},
	}

	// 模拟数据库返回的服务列表
	mockServices := []*model2.Service{
		{
			ID:               1,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   1,
			Name:             "Spa Treatment",
			Status:           offeringpb.Service_ACTIVE,
			Sort:             1,
		},
		{
			ID:               2,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   1,
			Name:             "Spa Massage",
			Status:           offeringpb.Service_ACTIVE,
			Sort:             2,
		},
	}

	// 设置 mock 期望
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), nil).Return(mockServices, int64(1), nil)
	helper.setupListServicesAttributeMocks([]int64{1, 2})

	// 执行测试
	resp, err := helper.logic.ListAvailableServices(context.Background(), req)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Len(t, resp.Services, 2)
	assert.Equal(t, int32(2), resp.Total)

	// 验证服务名称包含关键词
	assert.Contains(t, resp.Services[0].Name, "Spa")
	assert.Contains(t, resp.Services[1].Name, "Spa")
}

func TestLogic_BatchUpdateServices_Success(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 准备测试数据
	req := &offeringpb.BatchUpdateServicesRequest{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		UpdateServices: []*offeringpb.ServiceUpdateDef{
			{
				Id:          1,
				Name:        lo.ToPtr("Updated Service 1"),
				Description: lo.ToPtr("Updated Description 1"),
				ColorCode:   lo.ToPtr("#FF0000"),
				Sort:        lo.ToPtr(int64(10)),
				Status:      lo.ToPtr(offeringpb.Service_ACTIVE),
				Images:      []string{"image1.jpg", "image2.jpg"},
				AvailableBusiness: &offeringpb.AvailableBusiness{
					IsAll:       false,
					BusinessIds: []int64{1, 2, 3},
				},
				Attributes: &offeringpb.ServiceAttributes{
					Duration:    lo.ToPtr(int32(60)),
					MaxDuration: lo.ToPtr(int32(120)),
					AutoRollover: &offeringpb.AutoRollover{
						TargetServiceId: 2,
						AfterMinute:     30,
					},
				},
			},
			{
				Id:         2,
				CategoryId: lo.ToPtr(int64(5)),
				Name:       lo.ToPtr("Updated Service 2"),
				Status:     lo.ToPtr(offeringpb.Service_INACTIVE),
			},
		},
	}

	// 模拟数据库返回的服务列表
	mockServices := []*model2.Service{
		{
			ID:               1,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   123,
			Name:             "Original Service 1",
			Status:           offeringpb.Service_ACTIVE,
		},
		{
			ID:               2,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   123,
			Name:             "Original Service 2",
			Status:           offeringpb.Service_ACTIVE,
		},
	}

	// 设置 mock 期望 - List 方法
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), nil).Return(mockServices, int64(2), nil)

	// 设置 mock 期望 - Update 方法
	helper.mockServiceRepo.EXPECT().Update(gomock.Any(), gomock.Any()).DoAndReturn(
		func(ctx context.Context, service *model2.Service) error {
			if service.ID == 1 {
				assert.Equal(t, "Updated Service 1", service.Name)
				assert.Equal(t, "Updated Description 1", *service.Description)
				assert.Equal(t, "#FF0000", service.ColorCode)
				assert.Equal(t, int64(10), service.Sort)
				assert.Equal(t, offeringpb.Service_ACTIVE, service.Status)
				assert.Equal(t, []string{"image1.jpg", "image2.jpg"}, service.Images)
			}
			return nil
		}).Times(2)

	// 设置 mock 期望 - BusinessScope 更新（先查询现有记录，然后创建新记录）
	helper.mockBusinessScopeRepo.EXPECT().GetByServiceID(gomock.Any(), int64(1)).Return(nil, nil)
	helper.mockBusinessScopeRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)

	// 设置 mock 期望 - ServiceAttributes 更新
	helper.mockBasicRepo.EXPECT().ListByServiceID(gomock.Any(), int64(1)).Return([]*model2.ServiceAttribute{}, nil)
	helper.mockBasicRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil).Times(2) // Duration 和 MaxDuration
	helper.mockRolloverRepo.EXPECT().GetByServiceID(gomock.Any(), int64(1)).Return(nil, nil)
	helper.mockRolloverRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)

	// 执行测试
	resp, err := helper.logic.BatchUpdateServices(context.Background(), req)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, resp)
}

func TestLogic_BatchUpdateServices_ServiceNotFound(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 准备测试数据
	req := &offeringpb.BatchUpdateServicesRequest{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		UpdateServices: []*offeringpb.ServiceUpdateDef{
			{
				Id:   1,
				Name: lo.ToPtr("Updated Service"),
			},
			{
				Id:   999, // 不存在的服务ID
				Name: lo.ToPtr("Non-existent Service"),
			},
		},
	}

	// 模拟数据库只返回一个服务
	mockServices := []*model2.Service{
		{
			ID:               1,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   123,
			Name:             "Original Service",
			Status:           offeringpb.Service_ACTIVE,
		},
	}

	// 设置 mock 期望 - List 方法
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), nil).Return(mockServices, int64(1), nil)

	// 设置 mock 期望 - Update 方法（只对存在的服务ID调用）
	helper.mockServiceRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)

	// 执行测试
	resp, err := helper.logic.BatchUpdateServices(context.Background(), req)

	// 验证结果
	assert.Error(t, err)
	assert.Equal(t, "service not found", err.Error())
	assert.Nil(t, resp)
}

func TestLogic_BatchUpdateServices_ListError(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 准备测试数据
	req := &offeringpb.BatchUpdateServicesRequest{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		UpdateServices: []*offeringpb.ServiceUpdateDef{
			{
				Id:   1,
				Name: lo.ToPtr("Updated Service"),
			},
		},
	}

	// 模拟数据库错误
	expectedErr := errors.New("database connection failed")

	// 设置 mock 期望 - List 方法返回错误
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), nil).Return(nil, int64(0), expectedErr)

	// 执行测试
	resp, err := helper.logic.BatchUpdateServices(context.Background(), req)

	// 验证结果
	assert.Error(t, err)
	assert.Equal(t, expectedErr, err)
	assert.Nil(t, resp)
}

func TestLogic_BatchUpdateServices_UpdateError(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 准备测试数据
	req := &offeringpb.BatchUpdateServicesRequest{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		UpdateServices: []*offeringpb.ServiceUpdateDef{
			{
				Id:   1,
				Name: lo.ToPtr("Updated Service"),
			},
		},
	}

	// 模拟数据库返回的服务列表
	mockServices := []*model2.Service{
		{
			ID:               1,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   123,
			Name:             "Original Service",
			Status:           offeringpb.Service_ACTIVE,
		},
	}

	// 模拟数据库错误
	expectedErr := errors.New("update failed")

	// 设置 mock 期望 - List 方法
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), nil).Return(mockServices, int64(1), nil)

	// 设置 mock 期望 - Update 方法返回错误
	helper.mockServiceRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(expectedErr)

	// 执行测试
	resp, err := helper.logic.BatchUpdateServices(context.Background(), req)

	// 验证结果
	assert.Error(t, err)
	assert.Equal(t, expectedErr, err)
	assert.Nil(t, resp)
}

func TestLogic_BatchUpdateServices_BusinessScopeUpdateError(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 准备测试数据
	req := &offeringpb.BatchUpdateServicesRequest{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		UpdateServices: []*offeringpb.ServiceUpdateDef{
			{
				Id:   1,
				Name: lo.ToPtr("Updated Service"),
				AvailableBusiness: &offeringpb.AvailableBusiness{
					IsAll:       false,
					BusinessIds: []int64{1, 2},
				},
			},
		},
	}

	// 模拟数据库返回的服务列表
	mockServices := []*model2.Service{
		{
			ID:               1,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   123,
			Name:             "Original Service",
			Status:           offeringpb.Service_ACTIVE,
		},
	}

	// 模拟数据库错误
	expectedErr := errors.New("business scope update failed")

	// 设置 mock 期望 - List 方法
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), nil).Return(mockServices, int64(1), nil)

	// 设置 mock 期望 - Update 方法
	helper.mockServiceRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)

	// 设置 mock 期望 - BusinessScope 更新返回错误（先查询现有记录，然后创建新记录时失败）
	helper.mockBusinessScopeRepo.EXPECT().GetByServiceID(gomock.Any(), int64(1)).Return(nil, nil)
	helper.mockBusinessScopeRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(expectedErr)

	// 执行测试
	resp, err := helper.logic.BatchUpdateServices(context.Background(), req)

	// 验证结果
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "failed to update business scope")
	assert.Nil(t, resp)
}

func TestLogic_BatchUpdateServices_AttributesUpdateError(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 准备测试数据
	req := &offeringpb.BatchUpdateServicesRequest{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		UpdateServices: []*offeringpb.ServiceUpdateDef{
			{
				Id:   1,
				Name: lo.ToPtr("Updated Service"),
				Attributes: &offeringpb.ServiceAttributes{
					Duration: lo.ToPtr(int32(60)),
				},
			},
		},
	}

	// 模拟数据库返回的服务列表
	mockServices := []*model2.Service{
		{
			ID:               1,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   123,
			Name:             "Original Service",
			Status:           offeringpb.Service_ACTIVE,
		},
	}

	// 模拟数据库错误
	expectedErr := errors.New("attributes update failed")

	// 设置 mock 期望 - List 方法
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), nil).Return(mockServices, int64(1), nil)

	// 设置 mock 期望 - Update 方法
	helper.mockServiceRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)

	// 设置 mock 期望 - ServiceAttributes 更新返回错误
	helper.mockBasicRepo.EXPECT().ListByServiceID(gomock.Any(), int64(1)).Return([]*model2.ServiceAttribute{}, nil)
	helper.mockBasicRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(expectedErr)

	// 执行测试
	resp, err := helper.logic.BatchUpdateServices(context.Background(), req)

	// 验证结果
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "failed to update service attributes")
	assert.Nil(t, resp)
}

func TestLogic_BatchUpdateServices_EmptyUpdateServices(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 准备测试数据 - 空的更新服务列表
	req := &offeringpb.BatchUpdateServicesRequest{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		UpdateServices:   []*offeringpb.ServiceUpdateDef{},
	}

	// 当 UpdateServices 为空时，代码仍然会调用 List 方法，但会传入空的 IDs 切片
	// 设置 mock 期望 - List 方法返回空列表
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), nil).Return([]*model2.Service{}, int64(0), nil)

	// 执行测试
	resp, err := helper.logic.BatchUpdateServices(context.Background(), req)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, resp)
}

func TestLogic_BatchUpdateServices_OnlyBasicFields(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 准备测试数据 - 只更新基本字段
	req := &offeringpb.BatchUpdateServicesRequest{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		UpdateServices: []*offeringpb.ServiceUpdateDef{
			{
				Id:          1,
				Name:        lo.ToPtr("Updated Name"),
				Description: lo.ToPtr("Updated Description"),
				ColorCode:   lo.ToPtr("#00FF00"),
				Sort:        lo.ToPtr(int64(5)),
			},
		},
	}

	// 模拟数据库返回的服务列表
	mockServices := []*model2.Service{
		{
			ID:               1,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   123,
			Name:             "Original Service",
			Status:           offeringpb.Service_ACTIVE,
		},
	}

	// 设置 mock 期望 - List 方法
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), nil).Return(mockServices, int64(1), nil)

	// 设置 mock 期望 - Update 方法
	helper.mockServiceRepo.EXPECT().Update(gomock.Any(), gomock.Any()).DoAndReturn(
		func(ctx context.Context, service *model2.Service) error {
			assert.Equal(t, "Updated Name", service.Name)
			assert.Equal(t, "Updated Description", *service.Description)
			assert.Equal(t, "#00FF00", service.ColorCode)
			assert.Equal(t, int64(5), service.Sort)
			return nil
		})

	// 执行测试
	resp, err := helper.logic.BatchUpdateServices(context.Background(), req)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, resp)
}

func TestLogic_BatchUpdateServices_MultipleServices(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 准备测试数据 - 多个服务更新
	req := &offeringpb.BatchUpdateServicesRequest{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		UpdateServices: []*offeringpb.ServiceUpdateDef{
			{
				Id:   1,
				Name: lo.ToPtr("Service 1 Updated"),
			},
			{
				Id:   2,
				Name: lo.ToPtr("Service 2 Updated"),
			},
			{
				Id:   3,
				Name: lo.ToPtr("Service 3 Updated"),
			},
		},
	}

	// 模拟数据库返回的服务列表
	mockServices := []*model2.Service{
		{
			ID:               1,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   123,
			Name:             "Service 1",
			Status:           offeringpb.Service_ACTIVE,
		},
		{
			ID:               2,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   123,
			Name:             "Service 2",
			Status:           offeringpb.Service_ACTIVE,
		},
		{
			ID:               3,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   123,
			Name:             "Service 3",
			Status:           offeringpb.Service_ACTIVE,
		},
	}

	// 设置 mock 期望 - List 方法
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), nil).Return(mockServices, int64(3), nil)

	// 设置 mock 期望 - Update 方法调用3次
	helper.mockServiceRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil).Times(3)

	// 执行测试
	resp, err := helper.logic.BatchUpdateServices(context.Background(), req)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, resp)
}
