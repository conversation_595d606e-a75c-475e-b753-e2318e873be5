load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "service",
    srcs = [
        "business_scope.go",
        "converter.go",
        "service.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/offering/logic/service",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/offering/logic/association",
        "//backend/app/offering/logic/service/attribute",
        "//backend/app/offering/logic/service/availability",
        "//backend/app/offering/logic/service/obsetting",
        "//backend/app/offering/repo/db",
        "//backend/app/offering/repo/db/model",
        "//backend/app/offering/repo/db/query",
        "//backend/app/offering/repo/db/service",
        "//backend/proto/offering/v1:offering",
        "@com_github_samber_lo//:lo",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

go_test(
    name = "service_test",
    srcs = [
        "business_scope_test.go",
        "service_test.go",
    ],
    embed = [":service"],
    deps = [
        "//backend/app/offering/logic/association",
        "//backend/app/offering/logic/service/attribute",
        "//backend/app/offering/logic/service/availability",
        "//backend/app/offering/logic/service/obsetting",
        "//backend/app/offering/repo/db/association/mocks",
        "//backend/app/offering/repo/db/model",
        "//backend/app/offering/repo/db/service",
        "//backend/app/offering/repo/db/service/mocks",
        "//backend/app/offering/repo/db/serviceattribute/mocks",
        "//backend/app/offering/repo/db/serviceobsetting/mocks",
        "//backend/proto/offering/v1:offering",
        "//backend/proto/organization/v1:organization",
        "@com_github_samber_lo//:lo",
        "@com_github_stretchr_testify//assert",
        "@org_uber_go_mock//gomock",
    ],
)
