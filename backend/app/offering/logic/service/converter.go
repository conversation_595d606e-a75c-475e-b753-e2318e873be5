package service

import (
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/MoeGolibrary/moego/backend/app/offering/logic/association"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/service"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

// CreateDefToModel converts protobuf ServiceCreateDef to model.Service
func CreateDefToModel(pb *offeringpb.ServiceCreateDef) *model.Service {
	if pb == nil {
		return nil
	}

	return &model.Service{
		OrganizationType: pb.GetOrganizationType(),
		OrganizationID:   pb.GetOrganizationId(),
		CareTypeID:       pb.GetCareTypeId(),
		CategoryID:       pb.GetCategoryId(),
		Name:             pb.GetName(),
		Description:      pb.Description,
		ColorCode:        pb.GetColorCode(),
		Images:           pb.GetImages(),
		Source:           pb.GetSource(),
		Status:           pb.GetStatus(),
		Type:             offeringpb.Service_SERVICE,
	}
}

// UpdateDefToModel converts protobuf ServiceUpdateDef to model.Service
func UpdateDefToModel(pb *offeringpb.ServiceUpdateDef) *model.Service {
	if pb == nil {
		return nil
	}

	m := &model.Service{
		ID: pb.GetId(),
	}

	if pb.CategoryId != nil {
		m.CategoryID = pb.GetCategoryId()
	}
	if pb.Name != nil {
		m.Name = pb.GetName()
	}
	if pb.Description != nil {
		m.Description = pb.Description
	}
	if pb.ColorCode != nil {
		m.ColorCode = pb.GetColorCode()
	}
	if pb.Sort != nil {
		m.Sort = pb.GetSort()
	}
	if len(pb.Images) > 0 {
		m.Images = pb.Images
	}
	if pb.Status != nil {
		m.Status = pb.GetStatus()
	}

	return m
}

// RequestToFilter 构造服务查询的过滤条件
func RequestToFilter(
	req *offeringpb.ListAvailableServicesRequest) *service.ListServiceFilter {
	filter := &service.ListServiceFilter{
		OrganizationType: req.OrganizationType,
		OrganizationID:   req.OrganizationId,
		Types:            []offeringpb.Service_Type{offeringpb.Service_SERVICE},
	}
	// 有过滤条件
	if req.Filter != nil {
		filter.CareTypeIDs = req.Filter.CareTypeIds
		filter.Statuses = req.Filter.Statuses
		filter.Keyword = req.Filter.Keyword
	}

	return filter
}

// ModelToProto 转换 model.Service 为 proto.Service
func ModelToProto(m *model.Service) *offeringpb.Service {
	if m == nil {
		return nil
	}

	pb := &offeringpb.Service{
		Id:               m.ID,
		OrganizationType: m.OrganizationType,
		OrganizationId:   m.OrganizationID,
		CareTypeId:       m.CareTypeID,
		Name:             m.Name,
		Description:      m.Description,
		ColorCode:        m.ColorCode,
		Sort:             &m.Sort,
		Images:           m.Images,
		Source:           m.Source,
		Status:           m.Status,
		Type:             m.Type,
	}

	if m.CategoryID != 0 {
		pb.CategoryId = &m.CategoryID
	}

	if m.CreateTime != nil {
		pb.CreateTime = timestamppb.New(*m.CreateTime)
	}
	if m.UpdateTime != nil {
		pb.UpdateTime = timestamppb.New(*m.UpdateTime)
	}
	if m.DeleteTime != nil {
		pb.DeleteTime = timestamppb.New(*m.DeleteTime)
	}

	return pb
}

// ToAssociationAggregate 将 proto.AdditionalService 转换为 association.ServiceAssociationAggregate
func ToAssociationAggregate(
	serviceID int64, pb *offeringpb.AdditionalService) *association.ServiceAssociationAggregate {
	if pb == nil {
		return association.NewAggregate(serviceID)
	}

	agg := association.NewAggregate(serviceID)

	agg.TargetCareTypeIDs = pb.AdditionalCareTypeIds
	agg.TargetServiceIDs = pb.AdditionalServiceIds
	agg.TargetServiceTypes = pb.AdditionalServiceTypes

	return agg
}

// ToAdditionalService 将 association.ServiceAssociationAggregate 转换为 proto.AdditionalService
func ToAdditionalService(agg *association.ServiceAssociationAggregate) *offeringpb.AdditionalService {
	if agg == nil {
		return &offeringpb.AdditionalService{}
	}

	return &offeringpb.AdditionalService{
		AdditionalCareTypeIds:  agg.TargetCareTypeIDs,
		AdditionalServiceIds:   agg.TargetServiceIDs,
		AdditionalServiceTypes: agg.TargetServiceTypes,
	}
}
