package service

import (
	"context"

	"github.com/MoeGolibrary/moego/backend/app/offering/logic/service"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

type Service struct {
	logic *service.Logic
	offeringpb.UnimplementedServiceServiceServer
}

func NewServiceService() *Service {
	return &Service{
		logic: service.NewLogic(),
	}
}

func (t *Service) CreateService(ctx context.Context,
	req *offeringpb.CreateServiceRequest) (*offeringpb.CreateServiceResponse, error) {

	id, err := t.logic.CreateService(ctx, req.GetService())
	if err != nil {
		return nil, err
	}

	return &offeringpb.CreateServiceResponse{
		ServiceId: id,
	}, nil
}

func (t *Service) GetService(ctx context.Context,
	req *offeringpb.GetServiceRequest) (*offeringpb.GetServiceResponse, error) {
	service, err := t.logic.GetService(ctx, req.GetServiceId())
	if err != nil {
		return nil, err
	}

	return &offeringpb.GetServiceResponse{
		Service: service,
	}, nil
}

func (t *Service) UpdateService(ctx context.Context,
	req *offeringpb.UpdateServiceRequest) (*offeringpb.UpdateServiceResponse, error) {

	err := t.logic.UpdateService(ctx, req.GetService())
	if err != nil {
		return nil, err
	}

	return &offeringpb.UpdateServiceResponse{}, nil
}

func (t *Service) DeleteService(ctx context.Context,
	req *offeringpb.DeleteServiceRequest) (*offeringpb.DeleteServiceResponse, error) {
	err := t.logic.DeleteService(ctx, req.GetServiceId())
	if err != nil {
		return nil, err
	}

	return &offeringpb.DeleteServiceResponse{}, nil
}

func (t *Service) ListServices(ctx context.Context,
	req *offeringpb.ListServicesRequest) (*offeringpb.ListServicesResponse, error) {
	return t.logic.ListServices(ctx, req)
}

func (t *Service) ListAvailableServices(ctx context.Context,
	req *offeringpb.ListAvailableServicesRequest) (*offeringpb.ListAvailableServicesResponse, error) {
	return t.logic.ListAvailableServices(ctx, req)
}

func (t *Service) BatchUpdateServices(ctx context.Context,
	req *offeringpb.BatchUpdateServicesRequest) (*offeringpb.BatchUpdateServicesResponse, error) {
	return t.logic.BatchUpdateServices(ctx, req)
}

func (t *Service) BatchGetServices(ctx context.Context,
	req *offeringpb.BatchGetServicesRequest) (*offeringpb.BatchGetServicesResponse, error) {
	return t.logic.BatchGetServices(ctx, req)
}
