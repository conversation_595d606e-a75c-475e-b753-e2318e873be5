package address

import (
	"database/sql/driver"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/lib/pq"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type Address struct {
	ID                 int64                           `gorm:"primaryKey;column:id"`
	CustomerID         int64                           `gorm:"column:customer_id"`
	OrganizationID     int64                           `gorm:"column:organization_id"`
	OrganizationType   customerpb.OrganizationRef_Type `gorm:"column:organization_type;serializer:proto_enum"`
	Revision           int32                           `gorm:"column:revision"`
	RegionCode         string                          `gorm:"column:region_code"`
	LanguageCode       string                          `gorm:"column:language_code"`
	Organization       string                          `gorm:"column:organization"`
	PostalCode         string                          `gorm:"column:postal_code"`
	SortingCode        string                          `gorm:"column:sorting_code"`
	AdministrativeArea string                          `gorm:"column:administrative_area"`
	Locality           string                          `gorm:"column:locality"`
	Sublocality        string                          `gorm:"column:sublocality"`
	AddressLines       pq.StringArray                  `gorm:"column:address_lines;type:text[]"`
	Recipients         pq.StringArray                  `gorm:"column:recipients;type:text[]"`
	Latitude           float64                         `gorm:"column:latitude"`
	Longitude          float64                         `gorm:"column:longitude"`
	Type               customerpb.Address_Type         `gorm:"column:type;serializer:proto_enum"`
	State              customerpb.Address_State        `gorm:"column:state;serializer:proto_enum"`
	DeletedTime        *time.Time                      `gorm:"column:deleted_time"`
	CreatedTime        time.Time                       `gorm:"column:created_time;autoCreateTime"`
	UpdatedTime        time.Time                       `gorm:"column:updated_time;autoUpdateTime"`

	// 地理位置
	Geom GeoPoint `gorm:"column:geom"` // 空间字段
}

func (Address) TableName() string {
	return "address"
}

type ListFilter struct {
	IDs              []int64
	CustomerID       int64
	OrganizationRefs []*customerpb.OrganizationRef
	States           []customerpb.Address_State
	CustomerIDs      []int64
	Types            []customerpb.Address_Type
	SpatialFilter    *customerpb.SpatialFilter
}

type Pagination struct {
	PageSize        int32
	Cursor          *postgres.Cursor
	ReturnTotalSize bool
}

type OrderBy struct {
	Field     customerpb.ListAddressesRequest_Sorting_Field
	Direction customerpb.Direction
}

// CursorResult 游标分页结果
type CursorResult struct {
	Data       []*Address `json:"data"`     // 数据列表
	HasNext    bool       `json:"has_next"` // 是否有下一页
	TotalCount *int64     `json:"total_count"`
}

// HavingCondition 计数过滤条件
type HavingCondition struct {
	Field    string // 字段名，如 address_count
	Operator string // 操作符，如 GREATER_THAN
	Value    string // 值
}

// GeoPoint用于封装空间点，存储WKT字符串
type GeoPoint struct {
	Longitude float64
	Latitude  float64
	WKT       string // 方便调试和存储
}

// 实现 driver.Valuer
func (g GeoPoint) Value() (driver.Value, error) {
	if g.Longitude == 0 && g.Latitude == 0 {
		return nil, nil
	}
	// 返回空间WKT字符串
	return fmt.Sprintf("SRID=4326;POINT(%f %f)", g.Longitude, g.Latitude), nil
}

// 实现 sql.Scanner
func (g *GeoPoint) Scan(value interface{}) error {
	switch v := value.(type) {
	case string:
		g.WKT = v
		// 可选：解析WKT到经纬度（需要额外解析）
		// 这里只存WKT
		return nil
	case []byte:
		g.WKT = string(v)

		return nil
	default:
		return fmt.Errorf("unsupported scan type: %T", v)
	}
}

// 可选：辅助方法，解析WKT字符串到经纬度（如果需要）
func (g *GeoPoint) ParseWKT() error {
	// 简单解析示例，实际应使用WKT解析库
	if strings.HasPrefix(g.WKT, "SRID=4326;POINT(") {
		pointStr := strings.TrimPrefix(g.WKT, "SRID=4326;POINT(")
		pointStr = strings.TrimSuffix(pointStr, ")")
		parts := strings.Split(pointStr, " ")
		if len(parts) != 2 {
			return fmt.Errorf("invalid WKT point: %s", g.WKT)
		}
		var err error
		g.Longitude, err = strconv.ParseFloat(parts[0], 64)
		if err != nil {
			return err
		}
		g.Latitude, err = strconv.ParseFloat(parts[1], 64)

		return err
	}

	return fmt.Errorf("unsupported WKT format: %s", g.WKT)
}
