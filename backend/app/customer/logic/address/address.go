package address

import (
	"context"
	"errors"
	"fmt"
	"time"

	"google.golang.org/grpc/codes"
	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	addressrepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/address"
	"github.com/MoeGolibrary/moego/backend/app/customer/repo/redis"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type Logic struct {
	addressRepo addressrepo.Repository
	redisRepo   redis.API
}

func New() *Logic {
	return &Logic{
		addressRepo: addressrepo.New(),
		redisRepo:   redis.New(),
	}
}

func NewByParams(
	addressRepo addressrepo.Repository,
) *Logic {
	return &Logic{
		addressRepo: addressRepo,
		redisRepo:   redis.New(),
	}
}

func (l *Logic) WithTx(tx *gorm.DB) *Logic {
	return &Logic{
		addressRepo: l.addressRepo.WithTx(tx),
		redisRepo:   l.redisRepo,
	}
}

func (l *Logic) Create(ctx context.Context, req *Address) (*Address, error) {
	// create address, set output only columns
	now := time.Now().UTC()
	address := req.ToDB()
	address.State = customerpb.Address_ACTIVE
	address.CreatedTime = now
	address.UpdatedTime = now

	dbAddress, err := l.addressRepo.Create(ctx, address)
	if err != nil {
		return nil, errs.Newm(codes.Internal, "failed to create address")
	}
	// convert to logic address, and return
	return convertToAddress(dbAddress), nil
}

func (l *Logic) Get(ctx context.Context, id int64) (*Address, error) {
	dbAddress, err := l.addressRepo.Get(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errs.New(customerpb.ErrCode_ERR_CODE_ADDRESS_NOT_FOUND)
		}

		return nil, err
	}

	return convertToAddress(dbAddress), nil
}

func (l *Logic) List(ctx context.Context, req *ListAddressesRequest) (*ListAddressesResponse, error) {
	if req.Filter == nil {
		return nil, errs.Newm(codes.InvalidArgument, "filter is required")
	}
	if req.OrderBy == nil {
		req.OrderBy = &ListAddressesOrderBy{
			Field:     customerpb.ListAddressesRequest_Sorting_CREATED_TIME,
			Direction: customerpb.Direction_DESC,
		}
	}
	dbAddresses, err := l.addressRepo.ListByCursor(ctx, &addressrepo.ListFilter{
		IDs:              req.Filter.IDs,
		CustomerID:       req.Filter.CustomerID,
		OrganizationRefs: req.Filter.OrganizationRefs,
		States:           req.Filter.States,
		CustomerIDs:      req.Filter.CustomerIDs,
		Types:            req.Filter.Types,
		SpatialFilter:    req.Filter.SpatialFilter,
	}, &addressrepo.Pagination{
		PageSize:        req.Pagination.PageSize,
		Cursor:          req.Pagination.DecodeCursor(),
		ReturnTotalSize: req.Pagination.ReturnTotalSize,
	}, &addressrepo.OrderBy{
		Field:     req.OrderBy.Field,
		Direction: req.OrderBy.Direction,
	})
	if err != nil {
		return nil, err
	}

	result := &ListAddressesResponse{
		Addresses: convertToAddresses(dbAddresses.Data),
		HasNext:   dbAddresses.HasNext,
	}
	if dbAddresses.TotalCount != nil {
		result.TotalSize = dbAddresses.TotalCount
	}
	if dbAddresses.HasNext && len(dbAddresses.Data) > 0 {
		lastAddress := dbAddresses.Data[len(dbAddresses.Data)-1]
		cursor := postgres.Cursor{
			ID:        lastAddress.ID,
			CreatedAt: lastAddress.CreatedTime,
		}
		result.NextToken = cursor.EncodeCursor()
	}

	return result, nil
}

func (l *Logic) Update(ctx context.Context, id int64, updateRef *UpdateAddressRequest) (*Address, error) {
	// check address exists
	address, err := l.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	now := time.Now().UTC()
	address.UpdatedTime = now
	address.Revision = updateRef.Revision
	address.RegionCode = updateRef.RegionCode
	address.LanguageCode = updateRef.LanguageCode
	address.Organization = updateRef.Organization
	address.PostalCode = updateRef.PostalCode
	address.SortingCode = updateRef.SortingCode
	address.AdministrativeArea = updateRef.AdministrativeArea
	address.Locality = updateRef.Locality
	address.Sublocality = updateRef.Sublocality
	address.AddressLines = updateRef.AddressLines
	address.Recipients = updateRef.Recipients
	address.Latitude = updateRef.Latitude
	address.Longitude = updateRef.Longitude
	address.Type = updateRef.Type
	address.State = updateRef.State
	// 如果设置成主地址，则需要先获取主地址，并设置成附加地址
	if address.Type == customerpb.Address_PRIMARY {
		lockKey := fmt.Sprintf(redis.AddressPrimaryLockKey, address.CustomerID)
		if err := l.redisRepo.Lock(ctx, lockKey, redis.AddressPrimaryLockTTL); err != nil {
			return nil, err
		}
		defer func() {
			if err := l.redisRepo.Unlock(ctx, lockKey); err != nil {
				log.ErrorContextf(ctx, "UpdateAddress Failed to release lock, lock:%s, err:%v", lockKey, err)
			}
		}()

		primaryAddress, err := l.addressRepo.GetPrimaryAddress(ctx, address.CustomerID)
		if err != nil {
			return nil, err
		}

		if primaryAddress != nil {
			if primaryAddress.ID != id {
				primaryAddress.Type = customerpb.Address_ADDITIONAL
				primaryAddress.UpdatedTime = now
				_, err = l.addressRepo.Update(ctx, primaryAddress)
				if err != nil {
					return nil, err
				}
			}
		}
	}
	updatedAddress, err := l.addressRepo.Update(ctx, address.ToDB())
	if err != nil {
		return nil, err
	}

	return convertToAddress(updatedAddress), nil
}

func (l *Logic) Delete(ctx context.Context, id int64) error {
	_, err := l.Get(ctx, id)
	if err != nil {
		return err
	}

	err = l.addressRepo.Delete(ctx, id)
	if err != nil {
		return err
	}

	return nil
}

func convertToAddresses(dbAddresses []*addressrepo.Address) []*Address {
	addresses := make([]*Address, len(dbAddresses))
	for i, dbAddress := range dbAddresses {
		addresses[i] = convertToAddress(dbAddress)
	}

	return addresses
}

func convertToAddress(dbAddress *addressrepo.Address) *Address {
	return &Address{
		ID:                 dbAddress.ID,
		CustomerID:         dbAddress.CustomerID,
		OrganizationID:     dbAddress.OrganizationID,
		OrganizationType:   dbAddress.OrganizationType,
		Revision:           dbAddress.Revision,
		RegionCode:         dbAddress.RegionCode,
		LanguageCode:       dbAddress.LanguageCode,
		Organization:       dbAddress.Organization,
		PostalCode:         dbAddress.PostalCode,
		SortingCode:        dbAddress.SortingCode,
		AdministrativeArea: dbAddress.AdministrativeArea,
		Locality:           dbAddress.Locality,
		Sublocality:        dbAddress.Sublocality,
		AddressLines:       dbAddress.AddressLines,
		Recipients:         dbAddress.Recipients,
		Latitude:           dbAddress.Latitude,
		Longitude:          dbAddress.Longitude,
		Type:               dbAddress.Type,
		State:              dbAddress.State,
		DeletedTime:        dbAddress.DeletedTime,
		CreatedTime:        dbAddress.CreatedTime,
		UpdatedTime:        dbAddress.UpdatedTime,
	}
}
