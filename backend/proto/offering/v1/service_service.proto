syntax = "proto3";

package backend.proto.offering.v1;

import "backend/proto/offering/v1/common.proto";
import "backend/proto/offering/v1/service.proto";
import "backend/proto/offering/v1/service_ob_setting.proto";
import "backend/proto/organization/v1/organization.proto";
import "buf/validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego/backend/proto/offering/v1;offeringpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.offering.v1";

// (-- api-linter: core::0131::response-message-name=disabled
//     aip.dev/not-precedent: 统一用 Response message. --)
// (-- api-linter: core::0133::response-message-name=disabled
//     aip.dev/not-precedent: 统一用 Response message. --)
// (-- api-linter: core::0134::response-message-name=disabled
//     aip.dev/not-precedent: 统一用 Response message. --)
// (-- api-linter: core::0135::response-message-name=disabled
//     aip.dev/not-precedent: 统一用 Response message. --)
// Service for managing Service resources.
service ServiceService {
  // 创建一个服务
  rpc CreateService(CreateServiceRequest) returns (CreateServiceResponse);

  // 获取服务
  rpc GetService(GetServiceRequest) returns (GetServiceResponse);

  // 更新服务
  rpc UpdateService(UpdateServiceRequest) returns (UpdateServiceResponse);

  // 删除服务
  rpc DeleteService(DeleteServiceRequest) returns (DeleteServiceResponse);

  // list services
  rpc ListServices(ListServicesRequest) returns (ListServicesResponse);

  // ListAvailableServices 查询指定 business 的可用服务列表
  //
  // 用于预约场景，支持根据 pet 特征进行服务匹配筛选。
  // 当请求中包含 pet_ids 时，系统会根据以下 pet 配置信息过滤服务：
  // - type/breed（宠物类型/品种，如: Dog/Golden Doodle, Cat/American
  // Shorthair）
  // - code（宠物编码，商家自定义的标识）
  // - weight/size（体型，如：small, medium, large, giant）
  // - coat type（毛发类型，如：short, medium, long, double coat）
  //
  // 如果未传递 pet_ids，则返回该 business 下所有可用的服务。
  rpc ListAvailableServices(ListAvailableServicesRequest) returns (ListAvailableServicesResponse);

  // BatchUpdateServices 批量更新服务信息
  //
  // 支持批量更新服务的名称、排序值等基本信息
  rpc BatchUpdateServices(BatchUpdateServicesRequest) returns (BatchUpdateServicesResponse);

  // 更新服务
  rpc UpdateOBService(UpdateOBServiceRequest) returns (UpdateOBServiceResponse);

  // 批量获取服务
  rpc BatchGetServices(BatchGetServicesRequest) returns (BatchGetServicesResponse);
}

// 删除服务请求
message DeleteServiceRequest {
  // 服务ID
  int64 service_id = 1 [(buf.validate.field) = {
    int64: {gt: 0}
  }];
}

// 删除服务响应
message DeleteServiceResponse {}

// 更新服务请求
message UpdateServiceRequest {
  // 服务更新配置
  ServiceUpdateDef service = 1 [(buf.validate.field) = {
    required: true
  }];
}

// 更新服务响应
message UpdateServiceResponse {}

// (-- api-linter: core::0133::request-resource-field=disabled
//     aip.dev/not-precedent: 不复用 model 结构，单独定义 ServiceCreateDef 结构 --)
// 创建服务请求
message CreateServiceRequest {
  // 服务创建配置
  ServiceCreateDef service = 1 [(buf.validate.field) = {
    required: true
  }];
}

// 创建服务响应
message CreateServiceResponse {
  // 服务 ID
  int64 service_id = 1;
}

// 获取服务请求
message GetServiceRequest {
  // 服务ID
  int64 service_id = 1 [(buf.validate.field) = {
    int64: {gt: 0}
  }];
}

// 获取服务响应
message GetServiceResponse {
  // 服务
  Service service = 1;
}

// 分类服务
message CategoryService {
  // 分类ID
  int64 category_id = 1;
  // 分类名称
  string name = 2;
  // 服务模板列表
  repeated Service services = 3;
}

// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: 使用 PaginationRef 替代 --)
// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: 使用 PaginationRef 替代 --)
// (-- api-linter: core::0132::request-parent-required=disabled
//     aip.dev/not-precedent: 无 parent 语义 --)
// list services request
message ListServicesRequest {
  // The organization type
  backend.proto.organization.v1.OrganizationType organization_type = 1 [(buf.validate.field) = {
    required: true,
    enum: {
      defined_only: true
      not_in: [0]
    }
  }];

  // The organization ID.
  int64 organization_id = 2 [(buf.validate.field) = {
    required: true,
    int64: {gt: 0}
  }];
  // filter
  Filter filter = 3;

  // options to include extra info in response
  ExtraInfoOptions extra_info_options = 4;
  // 分页信息
  PaginationRef pagination = 5;

  // list filter
  message Filter {
    // category ids
    repeated int64 category_ids = 1 [(buf.validate.field) = {
      repeated: {
        min_items: 0
        max_items: 100
      }
    }];

    // care type ids
    repeated int64 care_type_ids = 2 [(buf.validate.field) = {
      repeated: {
        min_items: 0
        max_items: 100
      }
    }];

    // statuses
    repeated Service.Status statuses = 3 [(buf.validate.field) = {
      repeated: {
        min_items: 0
        max_items: 10
      }
    }];
  }
}

// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: 使用 PaginationRef 替代 --)
// (-- api-linter: core::0132::response-unknown-fields=disabled
//     aip.dev/not-precedent: 额外返回 total. --)
// list setting services response
message ListServicesResponse {
  // 服务模板列表
  repeated ServiceWithExtraInfo services = 1;
  // 分页信息
  optional PaginationRef pagination = 2;
  // 总数
  int32 total = 3;
}

// (-- api-linter: core::0234::request-unknown-fields=disabled
//     aip.dev/not-precedent: 需要租户信息 --)
// (-- api-linter: core::0234::request-requests-field=disabled
//     aip.dev/not-precedent: 不复用 UpdateServiceRequest 结构 --)
// (-- api-linter: core::0234::request-parent-field=disabled
//     aip.dev/not-precedent: 无 parent 语义 --)
// 批量更新服务请求
message BatchUpdateServicesRequest {
  // 当前的租户类型
  backend.proto.organization.v1.OrganizationType organization_type = 1 [(buf.validate.field) = {
    enum: {
      defined_only: true
      not_in: [0]
    }
  }];

  // 当前的租户 ID
  int64 organization_id = 2 [(buf.validate.field) = {
    int64: {gt: 0}
  }];

  // 服务更新配置列表
  repeated ServiceUpdateDef update_services = 3 [(buf.validate.field) = {
    repeated: {
      min_items: 1
      max_items: 1000
    }
  }];
}

// 单个服务的更新配置
message ServiceUpdateDef {
  // 服务 ID
  int64 id = 1 [(buf.validate.field) = {
    int64: {gt: 0}
  }];

  // 分类 ID（可选）
  optional int64 category_id = 2 [(buf.validate.field) = {
    int64: {gt: 0}
  }];

  // 服务名称（可选）
  optional string name = 3 [(buf.validate.field) = {
    string: {max_len: 100}
  }];

  // 描述
  optional string description = 4 [(buf.validate.field) = {
    string: {max_len: 1000}
  }];

  // 颜色
  optional string color_code = 5 [(buf.validate.field) = {
    string: {max_len: 10}
  }];

  // 排序值（可选）
  optional int64 sort = 6 [(buf.validate.field) = {
    int64: {gt: 0}
  }];

  // 图片
  repeated string images = 7 [(buf.validate.field) = {
    repeated: {
      min_items: 0
      max_items: 1000
      unique: true
      items: {
        string: {max_len: 1024}
      }
    }
  }];

  // 状态
  optional Service.Status status = 8 [(buf.validate.field) = {
    enum: {
      defined_only: true
      not_in: [0]
    }
  }];

  // 可用 business scope
  optional AvailableBusiness available_business = 9;

  // 关联的属性值
  optional ServiceAttributes attributes = 10;

  // 附加服务配置（可选）
  optional AdditionalService additional_service = 11;

  // 可用宠物类型和品种配置（可选）
  optional AvailablePetTypeBreed available_type_breed = 12;

  // 可用宠物尺寸配置（可选）
  optional AvailablePetSize available_pet_size = 13;

  // 可用宠物毛类型配置（可选）
  optional AvailableCoatType available_coat_type = 14;

  // 可用宠物代码配置（可选）
  optional AvailablePetCode available_pet_code = 15;
}

// (-- api-linter: core::0234::response-resource-field=disabled
//     aip.dev/not-precedent: 不返回 Service 类型 --)
// 批量更新服务响应
message BatchUpdateServicesResponse {}

// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: 使用 PaginationRef 替代 --)
// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: 使用 PaginationRef 替代 --)
// (-- api-linter: core::0132::request-parent-required=disabled
//     aip.dev/not-precedent: 无 parent 语义 --)
// 查询可用服务请求（用于预约场景）
message ListAvailableServicesRequest {
  // 当前的租户类型
  backend.proto.organization.v1.OrganizationType organization_type = 1 [(buf.validate.field) = {
    enum: {
      defined_only: true
      not_in: [0]
    }
  }];

  // 当前的租户 ID
  int64 organization_id = 2 [(buf.validate.field) = {
    int64: {gt: 0}
  }];

  // 当前需要预约的 business_id
  int64 business_id = 3 [(buf.validate.field) = {
    int64: {gt: 0}
  }];

  // 是否启用 business scope 范围过滤，默认启用
  // 当为 true 时，会根据 ServiceBusinessScope 的 available_business_ids 配置过滤服务范围
  // 当为 false 时，不进行 business scope 范围过滤
  bool enable_business_scope_filter = 4;

  // 可选的过滤条件
  optional Filter filter = 5;

  // 分页信息
  optional PaginationRef pagination = 6;

  // 过滤条件
  message Filter {
    // 当前选择的 Pet ID 列表
    // 如果传递了 pet_ids，将根据 pet 的 type、breed、code
    // 等配置信息进行服务匹配筛选 如果未传递 pet_ids，则返回该 business
    // 下所有可用的服务
    repeated int64 pet_ids = 1 [(buf.validate.field) = {
      repeated: {
        min_items: 0
        max_items: 100
        unique: true
        items: {
          int64: {gt: 0}
        }
      }
    }];

    // 当前选择的 care_type_id 列表
    repeated int64 care_type_ids = 2 [(buf.validate.field) = {
      repeated: {
        min_items: 0
        max_items: 100
        unique: true
        items: {
          int64: {gt: 0}
        }
      }
    }];

    // 状态过滤（如：active/inactive 等）
    repeated Service.Status statuses = 3 [(buf.validate.field) = {
      repeated: {
        min_items: 0
        max_items: 10
        unique: true
        items: {
          enum: {
            defined_only: true
            not_in: [0]
          }
        }
      }
    }];

    // 关键词搜索（如服务名称等）
    optional string keyword = 4 [(buf.validate.field) = {
      string: {max_len: 100}
    }];
  }
}

// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: 使用 PaginationRef 替代 --)
// (-- api-linter: core::0132::response-unknown-fields=disabled
//     aip.dev/not-precedent: 额外返回 total. --)
// 查询可用服务响应
message ListAvailableServicesResponse {
  // 可用服务列表
  repeated Service services = 1;

  // 分页信息
  optional PaginationRef pagination = 2;

  // 总数
  int32 total = 3;
}

//UpdateOBServiceRequest
message UpdateOBServiceRequest{
  // 服务
  int64 service_id = 1;
  // Whether the service is available for online booking
  optional bool is_available = 2;
  // Display price mode: 0 - Do not show, 1 - Show fixed price, 2 - Show "starting at", 3 - Show "varies"
  optional ServiceOBSetting.ShowBasePriceMode show_base_price = 3;
  // Whether all staff are available for this service when booking online
  optional bool is_all_staff = 4;
  //staff ids
  repeated int64 staff_ids = 5;
  // Optional display alias for the service in online booking
  optional string alias = 6;
}

//Update OB Service Response
message UpdateOBServiceResponse{
}

// (-- api-linter: core::0231::request-unknown-fields=disabled
//     aip.dev/not-precedent: 直接根据 ID 批量获取服务 --)
// (-- api-linter: core::0231::request-names-field=disabled
//     aip.dev/not-precedent: 直接根据 ID 批量获取服务 --)
// 批量获取服务请求
message BatchGetServicesRequest {
  // 当前的租户类型
  backend.proto.organization.v1.OrganizationType organization_type = 1 [(buf.validate.field) = {
    enum: {
      defined_only: true
      not_in: [0]
    }
  }];

  // 当前的租户 ID
  int64 organization_id = 2 [(buf.validate.field) = {
    int64: {gt: 0}
  }];

  // 服务ID列表
  repeated int64 ids = 3 [(buf.validate.field) = {
    repeated: {
      min_items: 1
      max_items: 100
      unique: true
      items: {
        int64: {gt: 0}
      }
    }
  }];
}

// 批量获取服务响应
message BatchGetServicesResponse {
  // 服务列表
  repeated Service services = 1;
}

// Service 创建配置定义
message ServiceCreateDef {
  // 组织类型
  backend.proto.organization.v1.OrganizationType organization_type = 1 [(buf.validate.field) = {
    required: true,
    enum: {
      defined_only: true
      not_in: [0]
    }
  }];
  
  // 组织 ID
  int64 organization_id = 2 [(buf.validate.field) = {
    required: true,
    int64: {gt: 0}
  }];
  
  // 护理类型 ID
  int64 care_type_id = 3 [(buf.validate.field) = {
    required: true,
    int64: {gt: 0}
  }];
  
  // 分类 ID（可选）
  optional int64 category_id = 4 [(buf.validate.field) = {
    int64: {gt: 0}
  }];
  
  // 服务名称
  string name = 5 [(buf.validate.field) = {
    required: true,
    string: {
      min_len: 1,
      max_len: 100
    }
  }];
  
  // 描述（可选）
  optional string description = 6 [(buf.validate.field) = {
    string: {max_len: 1000}
  }];
  
  // 颜色代码
  string color_code = 7 [(buf.validate.field) = {
    required: true,
    string: {
      min_len: 1,
      max_len: 10
    }
  }];
  
  // 图片列表（可选）
  repeated string images = 9 [(buf.validate.field) = {
    repeated: {
      min_items: 0,
      max_items: 1000,
      unique: true,
      items: {
        string: {max_len: 1024}
      }
    }
  }];
  
  // 来源
  OfferingSource source = 10 [(buf.validate.field) = {
    required: true,
    enum: {
      defined_only: true
      not_in: [0]
    }
  }];
  
  // 状态
  Service.Status status = 11 [(buf.validate.field) = {
    required: true,
    enum: {
      defined_only: true
      not_in: [0]
    }
  }];
  
  // 可用业务范围
  AvailableBusiness available_business = 13 [(buf.validate.field) = {
    required: true
  }];
  
  // 附加服务配置（可选）
  optional AdditionalService additional_service = 14;
  
  // 可用宠物类型和品种配置（可选）
  optional AvailablePetTypeBreed available_type_breed = 15;
  
  // 可用宠物尺寸配置（可选）
  optional AvailablePetSize available_pet_size = 16;
  
  // 可用宠物毛类型配置（可选）
  optional AvailableCoatType available_coat_type = 17;
  
  // 可用宠物代码配置（可选）
  optional AvailablePetCode available_pet_code = 18;
  
  // 服务属性（可选）
  optional ServiceAttributes attributes = 19;
}