syntax = "proto3";

package backend.proto.offering.v1;

import "backend/proto/offering/v1/common.proto";
import "backend/proto/offering/v1/addon.proto";
import "backend/proto/offering/v1/service.proto";
import "backend/proto/organization/v1/organization.proto";
import "buf/validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego/backend/proto/offering/v1;offeringpb";
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.offering.v1";

// (-- api-linter: core::0131::response-message-name=disabled
//     aip.dev/not-precedent: 统一用 Response message. --)
// (-- api-linter: core::0133::response-message-name=disabled
//     aip.dev/not-precedent: 统一用 Response message. --)
// (-- api-linter: core::0134::response-message-name=disabled
//     aip.dev/not-precedent: 统一用 Response message. --)
// (-- api-linter: core::0135::response-message-name=disabled
//     aip.dev/not-precedent: 统一用 Response message. --)
// Service for managing AddOn resources.
service AddOnService {
  // 创建一个 AddOn
  rpc CreateAddOn(CreateAddOnRequest) returns (CreateAddOnResponse);

  // 获取 AddOn
  rpc GetAddOn(GetAddOnRequest) returns (GetAddOnResponse);

  // 更新 AddOn
  rpc UpdateAddOn(UpdateAddOnRequest) returns (UpdateAddOnResponse);

  // 删除 AddOn
  rpc DeleteAddOn(DeleteAddOnRequest) returns (DeleteAddOnResponse);

  // 列表查询 AddOn
  rpc ListAddOns(ListAddOnsRequest) returns (ListAddOnsResponse);

  // 批量更新 AddOn
  rpc BatchUpdateAddOns(BatchUpdateAddOnsRequest) returns (BatchUpdateAddOnsResponse);

  // 获取 AddOn 分类列表
  rpc ListAddOnCategories(ListAddOnCategoriesRequest) returns (ListAddOnCategoriesResponse);

  // 保存 AddOn 分类
  rpc SaveAddOnCategories(SaveAddOnCategoriesRequest) returns (SaveAddOnCategoriesResponse);
}

// (-- api-linter: core::0133::request-resource-field=disabled
//     aip.dev/not-precedent: 不复用 model 结构，单独定义 AddOnCreateDef 结构 --)
// 创建 AddOn 请求
message CreateAddOnRequest {
  // AddOn 创建配置
  AddOnCreateDef add_on = 1 [(buf.validate.field) = {
    required: true
  }];
}

// 创建 AddOn 响应
message CreateAddOnResponse {
  // AddOn ID
  int64 id = 1;
}

// 获取 AddOn 请求
message GetAddOnRequest {
  // AddOn ID
  int64 id = 1 [(buf.validate.field) = {
    int64: {gt: 0}
  }];
}

// 获取 AddOn 响应
message GetAddOnResponse {
  // AddOn 信息
  AddOn add_on = 1;
}

// 更新 AddOn 请求
message UpdateAddOnRequest {
  // AddOn 更新配置
  AddOnUpdateDef add_on = 1 [(buf.validate.field) = {
    required: true
  }];
}

// 更新 AddOn 响应
message UpdateAddOnResponse {}

// 删除 AddOn 请求
message DeleteAddOnRequest {
  // AddOn ID
  int64 id = 1 [(buf.validate.field) = {
    int64: {gt: 0}
  }];
}

// 删除 AddOn 响应
message DeleteAddOnResponse {}

// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: 使用 PaginationRef 替代 --)
// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: 使用 PaginationRef 替代 --)
// (-- api-linter: core::0132::request-parent-required=disabled
//     aip.dev/not-precedent: 无 parent 语义 --)
// 列表查询 AddOn 请求
message ListAddOnsRequest {
  // 组织类型
  backend.proto.organization.v1.OrganizationType organization_type = 1 [(buf.validate.field) = {
    enum: {
      defined_only: true
      not_in: [0]
    }
  }];
  
  // 组织 ID
  int64 organization_id = 2 [(buf.validate.field) = {
    int64: {gt: 0}
  }];
  
  // 过滤条件
  optional Filter filter = 3;
  
  // 分页信息
  PaginationRef pagination = 4;
  
  // 过滤条件
  message Filter {
    // 分类 ID 列表
    repeated int64 category_ids = 1 [(buf.validate.field) = {
      repeated: {
        min_items: 0,
        max_items: 100,
        unique: true,
        items: {
          int64: {gt: 0}
        }
      }
    }];
    
    // 状态过滤
    repeated AddOn.Status statuses = 3 [(buf.validate.field) = {
      repeated: {
        min_items: 0,
        max_items: 10,
        unique: true,
        items: {
          enum: {
            defined_only: true
            not_in: [0]
          }
        }
      }
    }];
    
    // 来源过滤
    repeated OfferingSource sources = 4 [(buf.validate.field) = {
      repeated: {
        min_items: 0,
        max_items: 10,
        unique: true,
        items: {
          enum: {
            defined_only: true
            not_in: [0]
          }
        }
      }
    }];

    // 关键词搜索（名称）
    optional string keyword = 6 [(buf.validate.field) = {
      string: {max_len: 100}
    }];
  }
}

// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: 使用 PaginationRef 替代 --)
// (-- api-linter: core::0132::response-unknown-fields=disabled
//     aip.dev/not-precedent: 额外返回 total. --)
// 列表查询 AddOn 响应
message ListAddOnsResponse {
  // AddOn 列表
  repeated AddOn add_ons = 1;
  
  // 分页信息
  optional PaginationRef pagination = 2;
  
  // 总数
  int32 total = 3;
}

// (-- api-linter: core::0234::request-unknown-fields=disabled
//     aip.dev/not-precedent: 需要租户信息 --)
// (-- api-linter: core::0234::request-requests-field=disabled
//     aip.dev/not-precedent: 不复用 UpdateRequest 结构 --)
// (-- api-linter: core::0234::request-parent-field=disabled
//     aip.dev/not-precedent: 无 parent 语义 --)
// 批量更新 AddOn 请求
message BatchUpdateAddOnsRequest {
  // 当前租户类型
  backend.proto.organization.v1.OrganizationType organization_type = 1 [(buf.validate.field) = {
    enum: {
      defined_only: true
      not_in: [0]
    }
  }];
  
  // 当前租户 ID
  int64 organization_id = 2 [(buf.validate.field) = {
    int64: {gt: 0}
  }];
  
  // AddOn 更新配置列表
  repeated AddOnUpdateDef update_add_ons = 3 [(buf.validate.field) = {
    repeated: {
      min_items: 1,
      max_items: 1000
    }
  }];
}

// AddOn 创建配置定义
message AddOnCreateDef {
  // 组织类型
  backend.proto.organization.v1.OrganizationType organization_type = 1 [(buf.validate.field) = {
    required: true,
    enum: {
      defined_only: true
      not_in: [0]
    }
  }];
  
  // 组织 ID
  int64 organization_id = 2 [(buf.validate.field) = {
    required: true,
    int64: {gt: 0}
  }];
  
  // 是否必需员工
  bool is_required_staff = 3;
  
  // 分类 ID（可选）
  optional int64 category_id = 4 [(buf.validate.field) = {
    int64: {gt: 0}
  }];
  
  // AddOn 名称
  string name = 5 [(buf.validate.field) = {
    required: true,
    string: {
      min_len: 1,
      max_len: 100
    }
  }];
  
  // 描述（可选）
  optional string description = 6 [(buf.validate.field) = {
    string: {max_len: 1000}
  }];
  
  // 颜色代码
  string color_code = 7 [(buf.validate.field) = {
    required: true,
    string: {
      min_len: 1,
      max_len: 10
    }
  }];
  
  // 图片列表（可选）
  repeated string images = 8 [(buf.validate.field) = {
    repeated: {
      min_items: 0,
      max_items: 1000,
      unique: true,
      items: {
        string: {max_len: 1024}
      }
    }
  }];
  
  // 状态
  AddOn.Status status = 9 [(buf.validate.field) = {
    required: true,
    enum: {
      defined_only: true
      not_in: [0]
    }
  }];
  
  // 来源
  OfferingSource source = 10 [(buf.validate.field) = {
    required: true,
    enum: {
      defined_only: true
      not_in: [0]
    }
  }]; 
  
  // 可用业务范围
  AvailableBusiness available_business = 11 [(buf.validate.field) = {
    required: true
  }];
  
  // 适用服务配置
  ApplicableService applicable_service = 12 [(buf.validate.field) = {
    required: true
  }];

  // 时长。单位：分钟
  int32 duration = 13 [(buf.validate.field) = {
    int32: {gt: 0}
  }];

  // The pet type and breed scope for this add on.
  AvailablePetTypeBreed available_type_breed = 14 [(buf.validate.field) = {
    required: true
  }];

  // The pet size scope for this add on.
  AvailablePetSize available_pet_size = 15 [(buf.validate.field) = {
    required: true
  }];

  // The pet coat type scope for this add on.
  AvailableCoatType available_coat_type = 16 [(buf.validate.field) = {
    required: true
  }];

}

// AddOn 更新配置定义
message AddOnUpdateDef {
  // AddOn ID
  int64 id = 1 [(buf.validate.field) = {
    int64: {gt: 0}
  }];
  
  // 是否必需员工（可选）
  optional bool is_required_staff = 2;
  
  // 分类 ID（可选）
  optional int64 category_id = 3 [(buf.validate.field) = {
    int64: {gt: 0}
  }];
  
  // AddOn 名称（可选）
  optional string name = 4 [(buf.validate.field) = {
    string: {
      min_len: 1,
      max_len: 100
    }
  }];
  
  // 描述（可选）
  optional string description = 5 [(buf.validate.field) = {
    string: {max_len: 1000}
  }];
  
  // 颜色代码（可选）
  optional string color_code = 6 [(buf.validate.field) = {
    string: {
      min_len: 1,
      max_len: 10
    }
  }];
  
  // 排序值（可选）
  optional int64 sort = 7 [(buf.validate.field) = {
    int64: {gt: 0}
  }];
  
  // 图片列表（可选）
  repeated string images = 8 [(buf.validate.field) = {
    repeated: {
      min_items: 0,
      max_items: 1000,
      unique: true,
      items: {
        string: {max_len: 1024}
      }
    }
  }];
  
  // 状态（可选）
  optional AddOn.Status status = 9 [(buf.validate.field) = {
    enum: {
      defined_only: true
      not_in: [0]
    }
  }];
  
  // 可用业务范围（可选）
  optional AvailableBusiness available_business = 10;
  
  // 适用服务配置（可选）
  optional ApplicableService applicable_service = 11;

  // 时长（可选）单位：分钟
  optional int32 duration = 12 [(buf.validate.field) = {
    int32: {gt: 0}
  }];

  // The pet type and breed scope for this add on.
  optional AvailablePetTypeBreed available_type_breed = 14;

  // The pet size scope for this add on.
  optional AvailablePetSize available_pet_size = 15;

  // The pet coat type scope for this add on.
  optional AvailableCoatType available_coat_type = 16;
}

// (-- api-linter: core::0234::response-resource-field=disabled
//     aip.dev/not-precedent: 不返回 AddOn 类型 --)
// 批量更新 AddOn 响应
message BatchUpdateAddOnsResponse {}

// (-- api-linter: core::0132::request-parent-required=disabled
//     aip.dev/not-precedent: no need parent --)
// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: no need page --)
// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: no need page --)
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: no need page --)
// 获取 AddOn 分类列表请求
message ListAddOnCategoriesRequest{
  // 租户类型
  backend.proto.organization.v1.OrganizationType organization_type = 1;
  // 租户 ID
  int64 organization_id = 2;

  // 过滤条件
  Filter filter = 3;

  // 过滤条件
  message Filter {
    // 分类 ID 列表
    repeated int64 category_ids = 1;
  }
}

// (-- api-linter: core::0132::response-unknown-fields=disabled
  //     aip.dev/not-precedent: 命名简化 --)
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: no need page --)
// 获取 AddOn 分类列表响应
message ListAddOnCategoriesResponse{
  // 分类列表
  repeated AddOnCategory categories = 1;
}

// 保存 AddOn 分类请求
message SaveAddOnCategoriesRequest{
  // 租户类型
  backend.proto.organization.v1.OrganizationType organization_type = 1;
  // 租户 ID
  int64 organization_id = 2;

  // 新增的分类列表
  repeated CategoryCreateDef create_categories = 3 ;
  // 更新的分类列表
  repeated CategoryUpdateDef update_categories = 4 ;
  // 删除的分类 ID 列表
  repeated int64 delete_ids = 5 ;

  // 更新分类请求
  message CategoryUpdateDef {
    // 分类 ID
    int64 id = 1;
    
    // 分类名称
    string name = 2;

    // 排序值
    int64 sort = 3;
  }

  // 创建分类请求
  message CategoryCreateDef {
    // 分类名称
    string name = 1;

    // 排序值
    int64 sort = 2;
  }
}

// 保存 AddOn 分类响应
message SaveAddOnCategoriesResponse{
}
