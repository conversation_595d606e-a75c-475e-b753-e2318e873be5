import { isNormal, useAsync, useSerialCallback } from '@moego/finance-utils';
import { type KitModelType, type RealmType } from '@moego/finance-web-kit';
import { MinorMoreOutlined, MinorSendOutlined } from '@moego/icons-react';
import { Condition, Dropdown, IconButton, Text, Tooltip } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import { T_SECOND } from 'monofile-utilities/lib/consts';
import React, { useContext, useMemo } from 'react';
import { type FinanceKit } from '../../../../service/finance-kit';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { getInvoiceReceiptSentInfo } from '../../../../store/message/message.actions';
import { TARGET_TYPE_RECEIPT, invoiceSentInfoBox } from '../../../../store/message/message.boxes';
import { PaymentActionName } from '../../../../utils/reportData/payment';
import { useInvoiceReinventReport } from '../../hooks/useInvoiceReinvent.report';
import { ViewOrderDrawerContext } from '../ViewOrderDrawer.context';

export interface OrderActionsProps {
  refundOrder: KitModelType<typeof FinanceKit.model, RealmType.RefundOrder>;
}

interface LocalMenuItem {
  isDisabled?: boolean;
  title: string;
  key: string;
  render?: (order: KitModelType<typeof FinanceKit.model, RealmType.RefundOrder>) => React.ReactNode;
}
const DropdownMenuOptionEnum: Record<string, LocalMenuItem> = {
  sendReceipt: {
    isDisabled: false,
    title: 'Send receipt',
    key: 'sendReceipt',
    render: (refundOrder) => {
      // eslint-disable-next-line react-hooks/rules-of-hooks
      const [business, invoiceReceiptSentInfo] = useSelector(
        selectCurrentBusiness,
        invoiceSentInfoBox.mustGetItem(+refundOrder.id),
      );
      return (
        <Tooltip
          isDisabled={!invoiceReceiptSentInfo.createTime}
          content={`Receipt sent at ${business.formatDateTime(invoiceReceiptSentInfo.createTime * T_SECOND)}`}
          side="top"
        >
          <Text variant="regular-short" className="moe-text-s-20 moe-flex moe-items-center moe-gap-1">
            <MinorSendOutlined />
            {invoiceReceiptSentInfo.createTime ? 'Resend receipt' : 'Send receipt'}
          </Text>
        </Tooltip>
      );
    },
  },
} as const;

export const RefundOrderActions = (props: OrderActionsProps) => {
  const { refundOrder } = props;
  const dispatch = useDispatch();
  const context = useContext(ViewOrderDrawerContext);
  const canSendReceipt = refundOrder.isCompleted;
  const handleSelectionChange = useSerialCallback(async (keys: Set<string>) => {
    const arr = Array.from(keys);
    if (arr.includes(DropdownMenuOptionEnum.sendReceipt.key)) {
      await handleSendReceipt();
    }
  });
  const reportPaymentData = useInvoiceReinventReport();

  const dropdownItems = useMemo(() => {
    const list: LocalMenuItem[] = [];
    if (canSendReceipt) {
      list.push(DropdownMenuOptionEnum.sendReceipt);
    }
    return list;
  }, [canSendReceipt]);

  const handleSendReceipt = useSerialCallback(() => {
    reportPaymentData(PaymentActionName.InvoiceSendReceipt, {
      orderId: refundOrder.id,
      ctaId: 'drawer_payment_activity',
    });
    context?.onSendReceipt(refundOrder);
  });

  useAsync(async () => {
    if (!isNormal(refundOrder.id)) {
      return;
    }

    await dispatch(getInvoiceReceiptSentInfo(TARGET_TYPE_RECEIPT, refundOrder.id as unknown as number));
  }, [refundOrder.id]);

  return (
    <div className={'moe-flex moe-items-center moe-gap-s moe-flex-wrap'}>
      <Condition if={dropdownItems.length > 0}>
        <Dropdown defaultOpen={false}>
          <Dropdown.Trigger>
            <IconButton variant="secondary" size={'m'} icon={<MinorMoreOutlined />} />
          </Dropdown.Trigger>
          <Dropdown.Menu
            selectedKeys={[]}
            onSelectionChange={(keys) => {
              if (typeof keys !== 'string' && keys.size > 0) {
                handleSelectionChange(keys as Set<string>);
              }
            }}
          >
            {dropdownItems.map((item) => {
              if (item.render) {
                return (
                  <Dropdown.MenuItem key={item.key} textValue={item.title}>
                    {item.render(refundOrder)}
                  </Dropdown.MenuItem>
                );
              }
              return <Dropdown.MenuItem key={item.key} title={item.title} />;
            })}
          </Dropdown.Menu>
        </Dropdown>
      </Condition>
    </div>
  );
};
