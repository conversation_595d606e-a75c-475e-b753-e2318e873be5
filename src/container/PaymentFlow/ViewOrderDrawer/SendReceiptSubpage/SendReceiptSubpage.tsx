import { isNormal, useAsync, useSerialCallback } from '@moego/finance-utils';
import { MinorLeftArrowOutlined } from '@moego/icons-react';
import { Button, IconButton, toast } from '@moego/ui';
import { useDispatch } from 'amos';
import React from 'react';
import { WithPricingEnableUpgrade } from '../../../../components/Pricing/WithPricingComponents';
import {
  getInvoiceReceiptSentInfo,
  getTemplateMessagePreview,
  sendInvoiceEmailReceipt,
  sendMessageToCustomer,
} from '../../../../store/message/message.actions';
import { MessageDetailType, TARGET_TYPE_RECEIPT } from '../../../../store/message/message.boxes';
import { useRefObject } from '../../../../utils/hooks/hooks';
import { LayoutSubpage } from '../../TakePaymentDrawer/components/Layout/LayoutSubpage';
import { LayoutSubpageFooter } from '../../TakePaymentDrawer/components/Layout/LayoutSubpageFooter';
import { LayoutSubpageHeader } from '../../TakePaymentDrawer/components/Layout/LayoutSubpageHeader';
import { LayoutSubpageScrollBody } from '../../TakePaymentDrawer/components/Layout/LayoutSubpageScrollBody';
import { type OrderContextModel } from '../../hooks/OrderContext';
import { ReceiptSendChannel } from './SendReceipt.utils';
import { SendReceiptForm, type SendReceiptFormRef } from './SendReceiptForm';

interface SendReceiptSubpageProps {
  className?: string;
  order: OrderContextModel['order'] | null;
  onClose: () => void;
}
export const SendReceiptSubpage = (props: SendReceiptSubpageProps) => {
  const { className, order, onClose } = props;
  const orderId = order?.id;
  const dispatch = useDispatch();
  const formRef = useRefObject<SendReceiptFormRef>();

  const { value: messageTemplate } = useAsync(async () => {
    if (!isNormal(orderId)) {
      return;
    }

    const template = await dispatch(
      getTemplateMessagePreview(TARGET_TYPE_RECEIPT, orderId, MessageDetailType.Text, {
        groomingId: order!.groomingId,
      }),
    );
    return template;
  }, [orderId]);

  const handleBack = useSerialCallback(() => {
    onClose();
  });

  const handleSend = useSerialCallback(async (onCapture: (() => void) | null) => {
    if (!formRef.current) {
      return;
    }

    const isValid = await formRef.current?.validate();
    if (!isValid) {
      toast({
        type: 'error',
        title: 'Please check your input',
      });
      return;
    }

    const values = formRef.current?.getValues();
    switch (values.sendChannel) {
      case ReceiptSendChannel.MESSAGE:
        if (onCapture) {
          onCapture();
          return;
        }

        await dispatch(
          sendMessageToCustomer(
            order!.customerId,
            values.message,
            values.contactId,
            undefined,
            TARGET_TYPE_RECEIPT,
            orderId,
          ),
        );
        toast({
          type: 'success',
          title: 'Message has been sent successfully!',
        });
        break;
      case ReceiptSendChannel.EMAIL:
        await dispatch(sendInvoiceEmailReceipt({ orderId, recipientEmail: values.email, type: order!.type }));
        toast({
          type: 'success',
          title: 'The email has been sent successfully!',
        });
        break;
    }

    // refresh send info
    dispatch(getInvoiceReceiptSentInfo(TARGET_TYPE_RECEIPT, orderId as number));
    onClose();
  });

  const handleCancel = useSerialCallback(async () => {
    onClose();
  });

  if (!orderId) {
    return null;
  }

  return (
    <WithPricingEnableUpgrade newUI permission={'twoWay' as any}>
      {(onCapture) => (
        <LayoutSubpage className={className}>
          <LayoutSubpageHeader
            prefix={<IconButton className="moe-mr-xs" icon={<MinorLeftArrowOutlined onClick={handleBack} />} />}
            title={`Send receipt for Invoice #${orderId}`}
          />
          <LayoutSubpageScrollBody>
            <SendReceiptForm
              ref={formRef}
              customerId={order.customerId}
              message={messageTemplate}
              email={order.customerEmail}
              title="Send receipt"
            />
          </LayoutSubpageScrollBody>
          <LayoutSubpageFooter className="moe-flex moe-justify-between moe-gap-s moe-border-none moe-min-h-8px-900">
            <Button variant="secondary" onPress={handleCancel} className="moe-w-1/2">
              Cancel
            </Button>
            <Button className="moe-w-1/2" onPress={() => handleSend(onCapture)} isLoading={handleSend.isBusy()}>
              Send
            </Button>
          </LayoutSubpageFooter>
        </LayoutSubpage>
      )}
    </WithPricingEnableUpgrade>
  );
};
