import {
  type BatchQuickCheckInParams,
  type CalculateAppointmentInvoiceParams,
  type CreateAppointmentParams,
  type GetAppointmentLodgingParams,
  type GetAppointmentParams,
  type GetCustomerLastAppointmentParams,
  type RescheduleBoardingAppointmentParams,
  type UpdateAppointmentParams,
} from '@moego/api-web/moego/api/appointment/v1/appointment_api';
import {
  type LodgingAssignParams,
  type RescheduleAppointmentParams,
  type RescheduleBoardingServiceParams,
  type ReschedulePetDetailsParams,
  type SwitchAllPetsStartAtSameTimeParams,
} from '@moego/api-web/moego/api/appointment/v1/appointment_schedule_api';
import { type GetLodgingListParams } from '@moego/api-web/moego/api/appointment/v1/lodging_api';
import {
  type CountPetDetailParams,
  type DeletePetParams,
  type SaveOrUpdatePetDetailsParams,
} from '@moego/api-web/moego/api/appointment/v1/pet_detail_api';
import { type GetApplicableServiceListParams } from '@moego/api-web/moego/api/offering/v1/service_api';
import {
  type ListEvaluationStaffsParams,
  type ListServiceStaffsParams,
} from '@moego/api-web/moego/api/offering/v1/service_staff_api';
import { type ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { convertMoneyToMoeMoney } from '@moego/finance-utils';
import { action } from 'amos';
import ReactDOM from 'react-dom';
import { getMainCareType } from '../../../components/PetAndServicePicker/utils/getMainCareType';
import {
  transformCategoryList,
  transformCommonCategories,
} from '../../../components/ServiceApplicablePicker/utils/getMultipleServiceExactData';
import {
  AppointmentScheduleClient,
  AppointmentServiceClient,
  LodgingClient,
  PetDetailClient,
  ServiceManagementClient,
  ServiceStaffClient,
} from '../../../middleware/clients';
import { currentBusinessIdBox } from '../../../store/business/business.boxes';
import { EvaluationStaffListBox, evaluationServiceMapBox } from '../../../store/evaluation/evaluation.boxes';
import { lodgingUnitMapBox } from '../../../store/lodging/lodgingUnit.boxes';
import {
  type SaveCustomerActiveSubscriptionListParam,
  saveCustomerActiveSubscriptionList,
} from '../../../store/membership/membership.actions';
import { createAppointmentObservableAction } from '../../../store/observableServices/observableServices';
import { petMapBox } from '../../../store/pet/pet.boxes';
import { saveAppointmentPricingRuleDetail } from '../../../store/pricingRule/legacy/pricingRule.actions';
import { AppointmentPricingRuleRecord } from '../../../store/pricingRule/legacy/pricingRule.boxes';
import { isNormal } from '../../../store/utils/identifier';
import { autoParallelTask } from '../../../utils/autoParallelTask';
import { IgnoreErrorCode } from '../../../utils/createObservableActionFactory';
import { apptAddOnMapBox, apptInfoMapBox, apptPetMapBox, apptServiceMapBox } from './appt.boxes';
import { matchApptFlowScene } from './appt.options';
import { transformServiceDetail } from './appt.transform';
import { ApptFlowScene } from './appt.types';
import { revertPricingRuleInService } from '../../../store/pricingRule/legacy/pricingRule.utils';

const SERVICE_APPLICABLE_STAFF_PAGE_SIZE = 1000;

export const createAppointment = createAppointmentObservableAction(
  'createAppointment',
  async (_dispatch, _select, input: CreateAppointmentParams) => {
    return await AppointmentServiceClient.createAppointment(input);
  },
);

export const updateAppointment = createAppointmentObservableAction(
  'updateAppointment',
  async (_dispatch, _select, input: UpdateAppointmentParams) => {
    return await AppointmentServiceClient.updateAppointment(input);
  },
);

export const rescheduleAppointment = createAppointmentObservableAction(
  'rescheduleAppointment',
  async (_dispatch, _select, input: RescheduleAppointmentParams) => {
    return await AppointmentScheduleClient.rescheduleAppointment(input);
  },
);

export const switchAllPetsStartAtSameTime = action(
  async (_dispatch, _select, input: SwitchAllPetsStartAtSameTimeParams) => {
    return await AppointmentScheduleClient.switchAllPetsStartAtSameTime(input);
  },
);

export const getCustomerLastAppointment = action(
  async (_dispatch, _select, input: GetCustomerLastAppointmentParams) => {
    return await AppointmentServiceClient.getCustomerLastAppointment(input);
  },
);

export const updatePetService = createAppointmentObservableAction(
  'updatePetService',
  async (_dispatch, _select, input: SaveOrUpdatePetDetailsParams) => {
    // 添加数据一致性验证
    input.petDetails?.forEach((petDetail, petIndex) => {
      petDetail.services?.forEach((service, serviceIndex) => {
        const { enableOperation, operations } = service;
        const actualOperations = operations || [];

        // 检查数据一致性
        if (enableOperation && actualOperations.length === 0) {
          console.warn(
            `Data inconsistency detected: Pet ${petIndex + 1}, Service ${serviceIndex + 1} - enableOperation is true but operations array is empty. Auto-fixing...`,
          );
          // 自动修复：将 enableOperation 设置为 false
          service.enableOperation = false;
        } else if (!enableOperation && actualOperations.length > 0) {
          console.warn(
            `Data inconsistency detected: Pet ${petIndex + 1}, Service ${serviceIndex + 1} - enableOperation is false but operations array is not empty. Auto-fixing...`,
          );
          // 自动修复：将 enableOperation 设置为 true
          service.enableOperation = true;
        }
      });
    });

    return await PetDetailClient.saveOrUpdatePetDetails(input as SaveOrUpdatePetDetailsParams);
  },
);

export const deletePetService = createAppointmentObservableAction(
  'deletePetService',
  async (_dispatch, _select, input: DeletePetParams) => {
    return await PetDetailClient.deletePet(input);
  },
  // ignore missing pet detail error
  { ignoreError: [3] },
);

export interface GetAppointmentApiParams extends GetAppointmentParams {
  serviceItemTypes?: ServiceItemType[];
}
export const getAppointment = createAppointmentObservableAction(
  'getAppointment',
  async (dispatch, _select, input: GetAppointmentApiParams) => {
    const [res, petFeedings] = await autoParallelTask(
      [
        {
          condition: ({ appointmentId }) => isNormal(appointmentId),
          run: async (params, setParams) => {
            const res = await AppointmentServiceClient.getAppointment({ appointmentId: params.appointmentId });
            setParams({ serviceItemTypes: res.serviceItemTypes });
            return res;
          },
        },
        {
          condition: ({ appointmentId, serviceItemTypes }) =>
            isNormal(appointmentId) && Boolean(serviceItemTypes?.length),
          run: async (params) => {
            const { appointmentId, serviceItemTypes = [] } = params;
            if (matchApptFlowScene(ApptFlowScene.FeedingMedication, getMainCareType(serviceItemTypes))) {
              return await AppointmentScheduleClient.getPetFeedingMedicationSchedules({ appointmentId });
            }
          },
        },
      ],
      input,
    );
    const { appointmentId } = input;

    if (!res) {
      throw new Error(`Failed to load appointment data for appointmentId: ${appointmentId}`);
    }

    const { serviceDetail, pricingRuleApplyLogsV2 } = res;
    /**
     * 修复价格可能计算错误的 bug
     * 当有 pricing rule 生效时上传原价这样就不会重复计算
     *
     * @see revertPricingRuleInService
     */
    const serviceDetailRemovePricingRule = revertPricingRuleInService(serviceDetail, pricingRuleApplyLogsV2);
    const { pets, apptAddOns, apptServices, evaluations } = transformServiceDetail(
      serviceDetailRemovePricingRule,
      petFeedings,
    );

    const membershipSubscriptionInfo: SaveCustomerActiveSubscriptionListParam[] = [
      {
        customerId: res.customer.customerProfile.id,
        membershipSubscriptions: res.membershipSubscriptions?.membershipSubscriptions || [],
      },
    ];

    // TODO: move out of the api layer
    ReactDOM.unstable_batchedUpdates(() => {
      dispatch([
        apptPetMapBox.mergeItem(appointmentId, { pets: pets }),
        apptServiceMapBox.mergeItems(apptServices),
        apptAddOnMapBox.mergeItems(apptAddOns),
        apptInfoMapBox.mergeItem(appointmentId, convertMoneyToMoeMoney(res)),
        saveCustomerActiveSubscriptionList(membershipSubscriptionInfo),
        saveAppointmentPricingRuleDetail(
          appointmentId,
          pricingRuleApplyLogsV2.map((detail) => {
            return {
              petId: detail.petId,
              serviceId: detail.serviceId,
              pricingRule: detail.pricingRule,
              servicePrice: detail.adjustedPrice,
              originalPrice: detail.originalPrice,
              ownKey: AppointmentPricingRuleRecord.ownKey(detail.petId, detail.serviceId),
            };
          }),
        ),
        petMapBox.mergeItems(serviceDetail.map((s) => ({ petId: Number(s.pet.id), deleted: s.pet.deleted }))), // 跨tab操作时，会有这种case，所以我们尽量及时更新 pet 状态
      ]);
      // 由于 evaluation 的 staffId 有可能为空 (No assigned staff)，因此每次 load 都需要替换，而非 merge
      dispatch(evaluations.map((item) => evaluationServiceMapBox.setItem(item.ownId, item)));
    });
    return res;
  },
);

export const scheduleBoardingAppointment = createAppointmentObservableAction(
  'scheduleBoardingAppointment',
  async (_dispatch, _select, input: RescheduleBoardingServiceParams) => {
    const res = await AppointmentScheduleClient.rescheduleBoardingService(input);
    return res;
  },
);

export const scheduleAppointmentPetDetails = createAppointmentObservableAction(
  'scheduleAppointmentPetDetails',
  async (_dispatch, _select, input: ReschedulePetDetailsParams) => {
    const res = await AppointmentScheduleClient.reschedulePetDetails(input);
    return res;
  },
);

export const getAppointmentService = createAppointmentObservableAction(
  'getAppointmentService',
  async (_dispatch, _select, input: GetApplicableServiceListParams, signal?: AbortSignal) => {
    const { categoryList } = await ServiceManagementClient.getApplicableServiceList(input, {
      signal,
    });

    return transformCategoryList(categoryList);
  },
);

export const getMultiplePetsAppointmentService = createAppointmentObservableAction(
  'getMultiplePetsAppointmentService',
  async (_dispatch, _select, input: GetApplicableServiceListParams, signal?: AbortSignal) => {
    const { commonCategories, petServices } = await ServiceManagementClient.getApplicableServiceList(input, {
      signal,
    });

    const transformedCommonCategories = transformCommonCategories(commonCategories);

    return {
      categoryList: transformedCommonCategories,
      commonCategories: transformedCommonCategories,
      petServices,
    };
  },
);

type PartialKeys<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

export const getServiceApplicableStaff = createAppointmentObservableAction(
  'getServiceApplicableStaff',
  async (_dispatch, select, input: PartialKeys<ListServiceStaffsParams, 'pagination' | 'businessId'>) => {
    const currentBusinessId = select(currentBusinessIdBox);
    const presetOptions: Pick<ListServiceStaffsParams, 'businessId' | 'pagination'> = {
      businessId: currentBusinessId.toString(),
      pagination: {
        pageSize: SERVICE_APPLICABLE_STAFF_PAGE_SIZE,
        pageNum: 1,
      },
    };

    const res = await ServiceStaffClient.listServiceStaffs({
      ...presetOptions,
      ...input,
    });

    return res;
  },
);

export const getEvaluationApplicableStaff = action(
  async (dispatch, select, input: PartialKeys<ListEvaluationStaffsParams, 'pagination' | 'businessId'>) => {
    const currentBusinessId = select(currentBusinessIdBox);
    const presetOptions: Pick<ListEvaluationStaffsParams, 'businessId' | 'pagination'> = {
      businessId: currentBusinessId.toString(),
      pagination: {
        pageSize: SERVICE_APPLICABLE_STAFF_PAGE_SIZE,
        pageNum: 1,
      },
    };
    if (!input.evaluationId) {
      return;
    }

    const res = await ServiceStaffClient.listEvaluationStaffs({
      ...presetOptions,
      ...input,
    });
    dispatch(
      EvaluationStaffListBox.setList(
        input.evaluationId,
        res.staffs.map((item) => item.id),
      ),
    );

    return res;
  },
);

export const getApptServiceLodgingList = createAppointmentObservableAction(
  'getApptServiceLodgingList',
  async (dispatch, select, input: GetLodgingListParams, signal?: AbortSignal) => {
    const businessId = select(currentBusinessIdBox);
    const res = await LodgingClient.getLodgingList(input, { signal });
    res?.lodgingUnitList?.forEach((lodging) => {
      dispatch(
        lodgingUnitMapBox.mergeItems(lodging.lodgingUnits.map((v) => ({ ...v, businessId: String(businessId) }))),
      );
    });
    return res;
  },
  { ignoreError: [IgnoreErrorCode.CanceledError] },
);

export const calculateAppointmentTotal = action(
  async (_dispatch, _select, input: CalculateAppointmentInvoiceParams, signal?: AbortSignal) => {
    return await AppointmentServiceClient.calculateAppointmentInvoice(input, { signal });
  },
);

export const getDaycareConflict = action(async (_dispatch, _select, input: CountPetDetailParams) => {
  return await PetDetailClient.countPetDetail(input);
});

export const sendQuickCheckIn = action(async (_dispatch, _select, input: BatchQuickCheckInParams) => {
  return await AppointmentServiceClient.batchQuickCheckIn(input);
});

export const getAppointmentLodging = action(async (_dispatch, _select, input: GetAppointmentLodgingParams) => {
  const res = await AppointmentServiceClient.getAppointmentLodging(input);
  return res;
});

export const updateAppointmentLodging = action(async (_dispatch, _select, input: LodgingAssignParams) => {
  const res = await AppointmentScheduleClient.lodgingAssign(input);
  return res;
});

export const rescheduleBoardingAppointment = action(
  async (_dispatch, _select, input: RescheduleBoardingAppointmentParams) => {
    const res = await AppointmentServiceClient.rescheduleBoardingAppointment(input);
    return res;
  },
);
