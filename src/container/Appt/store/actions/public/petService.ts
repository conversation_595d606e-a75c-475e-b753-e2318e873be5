import { ServiceItemType, ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { action } from 'amos';
import { type ServiceEntry } from '../../../../../components/ServiceApplicablePicker/types/serviceEntry';
import {
  ApptAddOnRecord,
  ApptServiceRecord,
  apptAddOnMapBox,
  apptPetMapBox,
  apptServiceMapBox,
} from '../../appt.boxes';
import { selectPetsInAppt, selectPlaceholderServiceInAppt } from '../../appt.selectors';
import {
  CreateApptId,
  type ApptPetEvaluationInfo,
  type ApptPetServiceInfo,
  type ApptServiceInfoRecord,
} from '../../appt.types';
import { getAssociatedId } from '../../appt.utils';

export const addApptPetService = action(
  async (
    dispatch,
    _select,
    petId: string | number,
    serviceList: ServiceEntry[],
    appointmentId: string = CreateApptId,
    removeFirst = false,
  ) => {
    removeFirst && dispatch(removeApptPetService(petId, appointmentId));
    dispatch(addApptPet(petId, appointmentId));
    const actions = serviceList.map((service) => {
      if (service.serviceType === ServiceType.SERVICE) {
        return addServicesForPet([service], String(petId), appointmentId);
      } else {
        return addAddOnsForPet([service], String(petId), appointmentId);
      }
    });
    dispatch(actions);
  },
);

export const addApptPetEvaluations = action(
  async (
    dispatch,
    _select,
    petId: string,
    evaluations: ApptPetEvaluationInfo[],
    appointmentId: string = CreateApptId,
  ) => {
    dispatch(
      apptPetMapBox.updateItem(appointmentId, (pre) => {
        return pre.merge({
          pets: pre.pets.map((pet) => {
            if (pet.petId === petId) {
              return { ...pet, evaluations };
            }
            return pet;
          }),
        });
      }),
    );
  },
);

/**
 * 同时给appointment中的多只pet添加service
 */
export const addApptMultiPetService = action(
  async (
    dispatch,
    _select,
    petServiceList: { petId: string | number; serviceList: ServiceEntry[] }[],
    appointmentId: string = CreateApptId,
    removeFirst = false,
  ) => {
    dispatch(
      petServiceList.map(({ petId, serviceList }) => addApptPetService(petId, serviceList, appointmentId, removeFirst)),
    );
  },
);

/**
 * 此方法不会真的删除 0-0-0 的service map
 * 而是将 petId 为 CreateApptId 的 pet remove
 */

export const deletePlaceholderService = action(async (dispatch, select) => {
  const pets = select(selectPetsInAppt(CreateApptId, false));
  if (pets.some((v) => v.petId === CreateApptId)) {
    dispatch(
      apptPetMapBox.updateItem(CreateApptId, (prev) => {
        return prev.merge({ pets: prev.pets.filter((v) => v.petId !== CreateApptId) });
      }),
    );
  }
});

export const deleteApptAdditionalServiceAddOns = action(
  async (dispatch, select, appointmentId: string = CreateApptId) => {
    const pets = select(selectPetsInAppt(appointmentId, false));
    const newPetsInfo = pets.map((pet) => {
      const { services } = pet;
      const removeServiceList = services.filter((service) => service.isAdditionalService);
      const newPet = {
        ...pet,
        services: services.filter((service) => !service.isAdditionalService),
      };
      const actions = removeServiceList.map((service) => {
        if (service.serviceType === ServiceType.SERVICE) {
          return apptServiceMapBox.deleteItem(ApptServiceRecord.createOwnId(appointmentId, service.id));
        } else {
          return apptAddOnMapBox.deleteItem(ApptAddOnRecord.createOwnId(appointmentId, service.id));
        }
      });
      return {
        newPet,
        actions,
      };
    });
    const newPets = newPetsInfo.map((item) => item.newPet);
    const actions = newPetsInfo.flatMap((item) => item.actions);
    dispatch([
      ...actions,
      apptPetMapBox.updateItem(appointmentId, (prev) => {
        return prev.merge({
          pets: newPets,
        });
      }),
    ]);
  },
);

export const deleteApptServiceAddOnsByIdList = action(
  async (dispatch, select, appointmentId: string = CreateApptId, idList: string[] = []) => {
    const pets = select(selectPetsInAppt(appointmentId, false));
    const newPetsInfo = pets.map((pet) => {
      const { services } = pet;
      const removeServiceList = services.filter((service) => idList.includes(service.id));
      const newPet = {
        ...pet,
        services: services.filter((service) => !idList.includes(service.id)),
      };
      const actions = removeServiceList.map((service) => {
        if (service.serviceType === ServiceType.SERVICE) {
          return apptServiceMapBox.deleteItem(ApptServiceRecord.createOwnId(appointmentId, service.id));
        } else {
          return apptAddOnMapBox.deleteItem(ApptAddOnRecord.createOwnId(appointmentId, service.id));
        }
      });
      return {
        newPet,
        actions,
      };
    });
    const newPets = newPetsInfo.map((item) => item.newPet);
    const actions = newPetsInfo.flatMap((item) => item.actions);
    dispatch([
      ...actions,
      apptPetMapBox.updateItem(appointmentId, (prev) => {
        return prev.merge({
          pets: newPets,
        });
      }),
    ]);
  },
);

/**
 * 直接修改 0-0-0 placeholder service 的数据
 */

export const setPlaceholderService = action(async (dispatch, _select, input: Partial<ApptServiceInfoRecord>) => {
  dispatch(apptServiceMapBox.mergeItem(ApptServiceRecord.createOwnId(CreateApptId, CreateApptId), input));
  if (input.serviceItemType) {
    dispatch(setPlaceholderPet(input.serviceItemType));
  }
});

export const setPlaceholderPet = action(async (dispatch, select, serviceItemType: ServiceItemType) => {
  const pets = select(selectPetsInAppt(CreateApptId, false));
  if (pets.every((v) => v.petId === CreateApptId)) {
    const services = [{ id: CreateApptId, serviceId: CreateApptId, serviceType: ServiceType.SERVICE, serviceItemType }];
    dispatch(
      apptPetMapBox.mergeItem(CreateApptId, {
        pets: [{ petId: CreateApptId, services, evaluations: [], petCodes: [] }],
      }),
    );
  }
});

/**
 * 清空所有 pet service 数据，例如 drawer 关闭，或者完成 new appt 后
 */
export const clearCreateApptData = action(async (dispatch, select, appointmentId: string = CreateApptId) => {
  const pets = select(selectPetsInAppt(appointmentId, false));
  dispatch(clearPetsInAppt(pets));
  dispatch([apptPetMapBox.deleteItem(appointmentId), deletePlaceholderService()]);
});

export const clearPetsInAppt = action(
  async (dispatch, select, pets: ApptPetServiceInfo[], appointmentId: string = CreateApptId) => {
    const actions = pets
      .map((pet) => {
        const { services } = pet;
        return services.map((service) => {
          if (service.serviceType === ServiceType.SERVICE) {
            return apptServiceMapBox.deleteItem(ApptServiceRecord.createOwnId(appointmentId, service.id));
          } else {
            return apptAddOnMapBox.deleteItem(ApptAddOnRecord.createOwnId(appointmentId, service.id));
          }
        });
      })
      .flat();
    dispatch([
      ...actions,
      apptPetMapBox.updateItem(appointmentId, (prev) => {
        const newPets = prev.pets.filter((v) => pets.every((c) => c.petId !== v.petId));
        return prev.merge({ pets: newPets });
      }),
    ]);

    if (appointmentId === CreateApptId && !select(selectPetsInAppt(CreateApptId, false)).length) {
      // 这里是我们全局重置占位 service 的地方.
      // 当进入appt create 流程时，我们会将placeholder 加入 appt pet
      // 我们在某些场景，会删除掉全部的 pets，这个时候，我们应该更新 appt pet 的状态
      const { serviceItemType } = select(selectPlaceholderServiceInAppt());
      dispatch(setPlaceholderPet(serviceItemType));
    }
  },
);
export const addApptPet = action(
  async (dispatch, select, petId: string | number, appointmentId: string = CreateApptId) => {
    const newPetId = String(petId);
    const pets = select(selectPetsInAppt(appointmentId, false));
    const existPet = pets.find((pet) => pet.petId === newPetId);
    if (existPet) {
      return;
    }
    const nextPets = pets.concat({
      petId: newPetId,
      services: [],
      evaluations: [],
      petCodes: [],
    });

    dispatch(apptPetMapBox.mergeItem(appointmentId, { pets: nextPets }));
  },
); /** add services for target pet */

export const addServicesForPet = action(
  async (dispatch, select, serviceList: ServiceEntry[], petId: string, appointmentId: string = CreateApptId) => {
    const pets = select(selectPetsInAppt(appointmentId, false));
    let services: ApptServiceInfoRecord[] = [];
    const nextPets = pets.map((pet) => {
      if (pet.petId === petId) {
        services = serviceList.map((service) => {
          return ApptServiceRecord.createFromPetService(service, petId, appointmentId);
        });
        return getNextPetServices(pet, serviceList);
      }
      return pet;
    });
    dispatch([apptPetMapBox.mergeItem(appointmentId, { pets: nextPets }), apptServiceMapBox.mergeItems(services)]);
  },
);
/** add addOns for target pet */

export const addAddOnsForPet = action(
  async (dispatch, select, serviceList: ServiceEntry[], petId: string, appointmentId: string = CreateApptId) => {
    const pets = select(selectPetsInAppt(appointmentId, false));
    let addOns: ApptAddOnRecord[] = [];
    const next = pets.map((pet) => {
      if (pet.petId === petId) {
        addOns = serviceList.map((service) => {
          return ApptAddOnRecord.createFromPetService(service, petId, appointmentId);
        });
        return getNextPetServices(pet, serviceList);
      }
      return pet;
    });
    dispatch([apptPetMapBox.mergeItem(appointmentId, { pets: next }), apptAddOnMapBox.mergeItems(addOns)]);
  },
);
export const getNextPetServices = (pet: ApptPetServiceInfo, serviceList: ServiceEntry[]) => {
  const newServices = pet.services.concat(
    serviceList.map((s) => ({
      serviceType: s.serviceType,
      serviceItemType: s.serviceItemType || ServiceItemType.GROOMING,
      id: s.id,
      serviceId: String(s.serviceId),
      associatedId: getAssociatedId(s.associatedId),
    })),
  );
  return { ...pet, services: newServices };
};
export const removeApptPetService = action(
  async (dispatch, select, petId: string | number, appointmentId: string = CreateApptId) => {
    const targetPetId = String(petId);
    const pets = select(selectPetsInAppt(appointmentId, false));
    const existPetIndex = pets.findIndex((pet) => pet.petId === targetPetId);
    if (existPetIndex === -1) {
      return;
    }

    dispatch(
      apptPetMapBox.mergeItem(appointmentId, {
        pets: pets.map((pet) => {
          if (pet.petId === targetPetId) {
            return {
              ...pet,
              services: [],
            };
          }
          return pet;
        }),
      }),
    );
  },
);
