import { action } from 'amos';
import * as validator from 'validator';
import { http } from '../../middleware/api';
import { AgreementClient } from '../../middleware/clients';
import { type OpenApiModels } from '../../openApi/schema';
import { currentAccountIdBox } from '../account/account.boxes';
import { selectCurrentAccount } from '../account/account.selectors';
import { currentBusinessIdBox } from '../business/business.boxes';
import { BusinessType } from '../business/business.options';
import { currentCompanyIdBox } from '../company/company.boxes';
import { isNormal } from '../utils/identifier';
import {
  type CareAgreementSignModel,
  type CareFormModel,
  type CareSetupDetailRecord,
  careAgreementMapBox,
  careAgreementSignRecordMapBox,
  careFormMapBox,
  careOrderPreviewMapBox,
  careSetupDetailMapBox,
} from './care.boxes';

import isEmail = validator.isEmail;

export const getCareSetupDetail = action(async (dispatch, _select, code: string) => {
  const data = (await http.open('GET/payment/platform-care/record/code', { code })).data as CareSetupDetailRecord;
  const { agreementTitle } = await AgreementClient.getAgreement({ id: data.agreementId });
  const res = Object.assign(data, {
    agreementTitle,
  }) as CareSetupDetailRecord;
  dispatch(careSetupDetailMapBox.mergeItem(code, res));
  return res;
});

export const ERROR_CODE_CODE_EMAIL_UNMATCHED = 70036;

export const getCareAgreementDetail = action(async (dispatch, select, agreementId: string) => {
  const r = await AgreementClient.getAgreement({ id: agreementId });
  dispatch(careAgreementMapBox.mergeItem(agreementId, r));
  return r;
});

export const getCareBusinessTypeUtil = (agreementTitle: string) => {
  const title = agreementTitle.trim().toLowerCase();
  if (title.endsWith('mobile')) {
    return BusinessType.GroomingMobile;
  }
  if (title.endsWith('salon') || title.endsWith('daycare')) {
    return BusinessType.GroomingSalon;
  }
  if (title.endsWith('hybrid')) {
    return BusinessType.Hybrid;
  }
  // Should not reach!
  return BusinessType.GroomingSalon;
};

export const getCareIsBD = (agreementTitle: string) => {
  return agreementTitle.trim().toLowerCase().endsWith('daycare');
};

// TODO(Perqin, P2): 后台现在没有字段保存 Care record 对应的 business type，时间紧迫只能硬编码逻辑判断标题了，后续需要后端优化
// TODO(dori): 优化下
export const getCareBusinessType = action(
  async (dispatch, select, careSetupDetail: OpenApiModels['GET/payment/platform-care/record/code']['Res']['data']) => {
    const { agreementTitle } = await dispatch(getCareAgreementDetail(careSetupDetail.agreementId as string));
    return getCareBusinessTypeUtil(agreementTitle);
  },
);

export const signCareAgreement = action(
  async (
    _dispatch,
    select,
    agreementId: number,
    signature: string,
    accountId: number = select(currentAccountIdBox),
    companyId: number = select(currentCompanyIdBox),
    businessId: number = select(currentBusinessIdBox),
  ) => {
    const { email } = select(selectCurrentAccount());
    await http.open('PUT/payment/platform-care/record/update', {
      accountId,
      companyId,
      businessId,
      agreementId,
      signature,
      email,
    });
  },
);

export const updateCareForm = action((dispatch, select, companyId: number, input: Partial<CareFormModel>) => {
  dispatch(careFormMapBox.mergeItem(companyId, input));
});

export const getCareAgreementSignRecord = action(async (dispatch, select, code: string) => {
  const { data } = await http.open('GET/payment/platform-care/get/sign-record/code', { code });
  dispatch(
    careAgreementSignRecordMapBox.mergeItem(
      data.companyId as unknown as number,
      data as unknown as Partial<CareAgreementSignModel>,
    ),
  );
  return data;
});

export const getHardwareShippingMethods = action(async () => {
  const { data } = await http.open('GET/payment/hardware/shipping_methods', {});
  return data;
});

type CareOrderRequestParams = Omit<CareFormModel, 'couponInfo'>;

export const isValidForCardOrderPreview = (params: CareOrderRequestParams) => {
  const {
    companyId,
    companyName,
    recipientName,
    email,
    phoneNumber,
    shippingMethod,
    address,
    m2Quantity,
    smartReaderQuantity,
  } = params;
  const { line1, postalCode, country, city, state } = address;
  // See: https://stripe.com/docs/api/terminal/hardware_orders/preview#preview_terminal_hardware_order-shipping
  return !!(
    isNormal(companyId) &&
    companyName.length >= 3 &&
    companyName.length <= 40 &&
    recipientName.length >= 3 &&
    recipientName.length <= 15 &&
    isEmail(email) &&
    phoneNumber.length >= 10 &&
    phoneNumber.length <= 14 &&
    shippingMethod &&
    line1 &&
    postalCode &&
    country &&
    city &&
    state &&
    m2Quantity + smartReaderQuantity > 0
  );
};

export const getCareHardwareOrderPreview = action(async (dispatch, select, input: CareOrderRequestParams) => {
  dispatch(
    careOrderPreviewMapBox.mergeItem(input.companyId, {
      isLoading: true,
    }),
  );

  try {
    const preview = await http.open(
      'POST/payment/hardware/order/preview',
      input as OpenApiModels['POST/payment/hardware/order/preview']['Req'],
      {
        autoToast: false,
      },
    );
    dispatch(
      careOrderPreviewMapBox.mergeItem(input.companyId, {
        preview,
        failure: null,
      }),
    );
  } catch (e) {
    dispatch(
      careOrderPreviewMapBox.mergeItem(input.companyId, {
        failure: e,
      }),
    );
  } finally {
    dispatch(
      careOrderPreviewMapBox.mergeItem(input.companyId, {
        isLoading: false,
      }),
    );
  }
});

export const clearCareHardwareOrderPreview = action((dispatch, select, companyId: number) => {
  dispatch(
    careOrderPreviewMapBox.mergeItem(companyId, {
      preview: null,
      failure: null,
    }),
  );
});

export const placeCareHardwareOrder = action(async (dispatch, select, input: CareOrderRequestParams) => {
  await http.open('POST/payment/hardware/order/buy', input as OpenApiModels['POST/payment/hardware/order/buy']['Req']);
});
