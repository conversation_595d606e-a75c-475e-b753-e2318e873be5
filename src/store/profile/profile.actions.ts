import { UserType } from '@moego/api-web/moego/models/user_profile/v1/user_profile_models';
import { action } from 'amos';
import { UserProfileClient } from '../../middleware/clients';
import { currentBusinessIdBox } from '../business/business.boxes';

export const fetchBusinessProfile = action(
  async (dispatch, select, businessId: number = select(currentBusinessIdBox)) => {
    try {
      const res = await UserProfileClient.getUserProfile(
        {
          users: [
            {
              id: businessId as unknown as string,
              type: UserType.BUSINESS,
            },
          ],
        },
        {
          autoToast: false,
        },
      );
      return res.userProfiles.length > 0 ? res.userProfiles[0] : null;
    } catch {
      return null;
    }
  },
);
