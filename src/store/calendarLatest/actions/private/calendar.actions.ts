import { type ListDaySlotInfosParams } from '@moego/api-web/moego/api/appointment/v1/calendar_api';
import { action } from 'amos';
import { type Dayjs, isDayjs } from 'dayjs';
import { hasIn } from 'lodash';
import { type DeepPartial, type Overwrite } from 'utility-types';
import { RepeatTicketApplyTypes } from '../../../../components/ModalRepeatApply/ModalRepeatApply';
import { getAppointment } from '../../../../container/Appt/store/appt.api';
import { LK_VISIBLE_STAFFS_V2 } from '../../../../container/Calendar/latest/AwesomeCalendar.utils';
import { http } from '../../../../middleware/api';
import { CalendarClient } from '../../../../middleware/clients';
import { type OpenApiDefinitions, type OpenApiModels } from '../../../../openApi/schema';
import { type IdToNumberAndRequired, id2NumberAndRequired, overwriteReturnType } from '../../../../utils/api/api';
import { calendarTransformer } from '../../../../utils/api/transformers/calendar.transform';
import { IgnoreErrorCode } from '../../../../utils/createObservableActionFactory';
import { stringToDateMessage } from '../../../../utils/utils';
import { currentBusinessIdBox } from '../../../business/business.boxes';
import { selectCurrentBusiness } from '../../../business/business.selectors';
import { GroomingTicketNoShowType } from '../../../grooming/grooming.boxes';
import { createAppointmentObservableAction } from '../../../observableServices/observableServices';
import { NoAssignedStaffInfo, isValidStaffIdWithNoAssigned } from '../../../staff/no-assigned-staff-helper';
import { staffMapBox } from '../../../staff/staff.boxes';
import { preAuthDetailMapBox } from '../../../stripe/preAuth.boxes';
import { get } from '../../../utils/utils';
import { clearMagicWaitList } from '../../../waitList/actions/private/waitList.actions';
import {
  type ApptBatchModalStatusBox,
  type ApptDetailDrawerAddBox,
  type ApptDetailDrawerAddDetailBox,
  type ApptDetailDrawerEditBox,
  type ApptDetailModalStateBox,
  type CalendarEditTicketInfoBox,
  CalendarMonthlyViewTicket,
  type CalendarNotificationStateBox,
  CalendarSlotInfo,
  CalendarSlotSettingInfo,
  type CalendarViewName,
  DrivingDisplayInfo,
  type MagicWaitListMode,
  type MonthlyViewDTO,
  type StaffCalendarInfoBox,
  apptBatchModalStatusBox,
  apptDetailDrawerAddBox,
  apptDetailDrawerAddDetailBox,
  apptDetailDrawerEditBox,
  apptDetailDrawerEditHybridBox,
  apptDetailModalStatusBox,
  calendarAddBlockTimeConfigBox,
  calendarConfigMapBox,
  calendarDrivingDisplayInfoList,
  calendarDrivingDisplayInfoMapBox,
  calendarEditTicketInfoBox,
  calendarMonthlyTicketCurrentIdList,
  calendarMonthlyTicketDetailMapBox,
  calendarMonthlyTicketIdList,
  calendarMonthlyViewMoreMapBox,
  calendarNotificationBox,
  calendarSlotInfoList,
  calendarSlotInfoMapBox,
  calendarSlotSettingInfos,
  calendarViewConfigBox,
  quickAddPlaceholderBox,
  staffServiceAreaBox,
  staffWorkingHourBox,
  staffsForCalendarBox,
} from '../../calendar.boxes';
import { selectBusinessCalendarConfig } from '../../calendar.selectors';
import {
  type AddBlockTimeConfigPayload,
  type CalendarViewConfig,
  type QuickAddPlaceholderBoxInfo,
} from '../../calendar.types';
import { type ShowOnCalendarListForCalendarParams, getShowOnCalendarStaff } from '../public/calendar.actions';
import { getCalendarFilterPredicate } from '../../../../container/Calendar/latest/components/CalendarHeader/CalendarFilter/CalendarFilter.utils';

export const setCalendarDefaultView = action(
  async (dispatch, select, input?: CalendarViewName, businessId: number = select(currentBusinessIdBox)) => {
    localStorage.setItem('calendarview', input || 'Day');
    await Promise.resolve();
    dispatch(calendarConfigMapBox.mergeItem(businessId, { defaultView: input, businessId }));
  },
);

/**
 * 涉及自动关闭 driving time 的场景：
 * https://moego.atlassian.net/l/cp/nUArqsu8
 */
export const setDrivingTimeOpenState = action(
  async (dispatch, select, isDrivingTimeOpen: boolean, businessId: number = select(currentBusinessIdBox)) => {
    dispatch(
      calendarConfigMapBox.mergeItem(businessId, {
        isDrivingTimeOpen,
        ...(!isDrivingTimeOpen ? { isDrivingTimeVisible: false } : undefined),
      }),
    );
  },
);

export const setDrivingTimeVisibleState = action(
  async (dispatch, select, isDrivingTimeVisible: boolean, businessId: number = select(currentBusinessIdBox)) => {
    dispatch(calendarConfigMapBox.mergeItem(businessId, { isDrivingTimeVisible }));
  },
);

export const setWaitListMagicModeState = action(
  async (dispatch, select, businessId: number, newVal: Partial<MagicWaitListMode>) => {
    if (!newVal) {
      dispatch(clearMagicWaitList(businessId));
      dispatch(calendarConfigMapBox.mergeItem(businessId, { magicWaitListMode: null }));
      return;
    }
    const prev = select(calendarConfigMapBox.mustGetItem(businessId));
    dispatch(
      calendarConfigMapBox.mergeItem(businessId, {
        magicWaitListMode: { ...(prev.magicWaitListMode || {}), ...newVal },
      }),
    );
  },
);

export const refreshWaitListMagicMode = action(async (dispatch, select, businessId: number) => {
  const { magicWaitListMode } = select(calendarConfigMapBox.mustGetItem(businessId));
  if (!magicWaitListMode) {
    return;
  }
  dispatch(
    calendarConfigMapBox.mergeItem(businessId, {
      magicWaitListMode: { ...magicWaitListMode, refresh: !magicWaitListMode.refresh },
    }),
  );
});

export type updateCalendarViewConfigInput =
  | DeepPartial<CalendarViewConfig>
  | ((config: CalendarViewConfig) => DeepPartial<CalendarViewConfig>);

export const updateCalendarViewConfig = action(
  async (dispatch, select, input: updateCalendarViewConfigInput, _businessId = select(currentBusinessIdBox)) => {
    const nextConfig = typeof input === 'function' ? input(select(calendarViewConfigBox)) : input;
    dispatch(calendarViewConfigBox.setState((pre) => ({ ...pre, ...nextConfig })));
  },
);

export type QuickAddInputParams = Overwrite<
  OpenApiDefinitions['grooming']['com.moego.server.grooming.params.appointment.QuickAddAppointmentParam'],
  {
    petList?: Overwrite<
      OpenApiDefinitions['grooming']['com.moego.server.grooming.params.appointment.PetParams'],
      {
        serviceList?: Overwrite<
          OpenApiDefinitions['grooming']['com.moego.server.grooming.params.appointment.ServiceAndOperationParams'],
          {
            serviceItemEnum?: number;
            scopeTypeTime?: number;
            scopeTypePrice?: number;
          }
        >[];
      }
    >[];
  }
>;

export const setApptDetailModalState = action(async (dispatch, select, input: Partial<ApptDetailModalStateBox>) => {
  const preBoxState = select(apptDetailModalStatusBox);
  dispatch(apptDetailModalStatusBox.setState({ ...preBoxState, ...input }));
});

export const setApptBatchModalState = action(async (dispatch, select, input: Partial<ApptBatchModalStatusBox>) => {
  const preBoxState = select(apptBatchModalStatusBox);
  dispatch(apptBatchModalStatusBox.setState({ ...preBoxState, ...input }));
});

export const setBlockTimeAddDrawer = action(
  async (
    dispatch,
    select,
    input: Partial<AddBlockTimeConfigPayload> | ((d: AddBlockTimeConfigPayload) => Partial<AddBlockTimeConfigPayload>),
  ) => {
    const preBoxState = select(calendarAddBlockTimeConfigBox);
    const nextState = typeof input === 'function' ? input(preBoxState) : input;
    dispatch(calendarAddBlockTimeConfigBox.setState({ ...preBoxState, ...nextState }));
  },
);

export const setQuickAddPlaceholderBox = action(
  async (
    dispatch,
    select,
    input:
      | Partial<QuickAddPlaceholderBoxInfo>
      | ((p: QuickAddPlaceholderBoxInfo) => Partial<QuickAddPlaceholderBoxInfo>),
  ) => {
    const preBoxState = select(quickAddPlaceholderBox);
    const nextState = typeof input === 'function' ? input(preBoxState) : input;
    const hasEndDate = 'apptEndDateTime' in nextState;
    if (!hasEndDate) {
      const originDiffMinutes = preBoxState.apptEndDateTime.getMinutes() - preBoxState.apptDateTime.getMinutes();
      const validate = originDiffMinutes >= 0;
      nextState.apptEndDateTime = (nextState.apptDateTime ?? preBoxState.apptDateTime).add(
        validate ? originDiffMinutes : 0,
        'minute',
      );
    }
    dispatch(quickAddPlaceholderBox.setState({ ...preBoxState, ...nextState }));
  },
);

export const changeQuickAddPlaceholderSchedule = action(
  async (dispatch, select, input: { staffId?: number; apptDateTime?: Dayjs }) => {
    const { staffId, apptDateTime } = input;
    const params: Partial<QuickAddPlaceholderBoxInfo> = {};
    const hasStaff = isValidStaffIdWithNoAssigned(staffId);
    if (hasStaff) {
      params.staffId = staffId;
    }
    const hasStartDate = isDayjs(apptDateTime);
    if (hasStartDate) {
      const { apptDateTime: originStart, apptEndDateTime: originEnd } = select(quickAddPlaceholderBox);
      const originDiffMinutes = originEnd.getMinutes() - originStart.getMinutes();
      const nextApptEndDate = apptDateTime.add(originDiffMinutes, 'minute');
      Object.assign(params, {
        apptDateTime,
        apptEndDateTime: nextApptEndDate,
      });
    }
    dispatch(setQuickAddPlaceholderBox(params));
  },
);

export interface CalendarApptCardParams {
  endDate?: string;
  filterNoStaff?: boolean;
  filterNoStartTime?: boolean;
  isWaitingList?: boolean;
  startDate?: string;
}

export const getTicketPreAuthInfo = action(async (dispatch, select, id: number | string) => {
  const { data } = await http.open('GET/grooming/appointment/detail/pup', { id: +id });
  dispatch([preAuthDetailMapBox.mergeItem(+id, data.preAuthInfo)]);
  return data;
});

/**
 * 获取时间范围的working staff
 * 1. 仅返回show on calendar的staff
 * 2. 返回的字段中包含 isWorkingStaff 表示是否在入参的时间范围中是否是working staff
 */
export const getStaffListForCalendar = action(
  async (dispatch, select, params: ShowOnCalendarListForCalendarParams, syncLocalStorage = true) => {
    const staffsOrig = await dispatch(getShowOnCalendarStaff(params));
    const { onlyWorkingStaff, selectedStaffs, isAllStaff } = select(staffsForCalendarBox);
    /**
     * @description Calendar的unassigned staff column先隐藏，staff筛选里该No staff assigned选项也隐藏。
     * @jira https://moego.atlassian.net/browse/MER-602
     */
    // const boardingDaycareFeatureEnable = select(selectBDFeatureEnable);
    // const isEvaluationEnabled = select(selectIsEvaluationEnabled);
    // const staffs = boardingDaycareFeatureEnable && isEvaluationEnabled ? staffsOrig.concat([NoAssignedStaffInfo]) : staffsOrig;
    const staffs = staffsOrig;
    const nextState: Partial<StaffCalendarInfoBox> = {
      staffs,
    };

    // priority: onlyWorkingStaff > isAllStaff > selectedStaffs
    if (onlyWorkingStaff) {
      nextState.selectedStaffs = staffs.filter((staff) => staff.isWorkingStaff).map((staff) => staff.id);
    } else if (isAllStaff) {
      nextState.selectedStaffs = staffs.map((staff) => staff.id);
      nextState.isAllStaff = true;
    } else {
      const allStaffIds = staffs.map((staff) => staff.id);
      const validate = selectedStaffs.every((id) => allStaffIds.includes(id));
      if (!validate) {
        nextState.selectedStaffs = selectedStaffs.filter((id) => allStaffIds.includes(id));
      }
      // 如果下次的选中staff为空，则默认working staff, 这里不用判断是否是week view，因为staff选择那里做了这个case的处理
      if (nextState.selectedStaffs?.length === 0) {
        nextState.selectedStaffs = staffs.filter((s) => s.isWorkingStaff).map((staff) => staff.id);
        nextState.onlyWorkingStaff = true;
      }
    }
    dispatch([
      setStaffsForCalendar(nextState, syncLocalStorage),
      staffMapBox.mergeItem(NoAssignedStaffInfo.id, {
        ...NoAssignedStaffInfo,
        hireDate: 0,
        fireDate: 0,
        createTime: 0,
      }),
    ]);
    return staffs;
  },
);

export type StaffByServiceAreaParams = OpenApiModels['POST/business/workingArea/shiftManagement']['Req'];

export const getStaffsServiceArea = action(async (dispatch, select, params: StaffByServiceAreaParams) => {
  const business = select(selectCurrentBusiness());
  const isMobileGrooming = business.isMobileGrooming();
  if (!isMobileGrooming) {
    return;
  }
  const res = await http.open('POST/business/workingArea/shiftManagement', params);
  dispatch(staffServiceAreaBox.setState(res));
  return res;
});

export type EditCommentsParams = DeepPartial<
  OpenApiDefinitions['grooming']['com.moego.server.grooming.params.EditCommentsParams']
>;

export const modifyTicketComments = action(async (dispatch, select, params: EditCommentsParams) => {
  const res = await http.open('PUT/grooming/appointment/comments', params);
  return res;
});

export type TicketCommentsParamsV2 = OpenApiModels['GET/grooming/appointment/ticketComment/v2']['Req'];

export type TicketCommentsV2DTO =
  OpenApiDefinitions['grooming']['com.moego.server.grooming.dto.appointment.comment.TicketComment'];

export const getTicketCommentsV2 = action(async (dispatch, select, params: TicketCommentsParamsV2) => {
  const res = await http.open('GET/grooming/appointment/ticketComment/v2', params);
  return res;
});

export type ActionHistoryDTO = IdToNumberAndRequired<
  OpenApiDefinitions['grooming']['com.moego.server.grooming.dto.appointment.history.ActionHistoryDTO']
>;

export const getApptActivityLog = action(async (dispatch, select, params: { appointmentId: number }) => {
  const res = await http.open('GET/grooming/appointment/actionLog', params);
  return res;
});

export const modifyAlertNote = action(
  async (
    dispatch,
    select,
    params: { context: string; appointmentId: number },
    repeatType: number = RepeatTicketApplyTypes.ONE,
  ) => {
    await http.open('PUT/grooming/appointment/alertNotes/repeat', params, {
      query: {
        type: repeatType,
      },
    });
  },
);

/**
 * noShow: true 表示 noShow，false 表示 noShowCancel
 */
export const toggleNoShow = action(
  async (
    dispatch,
    select,
    params: { noShow: boolean; appointmentId: number; autoRefundOrder?: boolean; cancelReason?: string },
  ) => {
    const { noShow, appointmentId, autoRefundOrder, cancelReason } = params;
    const noShowStatus = noShow ? GroomingTicketNoShowType.noShow : GroomingTicketNoShowType.noShowCancel;
    return await http.open('PUT/grooming/appointment/cancel', {
      id: appointmentId,
      noShow: noShowStatus,
      ...(autoRefundOrder ? { autoRefundOrder: true } : {}),
      ...(cancelReason ? { cancelReason } : {}),
    });
  },
);

export const setTicketAlertsProps = action(
  async (
    dispatch,
    select,
    input:
      | Partial<CalendarNotificationStateBox>
      | ((p: CalendarNotificationStateBox) => Partial<CalendarNotificationStateBox>),
  ) => {
    const pre = select(calendarNotificationBox);
    const nextConfig = typeof input === 'function' ? input(pre) : input;
    dispatch(calendarNotificationBox.setState((pre) => ({ ...pre, ...nextConfig })));
  },
);

export const updateCalendarTicketInfo = action(
  async (
    dispatch,
    select,
    input: Partial<CalendarEditTicketInfoBox> | ((i: CalendarEditTicketInfoBox) => Partial<CalendarEditTicketInfoBox>),
  ) => {
    const nextConfig = typeof input === 'function' ? input(select(calendarEditTicketInfoBox)) : input;
    dispatch(calendarEditTicketInfoBox.setState((pre) => ({ ...pre, ...nextConfig })));
  },
);

export const getMonthlyViewList = action(async (dispatch, select, input: CalendarApptCardParams) => {
  const businessId: number = select(currentBusinessIdBox);
  const filters = getCalendarFilterPredicate();

  const { cards: grpcCards } = await CalendarClient.listMonthCards({
    ...input,
    businessId: businessId + '',
    startDate: stringToDateMessage(input.startDate || ''),
    endDate: stringToDateMessage(input.endDate || ''),
    filterNoStaff: input.filterNoStaff ?? true,
    filterNoStartTime: input.filterNoStartTime ?? true,
    isWaitingList: input.isWaitingList ?? false,
    ...(filters ? { predicate: filters } : {}),
  });
  const cardList = grpcCards.map(calendarTransformer.monthlyCardsResult.grpc2Open).map(id2NumberAndRequired);
  const list: MonthlyViewDTO[] = cardList.map((item) => ({
    serviceItemTypes: [],
    itemId: CalendarMonthlyViewTicket.generateId(item.id, item.cardType),
    ...item,
  }));
  dispatch([
    calendarMonthlyTicketDetailMapBox.mergeItems(list),
    calendarMonthlyTicketIdList.setList(businessId, list.map(get('itemId'))),
  ]);
});

export const refreshMonthlyViewCard = action(
  async (
    dispatch,
    select,
    input: { visible: CalendarMonthlyViewTicket[]; more: Record<string, CalendarMonthlyViewTicket[]> },
    businessId: number = select(currentBusinessIdBox),
  ) => {
    const { visible, more } = input;
    dispatch([
      calendarMonthlyTicketCurrentIdList.setList(businessId, visible.map(get('itemId'))),
      calendarMonthlyViewMoreMapBox.mergeItems(
        Object.entries(more).map(([key, l]) => ({ key, list: l.map(get('itemId')) })),
      ),
    ]);
  },
);

export const getOperationInfo = action(async (dispatch, select, petDetailId: number) => {
  const res = await http.open('GET/grooming/v2/appointment/operation/list', { petDetailId });
  return res;
});

export type DelTicketPetParams = OpenApiModels['DELETE/grooming/v2/appointment/pet']['Req'];

export const delTicketPet = action(async (dispatch, select, params: DelTicketPetParams) => {
  const res = await http.open('DELETE/grooming/v2/appointment/pet', params);
  return res;
});

export const setApptDetailDrawerAdd = action(async (dispatch, select, input: Partial<ApptDetailDrawerAddBox>) => {
  dispatch(apptDetailDrawerAddBox.setState((pre) => ({ ...pre, ...input })));
});

export const setApptDetailDrawerAddDetail = action(
  async (dispatch, select, input: Partial<ApptDetailDrawerAddDetailBox>) => {
    dispatch(apptDetailDrawerAddDetailBox.setState((pre) => ({ ...pre, ...input })));
  },
);

export const setApptDetailDrawerEdit = action(async (dispatch, select, input: Partial<ApptDetailDrawerEditBox>) => {
  dispatch(apptDetailDrawerEditBox.setState((pre) => ({ ...pre, ...input })));
});

export const exitApptDetailDrawerEdit = action(async (dispatch) => {
  dispatch(apptDetailDrawerEditBox.setState(apptDetailDrawerEditBox.initialState));
});

export const setApptDetailDrawerHybridEdit = action(
  async (dispatch, select, input: Partial<ApptDetailDrawerEditBox>) => {
    dispatch(apptDetailDrawerEditHybridBox.setState((pre) => ({ ...pre, ...input })));
  },
);

export const addSelectCalendarStaff = action(async (dispatch, select, staffIds: number[]) => {
  const { staffs, selectedStaffs: preSelected } = select(staffsForCalendarBox);
  const showStaffIds = staffs.map((s) => s.id);
  const nextSelectedStaffs = staffIds
    // 必须是show on calendar的staff
    .filter((id) => showStaffIds.includes(id))
    // 必须是之前没有选中的staff
    .filter((id) => !preSelected.includes(id));
  const selectedStaffs = preSelected.concat(nextSelectedStaffs);
  dispatch(setStaffsForCalendar({ selectedStaffs }));
});

export const getDrivingDisplayInfo = action(
  async (dispatch, select, startDate: string, endDate: string, staffIdList: number[], signal?: AbortSignal) => {
    const res = await http.open(
      'POST/grooming/appointment/driving/info/batch',
      {
        startDate,
        endDate,
        staffIdList,
      },
      {
        signal,
      },
    );
    // prepare the driving info list
    const { dateDrivingInfoList } = res;
    const drivingInfoList = dateDrivingInfoList.reduce((acc, { staffDrivingInfoList, date }) => {
      staffDrivingInfoList.forEach(({ drivingInfoList, staffId }) => {
        drivingInfoList.forEach((drivingInfoObj) => {
          const ownId = DrivingDisplayInfo.ownId(date, staffId, drivingInfoObj.groomingId);
          const drivingInfo = new DrivingDisplayInfo({
            ...drivingInfoObj,
            ownId,
            isDriveOut: hasIn(drivingInfoObj, 'drivingOutMinutes') && hasIn(drivingInfoObj, 'drivingOutMiles'),
          });
          acc.push(drivingInfo);
        });
      });
      return acc;
    }, [] as DrivingDisplayInfo[]);

    // update map
    const businessId = select(currentBusinessIdBox);
    const oldList = select(calendarDrivingDisplayInfoList.getList(businessId));
    dispatch(oldList.map((id) => calendarDrivingDisplayInfoMapBox.deleteItem(id)).toArray());
    dispatch([
      calendarDrivingDisplayInfoList.setList(businessId, drivingInfoList.map(get('ownId'))),
      calendarDrivingDisplayInfoMapBox.mergeItems(drivingInfoList),
    ]);
  },
);

export const getFeedback2023 = action(async (_dispatch, _select) => {
  const res = await http.open('GET/grooming/2023/questionnaire');
  return res;
});

export const submitFeedback2023 = action(async (dispatch, select, formDetail: string) => {
  const res = await http.open('POST/grooming/2023/questionnaire', { formDetail });
  return res;
});

export const setStaffsForCalendar = action(
  async (
    dispatch,
    select,
    input: Partial<StaffCalendarInfoBox> | ((pre: StaffCalendarInfoBox) => Partial<StaffCalendarInfoBox>),
    syncLocalStorage = false,
  ) => {
    const pre = select(staffsForCalendarBox);
    const next = typeof input === 'function' ? input(pre) : input;
    const temp = { ...pre, ...next };
    const allStaffIdList = temp.staffs.map((staff) => staff.id);
    const isAllStaff = next.isAllStaff || allStaffIdList.every((staffId) => temp.selectedStaffs?.includes(staffId));
    const selectedStaffList = isAllStaff ? allStaffIdList : temp.selectedStaffs || [];
    const fullState = { ...temp, isAllStaff, selectedStaffs: selectedStaffList };
    dispatch(staffsForCalendarBox.setState(fullState));
    if (syncLocalStorage) {
      const bizId = select(currentBusinessIdBox);
      const { onlyWorkingStaff, selectedStaffs, isAllStaff } = fullState;
      const str = JSON.stringify({ onlyWorkingStaff, selectedStaffs, isAllStaff });
      localStorage.setItem(LK_VISIBLE_STAFFS_V2 + bizId, str);
    }
  },
);
export const getTicketDetailInfo = createAppointmentObservableAction(
  'getTicketDetailInfo',
  async (dispatch, _select, id: number) => {
    const [{ data: dataOrig }, res] = await Promise.all([
      http.open('GET/grooming/appointment/detail/pup', { id }),
      dispatch(getAppointment({ appointmentId: String(id) })),
    ]);
    const data = id2NumberAndRequired(overwriteReturnType<typeof dataOrig, { waitListId: string }>(dataOrig));
    dispatch([preAuthDetailMapBox.mergeItem(id, data.preAuthInfo || {})]);
    return res;
  },
);

export type StaffByWorkingDateParams = OpenApiModels['GET/business/staff/staffByWorkingDate']['Req'] & {
  dispatch?: boolean;
};

export const getStaffsWorkingHour = createAppointmentObservableAction(
  'getStaffsWorkingHour',
  async (dispatch, select, params: StaffByWorkingDateParams) => {
    const { dispatch: isDispatch = true, ...rest } = params;
    const res = await http.open('GET/business/staff/staffByWorkingDate', rest);
    if (isDispatch) {
      dispatch(staffWorkingHourBox.setState(res));
    }
    return res;
  },
);

export const getCalendarConfig = createAppointmentObservableAction(
  'getCalendarConfig',
  async (dispatch, select, businessId: number = select(currentBusinessIdBox)) => {
    const r = await http.open('GET/business/calendar/setting');
    dispatch(calendarConfigMapBox.mergeItem(businessId, r.data));
    return r.data;
  },
);

export const updateCalendarConfig = createAppointmentObservableAction(
  'updateCalendarConfig',
  async (
    dispatch,
    select,
    input: OpenApiModels['PUT/business/calendar/setting']['Req'],
    businessId: number = select(currentBusinessIdBox),
  ) => {
    await http.open('PUT/business/calendar/setting', input);
    dispatch(calendarConfigMapBox.mergeItem(businessId, input));
  },
);

export type AddCalendarBlockTimeParams = OpenApiModels['POST/grooming/appointment/block']['Req'];

export const addCalendarBlockTime = createAppointmentObservableAction(
  'addCalendarBlockTime',
  async (dispatch, select, input: AddCalendarBlockTimeParams) => {
    const res = await http.open('POST/grooming/appointment/block', input);
    return res;
  },
);

export type ModifyCalendarBlockTimeParams = OpenApiModels['PUT/grooming/appointment/block']['Req'];

export const modifyCalendarBlockTime = createAppointmentObservableAction(
  'modifyCalendarBlockTime',
  async (dispatch, select, input: ModifyCalendarBlockTimeParams) => {
    const res = await http.open('PUT/grooming/appointment/block', input);
    return res;
  },
);

export type AddCalendarRepeatBlockTimeParams = DeepPartial<
  OpenApiModels['POST/grooming/appointment/block/repeat']['Req']
>;

export const addCalendarRepeatBlockTime = createAppointmentObservableAction(
  'addCalendarRepeatBlockTime',
  async (dispatch, select, input: AddCalendarRepeatBlockTimeParams) => {
    const res = await http.open('POST/grooming/appointment/block/repeat', input);
    return res;
  },
  { ignoreError: [IgnoreErrorCode.ParamsErrorCode] },
);

export type ModifyCalendarRepeatBlockTimeParams = DeepPartial<
  OpenApiModels['PUT/grooming/appointment/block/repeat']['Req']
>;

export const modifyCalendarRepeatBlockTime = createAppointmentObservableAction(
  'modifyCalendarRepeatBlockTime',
  async (dispatch, select, input: ModifyCalendarRepeatBlockTimeParams, query?: { type?: number }) => {
    const res = await http.open('PUT/grooming/appointment/block/repeat', input, { query });
    return res;
  },
);

export type DelCalendarRepeatBlockTimeParams = DeepPartial<OpenApiModels['DELETE/grooming/appointment/block']['Req']>;

export const delCalendarRepeatBlockTime = createAppointmentObservableAction(
  'delCalendarRepeatBlockTime',
  async (dispatch, select, input: DelCalendarRepeatBlockTimeParams) => {
    const res = await http.open('DELETE/grooming/appointment/block', input);
    return res;
  },
);

export const getCalendarSlotInfo = createAppointmentObservableAction(
  'getCalendarSlotInfo',
  async (dispatch, select, input: ListDaySlotInfosParams) => {
    const isMobileBiz = select(selectCurrentBusiness).isMobileGrooming();
    const calendarConfig = select(selectBusinessCalendarConfig);

    if (isMobileBiz || !calendarConfig.showSlotLocation) {
      return null;
    }

    const res = await CalendarClient.listDaySlotInfos(input);
    const businessId = select(currentBusinessIdBox);

    dispatch([
      calendarSlotInfoMapBox.mergeItems(
        res.slotInfos.map((slotInfo) => ({
          ...slotInfo,
          ownId: CalendarSlotInfo.uniqueId(slotInfo.date, slotInfo.staffId, slotInfo.startTime, slotInfo.endTime),
        })),
      ),
      calendarSlotInfoList.setList(
        businessId,
        res.slotInfos.map((slotInfo) =>
          CalendarSlotInfo.uniqueId(slotInfo.date, slotInfo.staffId, slotInfo.startTime, slotInfo.endTime),
        ),
      ),
      calendarSlotSettingInfos.mergeItems(
        res.settingSlotInfos.map((slotInfo) => ({
          ...slotInfo,
          ownId: CalendarSlotSettingInfo.uniqueId(slotInfo.staffId, slotInfo.date),
        })),
      ),
    ]);

    return res;
  },
);
