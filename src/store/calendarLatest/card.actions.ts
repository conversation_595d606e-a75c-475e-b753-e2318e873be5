import {
  type RescheduleCalendarCardParams as RescheduleCalendarCardParamsApi,
  type RescheduleEvaluationServiceParams,
  type RescheduleGroomingServiceParams,
} from '@moego/api-web/moego/api/appointment/v1/appointment_schedule_api';
import { type PreviewCalendarCardsParams } from '@moego/api-web/moego/api/appointment/v1/calendar_api';
import { RepeatAppointmentModifyScope } from '@moego/api-web/moego/models/appointment/v1/pet_detail_enums';
import { action } from 'amos';
import { CreateApptId } from '../../container/Appt/store/appt.types';
import { AppointmentScheduleClient, CalendarClient } from '../../middleware/clients';
import { stringToDateMessage } from '../../utils/utils';
import { currentBusinessIdBox } from '../business/business.boxes';
import { saveCustomerActiveSubscriptionList } from '../membership/membership.actions';
import { preAuthDetailMapBox } from '../stripe/preAuth.boxes';
import { isNormal } from '../utils/identifier';
import { get, truly } from '../utils/utils';
import { type CalendarApptCardParams } from './actions/private/calendar.actions';
import {
  apptEventCardMapBox,
  apptEventCardsList,
  apptEventDraggingRelateList,
  apptPreviewCardListMapBox,
} from './card.boxes';
import { type ApptEventCardInfo, type ApptPreviewCardListType } from './card.types';
import { transformCardToApptEvent } from './card.utils';
import { getCalendarFilterPredicate } from '../../container/Calendar/latest/components/CalendarHeader/CalendarFilter/CalendarFilter.utils';
import { calendarCurrentViewBox } from './calendar.boxes';
import { FullCalendarViewType } from './calendar.types';

export const previewCalendarCards = action(
  async (dispatch, select, input: PreviewCalendarCardsParams, signal?: AbortSignal) => {
    return await CalendarClient.previewCalendarCards(input, { signal });
  },
);

export const setApptPreviewCardList = action(
  (
    dispatch,
    select,
    appointmentId: string = CreateApptId,
    input: Partial<Omit<ApptPreviewCardListType, 'appointmentId'>>,
  ) => {
    dispatch(apptPreviewCardListMapBox.mergeItem(appointmentId, input));
  },
);

export const deleteApptPreviewCardList = action((dispatch, select, appointmentId: string = CreateApptId) => {
  dispatch(apptPreviewCardListMapBox.deleteItem(appointmentId));
});

export const getApptCalendarCards = action(
  async (dispatch, select, input: CalendarApptCardParams, signal?: AbortSignal) => {
    const businessId = select(currentBusinessIdBox);
    const filters = getCalendarFilterPredicate();
    const currentViewType = select(calendarCurrentViewBox);

    const { cards: cardList } = await CalendarClient.listDayCardsWithMixType(
      {
        ...input,
        businessId: businessId + '',
        startDate: stringToDateMessage(input.startDate || ''),
        endDate: stringToDateMessage(input.endDate || ''),
        filterNoStaff: input.filterNoStaff ?? true,
        filterNoStartTime: input.filterNoStartTime ?? true,
        ...(filters ? { predicate: filters } : {}),
        viewType: FullCalendarViewType[currentViewType],
      },
      { signal },
    );

    const cards = transformCardToApptEvent(cardList);

    dispatch([
      apptEventCardMapBox.mergeItems(cards),
      apptEventCardsList.setList(businessId, cards.map(get('cardId'))),
      preAuthDetailMapBox.mergeItems(
        cards
          .map((card) => {
            const { preAuthInfo } = card;
            if (!preAuthInfo) return;
            const { customerId, paymentId, preAuthId, ticketId, ...rest } = preAuthInfo;
            return {
              ...rest,
              customerId: Number(customerId),
              paymentId: Number(paymentId),
              preAuthId: Number(preAuthId),
              ticketId: Number(ticketId),
            };
          })
          .filter(truly),
      ),
      saveCustomerActiveSubscriptionList(
        cardList.map((card) => ({
          customerId: String(card.customerInfo?.customerId),
          membershipSubscriptions: card.membershipSubscriptions?.membershipSubscriptions || [],
        })),
      ),
    ]);
  },
);

export const setCalendarCardEvent = action(
  async (dispatch, select, cardId: string, input: Partial<ApptEventCardInfo>) => {
    dispatch(apptEventCardMapBox.mergeItem(cardId, input));
  },
);

export interface RescheduleCalendarCardParams
  extends Omit<
    RescheduleCalendarCardParamsApi,
    'appointmentId' | 'staffId' | 'petDetailIds' | 'moveAllCards' | 'originalStaffId'
  > {
  appointmentId: number;
  petDetailIds?: number[];
  staffId?: number;
  moveAllCards?: boolean;
  originalStaffId?: number;
}

export const rescheduleCalendarCard = action(async (dispatch, select, input: RescheduleCalendarCardParams) => {
  const {
    appointmentId,
    cardType,
    staffId,
    startDate,
    startTime,
    petDetailIds = [],
    repeatModifyType = RepeatAppointmentModifyScope.ONLY_THIS,
    moveAllCards = false,
    originalStaffId,
    endTime,
  } = input;
  await AppointmentScheduleClient.rescheduleCalendarCard({
    appointmentId: String(appointmentId),
    cardType: cardType as RescheduleGroomingServiceParams['cardType'],
    staffId: isNormal(staffId) ? String(staffId) : undefined,
    startDate,
    startTime,
    endTime,
    petDetailIds: petDetailIds.map(String),
    repeatModifyType: repeatModifyType,
    moveAllCards,
    originalStaffId: isNormal(originalStaffId) ? String(originalStaffId) : undefined,
  });
});

export const rescheduleEvaluationCard = action(async (dispatch, select, input: RescheduleEvaluationServiceParams) => {
  return AppointmentScheduleClient.rescheduleEvaluationService(input);
});

export const setApptEventDraggingRelateList = action(
  (dispatch, select, cardIds: string[], businessId: number = select(currentBusinessIdBox)) => {
    dispatch(apptEventDraggingRelateList.setList(businessId, cardIds));
  },
);

export const clearApptEventDraggingRelateList = action(
  (dispatch, select, businessId: number = select(currentBusinessIdBox)) => {
    dispatch(apptEventDraggingRelateList.deleteList(businessId));
  },
);
