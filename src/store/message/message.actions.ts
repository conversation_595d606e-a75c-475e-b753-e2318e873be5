/*
 * @since 2020-11-04 23:15:51
 * <AUTHOR> <<EMAIL>>
 */
import { action } from 'amos';
import { List } from 'immutable';
import { ChannelEnum } from '../../components/MessageSendBox/components/ChannelSelect/ChannelSelect';
import { CustomerIsRead } from '../../container/Message/MessageCenter/const';
import { http } from '../../middleware/api';
import { type OpenApiModels } from '../../openApi/schema';
import { sec } from '../../utils/DateTimeUtil';
import { id2NumberAndRequired } from '../../utils/api/api';
import { LoggerModule, logger } from '../../utils/logger';
import { checkIsCofMessage } from '../../utils/message';
import { omitEmpty } from '../../utils/misc';
import { currentBusinessIdBox } from '../business/business.boxes';
import { companyMapBox, currentCompanyIdBox, msgBillingMapBox } from '../company/company.boxes';
import { getCompanyCharges } from '../company/subscription.actions';
import { getCustomerDetail, updateCustomer } from '../customer/customer.actions';
import { customerMapBox } from '../customer/customer.boxes';
import { createMessageObservableAction } from '../observableServices/observableServices';
import { type PagedLoadMoreInput } from '../utils/PagedLoadMore';
import { type PartialProps, type PartialRequired, type RecordProps } from '../utils/RecordMap';
import { LegacyBool } from '../utils/createEnum';
import { ID_INVALID, isNormal } from '../utils/identifier';
import { get, listRemoveItem } from '../utils/utils';
import { currentAccountIdBox } from './../account/account.boxes';
import { A2PBannerStepTitleMap, A2PStatus } from './a2p.config';
import {
  LK_MESSAGE_PAGE_CONFIG,
  MessageDetailFromType,
  type MessageDetailListFilterRecord,
  type MessageDetailModel,
  MessagePageConfigRecord,
  type MessageThreadListFilterRecord,
  MessageThreadQueryType,
  MessageThreadRecord,
  MessageThreadUpdateType,
  TARGET_TYPE_RECEIPT,
  businessMessageThreadListBox,
  businessMessageThreadMapBox,
  invoiceSentInfoBox,
  isContainAllMessageThreadTargetType,
  messageDetailMapBox,
  messagePageConfigMapBox,
  messagePlanListBox,
  messagePlanMapBox,
  messageReportListBox,
  messageReportMapBox,
  messageSummaryMapBox,
  messageThreadDetailListBox,
} from './message.boxes';
import {
  selectBusinessMessageThread,
  selectBusinessMessageThreadList,
  selectCurrentCycleMessageCount,
  selectMessagePlans,
  selectMessageThreadDetails,
} from './message.selectors';

export const addThread = createMessageObservableAction(
  'messageAddThread',
  async (dispatch, select, customerId: number) => {
    const r = await http.open('POST/message/thread/add', { customerId });
    const ownKey = MessageThreadRecord.ownKey(r.data.businessId, r.data.customerId);
    const data = Object.assign(
      {
        unreadCount: 0,
        status: 0,
        updateTime: sec(),
        deleteTime: sec(),
        customerUnreadCount: 0,
        openStatus: LegacyBool.True,
        starsTime: 0,
        blockTime: 0,
      },
      omitEmpty(r.data),
    );
    dispatch(businessMessageThreadMapBox.mergeItem(ownKey, data));
    return data;
  },
);

export type GetThreadListInput = PagedLoadMoreInput & PartialProps<MessageThreadListFilterRecord>;

export const getThreadList = createMessageObservableAction(
  'messageGetThreadList',
  async (
    dispatch,
    select,
    input: GetThreadListInput,
    businessId: number = select(currentBusinessIdBox),
    signal?: AbortSignal,
  ) => {
    const state = selectBusinessMessageThreadList(select, businessId);
    const targetTypeList = input.targetTypeList;
    const filterTargetTypeAll = isContainAllMessageThreadTargetType(targetTypeList?.split(','));

    const filter = state.getLegacyFilter({
      ...input,
      targetTypeList: filterTargetTypeAll ? void 0 : targetTypeList,
    });

    dispatch(businessMessageThreadListBox.updateItem(businessId, (v) => v.applyStart(input.loadMode, filter)));
    try {
      const r = await http.open('GET/message/thread/list', filter, { signal });
      const { total = 0, dataList = [] } = r.data || {};
      const customerList = dataList.map((c) => ({
        firstName: c.firstName,
        lastName: c.lastName,
        avatarPath: c.customerAvatar,
        clientColor: c.clientColor,
        customerId: c.customerId,
        isNewCustomer: c.isNewCustomer,
        isProspectCustomer: c.isProspectCustomer,
        ownKey: MessageThreadRecord.ownKey(businessId, c.customerId),
      }));
      const threadList = dataList.map((c) => ({
        ...c,
        ownKey: MessageThreadRecord.ownKey(businessId, c.customerId),
      }));
      /**
       * <AUTHOR>
       * @since 2025-03-05
       * @see https://moego.atlassian.net/browse/CS-26251
       * 查一个无法复现的CS单，猜想可能是total不合法，导致下面更新total的时候算成了0,加个手动上报观察一下
       */
      if (!Number(total)) {
        logger.get(LoggerModule.EXPECT_ERROR).error('unexpected_total', {
          total,
          totalType: typeof total,
          filter,
        });
      }
      dispatch([
        customerMapBox.mergeItems(customerList),
        businessMessageThreadMapBox.mergeItems(threadList),
        businessMessageThreadListBox.updateItem(businessId, (v) =>
          v.applySuccess(input.loadMode, filter, dataList.map(get('customerId')), Number(total) || 0),
        ),
      ]);
    } catch (e) {
      dispatch(businessMessageThreadListBox.updateItem(businessId, (v) => v.applyFail()));
      throw e;
    }
  },
);

export const updateThread = createMessageObservableAction(
  'messageUpdateThread',
  async (
    dispatch,
    select,
    threadId: number,
    customerId: number,
    updateType: MessageThreadUpdateType,
    removeFromList: boolean,
    businessId: number = select(currentBusinessIdBox),
  ) => {
    if (updateType === MessageThreadUpdateType.Block || updateType === MessageThreadUpdateType.Unblock) {
      await dispatch(updateCustomer({ customerId, isBlockMessage: +(updateType === MessageThreadUpdateType.Block) }));
    } else {
      await http.open('POST/message/thread/update', { updateType: updateType, ids: [threadId] });
    }
    const threadProps: PartialProps<MessageThreadRecord> = {};
    const thread = select(selectBusinessMessageThread(customerId));
    switch (updateType) {
      case MessageThreadUpdateType.Open:
        threadProps.openStatus = LegacyBool.True;
        break;
      case MessageThreadUpdateType.Close:
        threadProps.openStatus = LegacyBool.False;
        break;
      case MessageThreadUpdateType.Star:
        threadProps.starsTime = sec();
        break;
      case MessageThreadUpdateType.Unstar:
        threadProps.starsTime = 0;
        break;
      case MessageThreadUpdateType.Block:
        threadProps.blockTime = sec();
        break;
      case MessageThreadUpdateType.Unblock:
        threadProps.blockTime = 0;
        break;
      case MessageThreadUpdateType.Read:
        threadProps.unreadCount = 0;
        break;
      case MessageThreadUpdateType.Delete:
        threadProps.deleteTime = sec();
        break;
      case MessageThreadUpdateType.Unread:
        threadProps.unreadCount = 1;
        break;
    }
    dispatch([
      businessMessageThreadMapBox.mergeItem(MessageThreadRecord.ownKey(businessId, customerId), threadProps),
      businessMessageThreadListBox.updateItem(businessId, (v) => (removeFromList ? v.deleteItem(customerId) : v)),
      messageSummaryMapBox.updateItem(businessId, (v) =>
        v.set(
          'totalUnread',
          MessageThreadUpdateType.Read === updateType ? Math.max(v.totalUnread - thread.unreadCount, 0) : v.totalUnread,
        ),
      ),
      messageThreadDetailListBox.updateItem(customerId, (v) =>
        removeFromList
          ? v.merge({
              data: List<number>(),
              total: 0,
            })
          : v,
      ),
    ]);
  },
);

export type GetMessageDetailListInput = PagedLoadMoreInput &
  RecordProps<MessageDetailListFilterRecord> & { customerId: number };

export const getMessageDetailList = createMessageObservableAction(
  'messageGetMessageDetailList',
  async (dispatch, select, input: GetMessageDetailListInput) => {
    const state = selectMessageThreadDetails(select, input.customerId);
    const filter = state.getLegacyFilter(input);
    dispatch(messageThreadDetailListBox.updateItem(input.customerId, (v) => v.applyStart(input.loadMode, filter)));
    try {
      const r = await http.open('GET/message/send/thread/list', filter);
      const { dataList = [], total = 0 } = id2NumberAndRequired(r.data) || {};
      dispatch([
        messageDetailMapBox.mergeItems(dataList),
        messageThreadDetailListBox.updateItem(input.customerId, (v) =>
          v.applySuccess(input.loadMode, filter, dataList.map(get('id')), total),
        ),
      ]);
    } catch (e) {
      dispatch(messageThreadDetailListBox.updateItem(input.customerId, (v) => v.applyFail()));
      throw e;
    }
  },
);

/**
 * @description send single message to customer
 * many places call {@link sendSingleMessage} in the same way with much steps, consider use the instead.
 * ! attention: if customer detail not prefetch, it will auto fetch.
 */
export const sendMessageToCustomer = createMessageObservableAction(
  'messageSendMessageToCustomer',
  async (
    dispatch,
    select,
    customerId: number,
    message: string,
    contactId?: number,
    options?: OpenApiModels['POST/message/send/toCustomer/one']['Req'],
    targetType?: number,
    invoiceId?: number,
  ) => {
    await dispatch(addThread(customerId));
    let customer = select(customerMapBox.mustGetItem(customerId));
    const thread = select(selectBusinessMessageThread(customerId));
    // 针对auto message，后端需要通过invoiceId作为targetId来记录
    const targetId = targetType === TARGET_TYPE_RECEIPT ? invoiceId : thread.id;

    if (!isNormal(customer.customerId)) {
      // 线上存在customer.customerId 是 -1 的情况，但是很难复现
      // 为了避免这种情况，这里做了一层保护
      await dispatch(getCustomerDetail(customerId));
      customer = select(customerMapBox.mustGetItem(customerId));
    }

    await dispatch(
      sendSingleMessage({
        ...options,
        targetId,
        messageBody: message.trim(),
        messageType: 1,
        targetType,
        customer: {
          customerId: customer.customerId,
          ...(contactId
            ? { contactId } // The contact should provide phone number and full name
            : {
                customerNumber: customer.phoneNumber,
                contactName: customer.fullName(),
              }),
        },
      }),
    );
  },
);

type SendSingleMessageInput = Omit<OpenApiModels['POST/message/send/toCustomer/one']['Req'], 'customer'> & {
  customer?: Partial<OpenApiModels['POST/message/send/toCustomer/one']['Req']['customer']>;
};

/**
 * @deprecated consider use {@link sendMessageToCustomer} instead.
 */
export const sendSingleMessage = createMessageObservableAction(
  'messageSendSingleMessage',
  async (dispatch, select, input: SendSingleMessageInput) => {
    const messageBody = (input.messageBody ?? '').trim();
    const { data } = await http.open('POST/message/send/toCustomer/one', {
      ...input,
      messageBody,
      needRefreshCOF: input.needRefreshCOF || checkIsCofMessage(messageBody),
    });
    if (input.method !== ChannelEnum.ppa) {
      dispatch(afterSendSingleMessage(data.numSegments));
    }
    await dispatch(receiveMessageDetail(data));
    return data;
  },
);

export const afterSendSingleMessage = createMessageObservableAction(
  'messageAfterSendSingleMessage',
  async (dispatch, select, counts: number = 1, companyId: number = select(currentCompanyIdBox)) => {
    const recentCycleKey = select(messageReportListBox).getList(companyId).get(0);
    const messageReport = select(messageReportMapBox.mustGetItem(recentCycleKey ?? ''));
    const messageCount = select(selectCurrentCycleMessageCount);
    if (messageCount.remainCount() - counts < 10) {
      dispatch(getMessageReport());
      return;
    }
    dispatch([
      messageReportMapBox.mergeItemDeep(messageReport.ownKey, {
        ...messageReport,
        used2wayMessage: messageReport.used2wayMessage + counts,
      }),
    ]);
  },
);

export const removeMessageDetail = action(async (dispatch, select, messageDetailId: number, customerId: number) => {
  await http.open('DELETE/message/send/delete', { messageDetailId });
  dispatch(
    messageThreadDetailListBox.updateItem(customerId, (v) =>
      v.merge({
        data: listRemoveItem(v.data, messageDetailId),
        total: v.total - 1,
      }),
    ),
  );
});

export const receiveMessageDetail = createMessageObservableAction(
  'messageReceiveMessageDetail',
  async (
    dispatch,
    select,
    message: MessageDetailModel | OpenApiModels['POST/message/send/toCustomer/one']['Res']['data'],
    input = {
      thread: {},
    },
    ignoreFetchThreadList = false,
  ) => {
    const ownKey = MessageThreadRecord.ownKey(message.businessId, message.customerId);
    const threadAlreadyExist = select(businessMessageThreadMapBox).hasItem(ownKey);

    let lastBusinessMessageThreadListBox;
    try {
      // 现在新增了 message 级别的筛选，无法确定在有筛选条件的时候，当前新增的消息是否需要显示，所以需要发 getThreadList 请求去确定当前应该展示的 Thread
      // dispatch getThreadList 会在顶部添加一条 Thread，如果不删掉之前的 customer thread，就会有重复数据产生
      // applySuccess 的去重不满足当前场景（不太敢直接改，影响范围太大）
      // 第一条 thread 可以被正确去重，为了避免接受消息时 ThreadList 一直闪烁，增加判断 isFirstThread
      threadAlreadyExist &&
        dispatch(
          businessMessageThreadListBox.updateItem(message.businessId, (v) => {
            lastBusinessMessageThreadListBox = v;
            const isFirstThread = v.data.findIndex((item) => item === message.customerId) === 0;
            return isFirstThread ? v : v.merge({ data: listRemoveItem(v.data, message.customerId) });
          }),
        );
      if (!ignoreFetchThreadList) {
        await dispatch(getThreadList({ loadMode: 'PREPEND', ...input.thread }, message.businessId));
      }
    } catch (e) {
      lastBusinessMessageThreadListBox &&
        dispatch(businessMessageThreadListBox.mergeItem(message.businessId, lastBusinessMessageThreadListBox));
      throw e;
    }

    if (select(messageThreadDetailListBox).mustGetItem(message.customerId).data.includes(message.id)) {
      // pushed already, just merge properties
      dispatch([messageDetailMapBox.mergeItem(message.id, message)]);
      return;
    }
    dispatch([
      // merge message detail
      messageDetailMapBox.mergeItem(message.id, message),
      // update unread count and last message
      businessMessageThreadMapBox.updateItem(ownKey, (v) =>
        v.merge({
          unreadCount:
            message.type === MessageDetailFromType.Customer && !LegacyBool.truly(message.isRead)
              ? v.unreadCount + 1
              : v.unreadCount,
          lastMessageText: message.messageText,
          lastMessageTime: message.createTime,
          lastErrorCode: message.errorCode,
          lastErrorMsg: message.errorMessage,
        }),
      ),

      // push to thread data list
      messageThreadDetailListBox.updateItem(message.customerId, (v) =>
        v.merge({
          data: v.data.push(message.id),
          total: v.total + 1,
        }),
      ),
    ]);
  },
);

// 传入某条消息 id，更新该消息以及该消息之前 (id < messageId) 的 customerIsRead 状态为 2
export const updateMessageReadStatus = createMessageObservableAction(
  'messageUpdateMessageReadStatus',
  async (
    dispatch,
    select,
    readDetail: {
      customerId: number;
      messageId: number;
    },
  ) => {
    const detailList = select(messageThreadDetailListBox).mustGetItem(readDetail.customerId);
    if (!detailList.data.includes(readDetail.messageId)) {
      return;
    }
    const index = detailList.data.indexOf(readDetail.messageId);
    const updateList = detailList.data.slice(0, index + 1);
    updateList.forEach((id) => {
      dispatch(
        messageDetailMapBox.mergeItem(id, {
          customerIsRead: CustomerIsRead.read,
        }),
      );
    });
  },
);

export const updateMessagePageConfig = createMessageObservableAction(
  'messageUpdateMessagePageConfig',
  (
    dispatch,
    select,
    action: PartialProps<MessagePageConfigRecord>,
    businessId: number = select(currentBusinessIdBox),
  ) => {
    dispatch(messagePageConfigMapBox.mergeItem(businessId, action));
    localStorage.setItem(
      LK_MESSAGE_PAGE_CONFIG,
      JSON.stringify(
        Array.from(select(messagePageConfigMapBox).map.values())
          .sort((a, b) => a.updateTime - b.updateTime)
          .slice(0, 5)
          .map(MessagePageConfigRecord.toShortJSON),
      ),
    );
  },
);

export const getMessageReport = createMessageObservableAction('messageGetMessageReport', async (dispatch, _select) => {
  const r = await http.open('GET/message/send/count/report');
  dispatch([
    messageReportMapBox.mergeItemsDeep(
      r.data.map((i) => ({
        ...i,
        ownKey: `${i.companyId}-${i.cycleBeginTime}`,
      })),
    ),
  ]);
  r.data.length > 0 &&
    dispatch(
      messageReportListBox.setList(
        r.data[0]?.companyId ?? ID_INVALID,
        r.data.map((i) => `${i.companyId}-${i.cycleBeginTime}`),
      ),
    );
  return r.data[0] ? `${r.data[0].companyId}-${r.data[0].cycleBeginTime}` : '';
});

export const checkCustomerMessagePermission = action((dispatch, select, customerId: number) => {
  return http.open('GET/message/checkPermission', { customerId }).then((r) => r.data);
});

export const getMessageSummary = action(async (dispatch, select, businessId: number = select(currentBusinessIdBox)) => {
  const r = await http.open('GET/message/count/unread');
  dispatch(messageSummaryMapBox.mergeItem(businessId, r.data));
});

// 如果在 unread 视图，需要移除 thread
export const readAllMessage = createMessageObservableAction(
  'messageReadAllMessage',
  async (dispatch, select, queryType, businessId: number = select(currentBusinessIdBox)) => {
    await http.open('PUT/message/send/set/all/read');

    const threadList = selectBusinessMessageThreadList(select, businessId);
    // find the unread thread, and update it
    threadList.data.forEach((customerId) => {
      const thread = select(selectBusinessMessageThread(customerId));
      const { unreadCount, id } = thread;
      if (unreadCount > 0) {
        dispatch(
          updateThread(id, customerId, MessageThreadUpdateType.Read, queryType === MessageThreadQueryType.Unread),
        );
      }
      // update totalUnread to 0
      dispatch([messageSummaryMapBox.updateItem(businessId, (v) => v.set('totalUnread', 0))]);
    });
  },
);

export const getMessagePlans = action(
  async (dispatch, select, companyId: number, accountId: number = select(currentAccountIdBox)) => {
    const { data } = await http.open('GET/payment/msg/plan', {
      companyId,
    });
    dispatch([
      messagePlanMapBox.mergeItems(data ?? []),
      messagePlanListBox.setList(
        accountId,
        data.map((i) => i.id),
      ),
    ]);
    return data;
  },
);

export const purchaseMessagePlan = createMessageObservableAction(
  'messagePurchaseMessagePlan',
  async (
    dispatch,
    select,
    input: PartialRequired<OpenApiModels['POST/payment/msg/buyMsgPackage']['Req'], 'companyId'>,
  ) => {
    const res = await http.open('POST/payment/msg/buyMsgPackage', input, {
      query: {
        companyId: input.companyId,
      },
    });
    const msgPlans = selectMessagePlans(select).map((id) => select(messagePlanMapBox.mustGetItem(id)));
    const msgPlan = msgPlans.find((item) => item.id === input.msgPlanId);
    await dispatch([
      getCompanyCharges({ companyId: input.companyId }),
      msgBillingMapBox.mergeItemDeep(input.companyId, {
        amount: msgPlan?.amount,
        price: msgPlan?.price,
        autoReload: input.autoReload,
        msgPlanId: input.msgPlanId,
        companyId: input.companyId,
      }),
    ]);
    return res.data;
  },
);

export const getA2PConfig = action(async (dispatch, select, input: OpenApiModels['GET/message/a2p/config']['Req']) => {
  const res = await http.open('GET/message/a2p/config', input);
  return res;
});

export const saveCompanyA2pConfig = action(
  async (dispatch, select, input: OpenApiModels['PUT/message/a2p/submit']['Req']) => {
    await http.open('PUT/message/a2p/submit', input);
    const company = select(companyMapBox).mustGetItem(input.companyId!);
    dispatch(
      companyMapBox.mergeItem(input.companyId!, {
        businessLine: {
          ...company.businessLine,
          a2p: {
            ...company.businessLine.a2p,
            a2pStatus: A2PStatus.Pending,
            step: A2PBannerStepTitleMap.BrandReview,
            withEin: !!input.einFiles?.length,
          },
        },
        a2pAlertBarVisible: false,
      }),
    );
  },
);

export const resendOTPVerificationMessage = action(
  async (dispatch, select, input: OpenApiModels['POST/message/a2p/brand/otp/retry']['Req']) => {
    await http.open('POST/message/a2p/brand/otp/retry', input);
  },
);

export type SendMassTextInput = OpenApiModels['POST/message/send/toCustomer/batch/asyn']['Req'];
export const sendMassText = action(async (dispatch, select, input: SendMassTextInput) => {
  const res = await http.open('POST/message/send/toCustomer/batch/asyn', input);
  return res;
});

export type GetRecoverMassTemplateInput = OpenApiModels['GET/message/template']['Req'];
export type GetRecoverMassTemplateRes = OpenApiModels['GET/message/template']['Res'];
export const getRecoverMassTemplate = action(async (dispatch, select, input: GetRecoverMassTemplateInput) => {
  const res = await http.open('GET/message/template', input);
  return res;
});

export type SendInvoiceEmailReceiptInput = OpenApiModels['POST/message/send/invoice/receipt']['Req'];

export const sendInvoiceEmailReceipt = action(async (dispatch, select, input: SendInvoiceEmailReceiptInput) => {
  await http.open('POST/message/send/invoice/receipt', input);
});

export type SendMultipleReceiptsInput = OpenApiModels['POST/message/send/toCustomer/multipleReceipts']['Req'];

export const sendMultipleReceiptsToCustomer = action(async (dispatch, select, input: SendMultipleReceiptsInput) => {
  await http.open('POST/message/send/toCustomer/multipleReceipts', input);
});

/**
 * Fill current template message with detail from specified target ID and customer, returning filled template message.
 */
export const getTemplateMessagePreview = action(
  async (
    _dispatch,
    _select,
    targetType: number,
    targetId: number,
    messageType: number,
    extParams: {
      orderUuid?: string;
      groomingId?: number;
    },
  ) => {
    const { content } = await http.open('POST/message/auto/template/preview', {
      targetType,
      targetId,
      messageType,
      extParams,
    });
    return content;
  },
);

export const getInvoiceReceiptSentInfo = action(async (dispatch, select, targetType: number, targetId: number) => {
  const rsp = await http.open('POST/message/send/autoMessage/querySent', {
    targetType,
    targetId,
  });

  dispatch(invoiceSentInfoBox.mergeItem(targetId, { createTime: rsp.createTime }));

  return rsp;
});

/**
 * 以彩信形式发送多张图片之前的准备工作：分组 (分组可以减少发送的短信条数消耗)
 */
type ArrangeMultipleImagesIntoGroupInput = OpenApiModels['POST/message/send/mms/group']['Req'];
export const arrangeMultipleImagesIntoGroup = action(
  async (dispatch, select, input: ArrangeMultipleImagesIntoGroupInput) => {
    const result = await http.open('POST/message/send/mms/group', {
      ...input,
    });
    return result;
  },
);

/**
 * 以彩信形式发送多张图片
 */
type SendMultipleImagesToCustomerInput = OpenApiModels['POST/message/send/toCustomer/mms/one']['Req'];
export const sendMultipleImagesToCustomer = createMessageObservableAction(
  'messageSendMultipleImagesToCustomer',
  async (
    dispatch,
    select,
    input: SendMultipleImagesToCustomerInput,
    option: {
      method?: ChannelEnum;
      needGroup?: boolean;
    },
  ) => {
    const { customer, fileInfoGroupList, fileInfoList = [] } = input;
    const { needGroup, method } = option;

    if (!fileInfoList?.length) {
      return fileInfoList?.length;
    }

    const finalFileInfoGroupList = needGroup
      ? await dispatch(arrangeMultipleImagesIntoGroup({ fileInfoList }))
      : fileInfoGroupList;
    const result = await http.open('POST/message/send/toCustomer/mms/one', {
      customer,
      fileInfoList,
      fileInfoGroupList: finalFileInfoGroupList,
    });
    if (method !== ChannelEnum.ppa) {
      const numSegments = result.mmsMessageDetailList.reduce((acc, cur) => acc + cur.numSegments, 0);
      dispatch(afterSendSingleMessage(numSegments));
    }
    // 按照顺序来
    for (const [index, message] of result.mmsMessageDetailList.entries()) {
      /**
       * 由于一组 image messages 里面每个 image message item 都对应的是相同的 thread
       * 所以只让第一条消息 fetch thread list 即可，否则会触发太多次一样的调用
       */
      const ignoreFetchThreadList = index !== 0;
      await dispatch(receiveMessageDetail(message, undefined, ignoreFetchThreadList));
    }
    return finalFileInfoGroupList?.length;
  },
);
