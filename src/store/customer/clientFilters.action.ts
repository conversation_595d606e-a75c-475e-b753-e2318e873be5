import { action } from 'amos';
import { compatibleTransformClientFilter } from '../../container/Client/ClientList/components/FilterItems/FilterProperty.utils';
import { http } from '../../middleware/api';
import { type ComMoegoCommonParamsFilterParamsProperty as FilterParamsProperty } from '../../openApi/customer-schema';
import { type OpenApiDefinitions } from '../../openApi/schema';
import { currentAccountIdBox } from '../account/account.boxes';
import {
  type SaveCustomerActiveSubscriptionListParam,
  saveCustomerActiveSubscriptionList,
} from '../membership/membership.actions';
import { uniq } from '../utils/utils';
import { chosenCustomersMapBox, ChosenCustomersRecord } from './chosenCustomers.boxes';
import { selectChosenCustomers } from './chosenCustomers.selectors';
import {
  type AddFilterModel,
  type ClientFilterListSource,
  ClientFilterListSourceMap,
  ClientFiltersRecord,
  type SyncClientFilterOptions,
  clientFiltersListBox,
  clientFiltersMapBox,
  clientFiltersVisibleBox,
  editingClientFilterBox,
  customerInternalFilter,
  leadInternalFilter,
} from './clientFilters.boxes';
import { selectClientFiltersList } from './clientFilters.selectors';
import { getClientViewList } from './clientView.actions';
import { TEMPORARY_VIEW_ID } from './clientView.constants';
import { transformMembershipSubscription } from './customer.actions';
import { CustomerListFilterRecord, CustomerStatusKinds, businessCustomerListBox } from './customer.boxes';
import { selectBusinessCustomers } from './customer.selectors';

export const toggleClientFiltersVisible = action((dispatch, _select, visible: boolean) => {
  dispatch(clientFiltersVisibleBox.setState(visible));
});

export const addClientFilter = action((dispatch, select, input: AddFilterModel) => {
  const { source, property, viewId = TEMPORARY_VIEW_ID } = input;
  const accountId = select(currentAccountIdBox);
  const ownListKey = ClientFiltersRecord.ownListKey(accountId, source, viewId);
  const propertyKey = ClientFiltersRecord.ownKey(source, property, viewId);
  dispatch([
    clientFiltersListBox.pushList(ownListKey, propertyKey),
    clientFiltersMapBox.mergeItem(propertyKey, {
      ...input,
      ownKey: propertyKey,
    }),
  ]);
});

export const addClientFilterList = action(
  (dispatch, select, input: AddFilterModel[], viewId: number = TEMPORARY_VIEW_ID) => {
    const accountId = select(currentAccountIdBox);
    const propertyList = input.map((item) => ClientFiltersRecord.ownKey(item.source, item.property, viewId));
    const source = input[0].source;
    const curClientFiltersList = select(selectClientFiltersList(source, true, viewId));
    const ownListKey = ClientFiltersRecord.ownListKey(accountId, source, viewId);

    dispatch([
      clientFiltersListBox.setList(ownListKey, uniq(curClientFiltersList.concat(propertyList))),
      clientFiltersMapBox.mergeItems(
        input.map((item) => ({
          ...item,
          ownKey: ClientFiltersRecord.ownKey(item.source, item.property, viewId),
        })),
      ),
    ]);
  },
);

export const removeClientFilter = action(
  (
    dispatch,
    select,
    source: ClientFilterListSource,
    property: FilterParamsProperty,
    viewId: number = TEMPORARY_VIEW_ID,
  ) => {
    const accountId = select(currentAccountIdBox);
    const ownListKey = ClientFiltersRecord.ownListKey(accountId, source, viewId);
    const propertyKey = ClientFiltersRecord.ownKey(source, property, viewId);

    dispatch([clientFiltersListBox.deleteItem(ownListKey, propertyKey), clientFiltersMapBox.deleteItem(propertyKey)]);
  },
);

export const removeAllClientFilter = action(
  (dispatch, select, source: ClientFilterListSource, viewId: number = TEMPORARY_VIEW_ID) => {
    const accountId = select(currentAccountIdBox);
    const clientFiltersList = select(selectClientFiltersList(source, true, viewId));
    const ownListKey = ClientFiltersRecord.ownListKey(accountId, source, viewId);

    clientFiltersList.forEach((propertyKey) => {
      dispatch(clientFiltersMapBox.deleteItem(propertyKey));
    });
    dispatch(clientFiltersListBox.deleteList(ownListKey));
    if (source === ClientFilterListSourceMap.ClientList) {
      dispatch(
        addClientFilter({
          source,
          viewId,
          ...customerInternalFilter,
        }),
      );
    }
    if (source === ClientFilterListSourceMap.LeadList) {
      dispatch(
        addClientFilter({
          source,
          viewId,
          ...leadInternalFilter,
        }),
      );
    }
  },
);

/**
 * @description 同步客户列表的筛选条件到另一个客户列表
 * !important 使用此API的时候，必须明确标记 {@link ClientFilterListSourceMap} 的 useStandardOperator
 * 这里的 compatible 有存在的必要么，存在不需要兼容的情况吗？
 */
export const syncClientListFilterNSelection2Source = action((dispatch, select, options: SyncClientFilterOptions) => {
  const { fromSource, fromViewId, toSource, toViewId } = options || {};
  const accountId = select(currentAccountIdBox);
  const clientListFilters = select(selectClientFiltersList(fromSource, true, fromViewId));
  const chosenCustomers = select(selectChosenCustomers(fromSource, fromViewId));
  const keyword = select(selectBusinessCustomers(fromSource, fromViewId)).filter.queries?.keyword ?? '';
  const clientFiltersMap = select(clientFiltersMapBox);
  const targetFilterList = clientListFilters.map((sourceProperty) =>
    ClientFiltersRecord.syncPropertyKey(sourceProperty, options),
  );
  const targetFilterItems = clientListFilters
    .map((sourceProperty) => {
      const filter = clientFiltersMap.mustGetItem(sourceProperty);
      // !important: 表格之间同步的时候
      const compatibleFilter = options.compatible
        ? compatibleTransformClientFilter({ fromSource, toSource, filter })
        : filter.toJSON();

      const ownKey = ClientFiltersRecord.syncPropertyKey(sourceProperty, options);

      return {
        ...compatibleFilter,
        ownKey,
      };
    })
    .toArray();

  const filtersListOwnKey = ClientFiltersRecord.ownListKey(accountId, toSource, toViewId);
  const chosenOwnKey = ChosenCustomersRecord.ownKey(accountId, toSource, toViewId);
  const businessCustomerListOwnKey = CustomerListFilterRecord.ownKey(accountId, toSource, toViewId);

  dispatch([
    clientFiltersListBox.setList(filtersListOwnKey, targetFilterList),
    clientFiltersMapBox.mergeItems(targetFilterItems),
    chosenCustomersMapBox.mergeItem(chosenOwnKey, chosenCustomers.toJSON()),
    businessCustomerListBox.updateItem(businessCustomerListOwnKey, (v) => v.applyStart({ queries: { keyword } })),
  ]);
});

export const setEditingFilter = action((dispatch, select, property?: FilterParamsProperty) => {
  const accountId = select(currentAccountIdBox);
  dispatch(editingClientFilterBox.setItem(accountId, { accountId, property }));
});

export type CustomerBookingSearchListDto =
  OpenApiDefinitions['customer']['com.moego.server.customer.service.dto.CustomerBookingSearchListDto'];

export const clientBookSearch = action(async (dispatch, _select, keyword?: string, signal?: AbortSignal) => {
  const result: { data: CustomerBookingSearchListDto } = await http.open(
    'GET/customer/booknew/search',
    { keyword },
    { signal },
  );
  const membershipSubscriptionInfo: SaveCustomerActiveSubscriptionListParam[] = result.data.customerList.map(
    (clientInfo: CustomerBookingSearchListDto['customerList'][number]) => {
      return {
        customerId: clientInfo.customerId + '',
        membershipSubscriptions: clientInfo.membershipSubscriptions?.map(transformMembershipSubscription) || [],
      };
    },
  );
  await dispatch(saveCustomerActiveSubscriptionList(membershipSubscriptionInfo));
  return result.data.customerList;
});

export const getView = action(
  async (dispatch, _, viewTitle: string = CustomerStatusKinds.mapLabels[CustomerStatusKinds.activeClient]) => {
    const source = ClientFilterListSourceMap.ClientList;
    const viewList = await dispatch(getClientViewList(source));

    return viewList.find((view) => view.title.toLowerCase() === viewTitle.toLowerCase());
  },
);
