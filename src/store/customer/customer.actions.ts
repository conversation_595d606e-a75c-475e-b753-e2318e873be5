/*
 * @since 2021-02-08 10:09:31
 * <AUTHOR> <<EMAIL>>
 */

import { CalendarPeriod } from '@moego/api-web/google/type/calendar_period';
import { type MembershipSubscriptionModel } from '@moego/api-web/moego/models/membership/v1/subscription_models';
import { UserType } from '@moego/api-web/moego/models/subscription/v1/subscription_models';
import { action, type Dispatchable } from 'amos';
import ReactDOM from 'react-dom';
import dayjs from 'dayjs';
import { isNil, omit, upperFirst } from 'lodash';
import { toastApi } from '../../components/Toast/Toast';
import { http } from '../../middleware/api';
import { BusinessCustomerClient, FinanceSubscriptionClient } from '../../middleware/clients';
import { ErrorCodes } from '../../middleware/codes';
import { type OpenApiDefinitions, type OpenApiModels } from '../../openApi/schema';
import { id2NumberAndRequired, type IdToNumberAndRequired } from '../../utils/api/api';
import { openMoney2GrpcMoney } from '../../utils/api/utils';
import { omitEmpty } from '../../utils/misc';
import { currentAccountIdBox } from '../account/account.boxes';
import { currentBusinessIdBox } from '../business/business.boxes';
import {
  saveCustomerActiveSubscriptionList,
  type SaveCustomerActiveSubscriptionListParam,
} from '../membership/membership.actions';
import { createCustomerObservableAction } from '../observableServices/observableServices';
import { customerPetListBox, petMapBox, petSummaryListBox } from '../pet/pet.boxes';
import { petCodeCommentMapBox, petCodeMapBox } from '../pet/petCode.boxes';
import { petVaccineMapBox } from '../pet/petVaccine.boxes';
import { petVaccineBindingListBox, petVaccineBindingMapBox } from '../pet/petVaccineBinding.boxes';
import { type PagedInput } from '../utils/PagedList';
import { type PartialProps, type PartialRequired } from '../utils/RecordMap';
import { flat, get, mapAssign, mapGet } from '../utils/utils';
import { customerInternalFilter, type ClientFilterListSource } from './clientFilters.boxes';
import { TEMPORARY_VIEW_ID } from './clientView.constants';
import { DEFAULT_REQUEST_FILTER } from './clientView.utils';
import {
  CustomerListFilterRecord,
  businessCustomerListBox,
  customerCreditBox,
  customerMapBox,
  type CustomerListFilterModel,
} from './customer.boxes';
import { customerAddressListBox, customerAddressMapBox } from './customerAddress.boxes';
import { CustomerContactKinds, customerContactListBox, customerContactMapBox } from './customerContact.boxes';
import { customerNoteListBox, customerNoteMapBox } from './customerNote.boxes';
import { customerTagMapBox } from './customerTag.boxes';

export type GetCustomerListInput = PagedInput<PartialProps<CustomerListFilterRecord>> & {
  source?: ClientFilterListSource;
  viewId?: number;
  specificCustomerList?: string[];
};

let customerListAbortController: AbortController | null = null;
let dismissCatchOnce = false;

function dayNumber2String(day: string | number | undefined): string | undefined {
  if (isNil(day)) {
    return undefined;
  }
  return dayjs(+day * 1000).toJSON();
}

/**
 * 注：rest 接口返回的 membership 没有 totalPrice 和 totalTax 字段，这里会忽略
 */
export function transformMembershipSubscription({
  membership,
  subscription,
}: OpenApiModels['POST/customer/smart-list']['Res']['clientPage']['dataList'][number]['membershipSubscriptions'][number]): MembershipSubscriptionModel {
  return {
    // fix types:这里字段B web暂时没有用到，先传空
    perkDetail: [],
    membership: {
      ...membership,
      id: membership.id + '',
      companyId: membership.companyId + '',
      internalProductId: membership.internalProductId + '',
      priceId: membership.priceId + '',
      taxId: membership.taxId + '',
      createdAt: dayNumber2String(membership.createdAt),
      updatedAt: dayNumber2String(membership.updatedAt),
      deletedAt: dayNumber2String(membership.deletedAt),
      totalPrice: openMoney2GrpcMoney(membership.totalPrice),
      totalTax: openMoney2GrpcMoney(membership.totalTax),
    } as unknown as MembershipSubscriptionModel['membership'],
    subscription: {
      ...subscription,
      id: subscription.id + '',
      subStatus: subscription.subStatus,
      latestOrderId: subscription.latestOrderId + '',
      companyId: subscription.companyId + '',
      customerId: subscription.customerId + '',
      businessId: subscription.businessId + '',
      membershipId: subscription.membershipId + '',
      internalSubscriptionId: subscription.internalSubscriptionId + '',
      validityPeriod: {
        startTime: dayNumber2String(subscription.validityPeriod?.startTime),
        endTime: dayNumber2String(subscription.validityPeriod?.endTime),
      },
      nextBillingDate: dayNumber2String(subscription.nextBillingDate),
      expiresAt: dayNumber2String(subscription.expiresAt),
      sellLinkId: subscription.sellLinkId + '',
      createdAt: dayNumber2String(subscription.createdAt),
      updatedAt: dayNumber2String(subscription.updatedAt),
      deletedAt: dayNumber2String(subscription.deletedAt),
      // billingCyclePeriod: membership.billingCyclePeriod,
      billingCyclePeriod: { period: CalendarPeriod.DAY, value: 0 },
      pausedAt: undefined,
      autoResumeAt: undefined,
      autoResumeSetting: {
        numOfBillingCycle: undefined,
        date: undefined,
      },
      cancelReason: '',
    },
  };
}

export const mergeListFilter = (listFilter: GetCustomerListInput): CustomerListFilterModel => {
  // const filterSource = listFilter.source ?? ClientFilterListSourceMap.ClientList;
  // const useInternalFilters = listFilter.useInternalFilters ?? true;
  // const internalFilters = useInternalFilters
  //   ? ClientFilterListSourceMap.mapLabels[filterSource].internalFilters ?? []
  //   : [];
  if (listFilter.source) {
    return listFilter;
  }
  const filters = [customerInternalFilter, ...(listFilter.filters?.filters ?? [])];
  return {
    ...listFilter,
    filters: isNil(listFilter.filters)
      ? {
          ...DEFAULT_REQUEST_FILTER,
          filters,
        }
      : {
          ...listFilter.filters,
          filters,
        },
  };
};

export const getCustomerList = createCustomerObservableAction(
  'customerGetCustomerList',
  async (dispatch, select, input: GetCustomerListInput) => {
    const accountId = select(currentAccountIdBox);
    const ownKey = CustomerListFilterRecord.ownKey(accountId, input.source, input.viewId);
    const state = select(businessCustomerListBox.mustGetItem(ownKey));
    const originListFilter = state.getFilter(input);
    const listFilter = mergeListFilter(originListFilter);
    const abortController = new AbortController();

    // will not update businessCustomerListBox when cancel repeat request, to keep loading status
    dismissCatchOnce = !!customerListAbortController;
    customerListAbortController?.abort();
    customerListAbortController = abortController;
    dispatch(businessCustomerListBox.updateItem(ownKey, (v) => v.applyStart(input)));
    try {
      const resOrig = await http.open('POST/customer/smart-list', listFilter, {
        autoToast: false,
        signal: abortController.signal,
      });
      const r = id2NumberAndRequired(resOrig);
      customerListAbortController = null;

      const membershipSubscriptionInfo: SaveCustomerActiveSubscriptionListParam[] = r.clientPage.dataList.map(
        (clientInfo) => {
          return {
            customerId: clientInfo.customerId + '',
            membershipSubscriptions: clientInfo.membershipSubscriptions?.map(transformMembershipSubscription) || [],
          };
        },
      );

      dispatch([
        businessCustomerListBox.updateItem(ownKey, (v) =>
          v.applySuccess(
            r.clientPage.dataList.map(get('customerId')),
            r.clientPage.total,
            originListFilter.pageNum,
            !!input.clear,
          ),
        ),
        customerMapBox.mergeItems(
          r.clientPage.dataList.map((c) => ({
            ...c,
            totalPaid: c.totalPaid,
            totalBookings: c.totalApptCount,
            upcomingBookingTime: c.upcomingBooking,
          })),
        ),
        petMapBox.mergeItems(flat(r.clientPage.dataList.map((c) => c.petList))),
        customerPetListBox.setLists(r.clientPage.dataList.map((c) => [c.customerId, c.petList.map(get('petId'))])),
        petSummaryListBox.setList(
          accountId,
          [
            {
              petTypeId: -1,
              typeName: 'Total clients',
              count: r.clientPage.total,
            },
          ].concat(r.petCount ?? []),
        ),
        saveCustomerActiveSubscriptionList(membershipSubscriptionInfo),
      ]);
      return r;
    } catch (e: any) {
      if (!dismissCatchOnce) {
        customerListAbortController = null;
        dispatch(businessCustomerListBox.updateItem(ownKey, (v) => v.applyFail(originListFilter.pageNum)));
        if (e?.data?.code === ErrorCodes.PERMISSION_NOT_ENOUGH) {
          // 如果突然没有权限了，就清空列表，QA 说这样比较好
          // 当跨浏览器操作时会发生
          dispatch(businessCustomerListBox.deleteItem(ownKey));
        }
        const message =
          e?.data?.message ||
          (typeof e?.data?.data === 'string' ? e.data.data : ErrorCodes.mapLabels[ErrorCodes.INTERNAL]);
        if (message) {
          toastApi.error(upperFirst(message));
        }
      }
      // enable last request can be catch
      dismissCatchOnce = false;
      return null;
    }
  },
);

/**
 * 初始化推荐使用 useGetCredit 根据白名单控制是否获取 credit
 */
export const getCredit = action(async (dispatch, _select, customerId: number) => {
  const res = await FinanceSubscriptionClient.getCredit({
    user: {
      id: String(customerId),
      type: UserType.CUSTOMER,
    },
  });

  dispatch([
    customerCreditBox.mergeItem(customerId, {
      credit: res.credit,
      customerId,
    }),
  ]);
});

export const getCustomerTotal = action(async (_dispatch, select, input: GetCustomerListInput) => {
  const accountId = select(currentAccountIdBox);
  const ownKey = CustomerListFilterRecord.ownKey(accountId, input.source, input.viewId);
  const state = select(businessCustomerListBox.mustGetItem(ownKey));
  const filter = state.getFilter(input);
  const controller = new AbortController();

  const resOrig = await http.open(
    'POST/customer/smart-list',
    {
      ...filter,
      pageSize: 1,
      pageNum: 1,
    },
    {
      autoToast: false,
      signal: controller.signal,
    },
  );
  const r = id2NumberAndRequired(resOrig);
  return r.clientPage.total;
});

export const importCustomer = action(async (_dispatch, _select, filePath: string) => {
  await http.open('POST/customer/import', { filePath });
});

export const getCustomer = action(async (dispatch, _select, customerId: number) => {
  const [rOrig, preferredTipOrig] = await Promise.all([
    http.open('GET/customer/basic', { customerId }),
    http.open('GET/customer/preferredTip', { customerId }),
  ]);
  const r = id2NumberAndRequired(rOrig);
  const preferredTip = id2NumberAndRequired(preferredTipOrig);
  dispatch(customerMapBox.mergeItem(customerId, omitEmpty({ ...r.data, preferredTip })));
  return omitEmpty({ ...r.data, preferredTip });
});

export type CustomerWithPetForMessageDto = IdToNumberAndRequired<
  OpenApiDefinitions['customer']['com.moego.server.customer.dto.CustomerWithPetForMessageDto']
>;

export const getCustomerOverviewForMessage = createCustomerObservableAction(
  'customerGetCustomerOverviewForMessage',
  async (
    dispatch,
    _select,
    customerId: number,
    { ignorePreferredTip = false }: { ignorePreferredTip?: boolean } = {},
  ) => {
    const [r, preferredTip] = await Promise.all([
      http.open('GET/customer/detail/withOverview', { customerId }),
      ignorePreferredTip ? undefined : http.open('GET/customer/preferredTip', { customerId }),
    ]);
    if (!r) {
      return;
    }
    const {
      data: {
        customerDetail,
        nextAppointment,
        contactList,
        customerTotal: { payments, appointmentInfo, avgReview },
        lastAppointment,
        petList = [],
        tagList = [],
        lastAlertNote,
      },
    } = r;
    const preferredTipObj = ignorePreferredTip ? {} : { preferredTip };
    ReactDOM.unstable_batchedUpdates(() => {
      dispatch([
        customerMapBox.mergeItem(
          customerId,
          id2NumberAndRequired({
            ...omitEmpty(customerDetail),
            lastAppointment,
            nextAppointment,
            primaryAddressId: customerDetail.address?.customerAddressId,
            primaryContactId: contactList.find((c) => c.isPrimary)?.contactId,
            customerTagIdList: tagList.map(get('id')),
            totalPaid: payments.totalPaid,
            totalOutstandingBalance: payments.totalOutstandingBalance,
            avgReview,
            totalBookings: appointmentInfo.totalAppts,
            upcomingBookings: appointmentInfo.upcoming,
            finishedBookings: appointmentInfo.finished,
            cancelledBookings: appointmentInfo.cancelled,
            noShowBookings: appointmentInfo.noShow,
            lastAlertNote,
            ...preferredTipObj,
          }),
        ),
        customerTagMapBox.mergeItems(tagList.map(omitEmpty)),
        customerContactMapBox.mergeItems(
          contactList.map((p) => ({ ...p, customerContactId: p.contactId, customerId })),
        ),
        customerContactListBox.setList(customerId, contactList.map(get('contactId'))),
        petMapBox.mergeItems(
          petList.map((p) => ({
            ...omit(p.petDetail, 'playgroupId'),
            customerId,
            expiryNotification: p.petDetail.expiryNotification ?? 1,
            petCodeIdList: p.petCodeList.map((p) => p.petCodeId),
          })),
        ),
        customerPetListBox.setList(
          customerId,
          petList.map((p) => p.petDetail.petId),
        ),
        petCodeMapBox.mergeItems(flat(petList.map((p) => p.petCodeList.map((p) => ({ ...p, id: p.petCodeId }))))),
        petCodeCommentMapBox.mergeItems(
          petList.flatMap((pet) =>
            pet.petCodeList.map((p) => ({
              petIdAddCodeId: `${pet.petDetail.petId}+${p.petCodeId}`,
              comment: pet.petCodeBindingList.find((b) => b.codeId === p.petCodeId)?.comment,
            })),
          ),
        ),
        petVaccineMapBox.mergeItems(
          flat(
            petList.map((p) =>
              p.petVaccineList.map((v) => ({
                id: v.vaccineId,
                name: v.vaccineName,
              })),
            ),
          ),
        ),
        petVaccineBindingMapBox.mergeItems(
          flat(petList.map((p) => mapAssign(p.petVaccineList, { petId: p.petDetail.petId }))),
        ),
        petVaccineBindingListBox.setLists(
          petList.map((p) => [p.petDetail.petId, p.petVaccineList.map((p) => p.vaccineBindingId)]),
        ),
        customerAddressMapBox.mergeItems(customerDetail.address ? [{ ...customerDetail.address, customerId }] : []),
      ] as Dispatchable[]);
    });

    return id2NumberAndRequired(r.data);
  },
);

export const getCustomerOverview = createCustomerObservableAction(
  'customerGetCustomerOverview',
  async (dispatch, _select, customerId: number) => {
    const [overviewOrig, preferredTipOrig] = await Promise.all([
      http.open('GET/customer/overview', { customerId }),
      http.open('GET/customer/preferredTip', { customerId }),
    ]);
    const {
      data: { customerDetail, petList, appointmentInfo, customerTags, payments, avgReview },
    } = id2NumberAndRequired(overviewOrig);
    const preferredTip = id2NumberAndRequired(preferredTipOrig);
    dispatch([
      petCodeMapBox.mergeItems(flat(petList.map((p) => p.petCodeList.map((p) => ({ ...p, id: p.petCodeId }))))),
      petCodeCommentMapBox.mergeItems(
        petList.flatMap((pet) =>
          pet.petCodeList.map((p) => ({
            petIdAddCodeId: `${pet.petDetail.petId}+${p.petCodeId}`,
            comment: pet.petCodeBindingList.find((b) => b.codeId === p.petCodeId)?.comment,
          })),
        ),
      ),
      petMapBox.mergeItems(
        petList.map((p) => ({
          ...p.petDetail,
          expiryNotification: p.vaccineStatus.expiryNotification,
          haveExpiredVaccineBinding: p.vaccineStatus.haveExpired,
          haveVaccineBinding: p.vaccineStatus.haveVaccineBinding,
          petCodeIdList: p.petCodeList.map((p) => p.petCodeId),
        })),
      ),
      customerPetListBox.setList(
        customerId,
        petList.map((p) => p.petDetail.petId),
      ),
      customerMapBox.mergeItem(
        customerId,
        omitEmpty({
          ...customerDetail,
          upcomingBookings: appointmentInfo.upcoming,
          finishedBookings: appointmentInfo.finished,
          cancelledBookings: appointmentInfo.cancelled,
          noShowBookings: appointmentInfo.noShow,
          totalBookings: appointmentInfo.totalAppts,
          totalPaid: payments.totalPaid,
          totalOutstandingBalance: payments.totalOutstandingBalance,
          avgReview: avgReview,
          customerTagIdList: customerTags.map((c) => c.id),
          primaryAddressId: customerDetail.address?.customerAddressId,
          preferredTip,
        }),
      ),
      customerTagMapBox.mergeItems(customerTags.map((t) => omitEmpty({ ...t }))),
      customerAddressMapBox.mergeItems(customerDetail.address ? [{ ...customerDetail.address, customerId }] : []),
    ]);
  },
);

export const getCustomerDetail = createCustomerObservableAction(
  'customerGetCustomerDetail',
  async (
    dispatch,
    _select,
    customerId: number,
    { ignorePreferredTip = false }: { ignorePreferredTip?: boolean } = {},
  ) => {
    const [detailOrig, preferredTipOrig] = await Promise.all([
      http.open('GET/customer/page/detail', { customerId }),
      ignorePreferredTip
        ? undefined
        : http.open('GET/customer/preferredTip', {
            customerId,
          }),
    ]);
    const {
      data: { contactList, addressList, customerInfo, customerNoteList, customerTagList },
    } = id2NumberAndRequired(detailOrig);
    const preferredTip = id2NumberAndRequired(preferredTipOrig);
    const preferredTipObj = ignorePreferredTip ? {} : { preferredTip };
    dispatch([
      customerContactMapBox.mergeItems(mapAssign(contactList, { customerId })),
      customerContactListBox.setList(customerId, mapGet(contactList, 'customerContactId')),
      customerAddressMapBox.mergeItems(mapAssign(addressList, { customerId })),
      customerAddressListBox.setList(customerId, mapGet(addressList, 'customerAddressId')),
      customerNoteMapBox.mergeItems(mapAssign(customerNoteList, { customerId })),
      customerNoteListBox.setList(customerId, customerNoteList.map(get('customerNoteId'))),
      customerTagMapBox.mergeItems(customerTagList.map((t) => omitEmpty({ ...t }))),
      customerMapBox.mergeItem(customerId, {
        ...customerInfo,
        ownerPhoneNumber: contactList.find((c) => c.type === CustomerContactKinds.Owned)?.phoneNumber,
        primaryAddressId: addressList.find((c) => c.isPrimary)?.customerAddressId,
        primaryContactId: contactList.find((c) => c.isPrimary)?.customerContactId,
        customerTagIdList: customerTagList.map((c) => c.id),
        ...preferredTipObj,
      }),
    ]);
  },
);

export const getCustomerContactList = createCustomerObservableAction(
  'customerGetCustomerContactList',
  async (dispatch, _select, customerId: number) => {
    const contactList = await http.open('GET/customer/contacts', { customerId });
    dispatch([
      customerContactMapBox.mergeItems(mapAssign(contactList, { customerId })),
      customerContactListBox.setList(customerId, mapGet(contactList, 'customerContactId')),
    ]);
    return contactList;
  },
);

type UpdateCustomerInput = PartialRequired<
  OpenApiDefinitions['customer']['com.moego.server.customer.params.UpdateCustomerInfoParams'],
  'customerId'
>;

export const updateCustomer = createCustomerObservableAction(
  'customerUpdateCustomer',
  async (
    dispatch,
    select,
    input: PartialRequired<UpdateCustomerInput, 'customerId'>,
    options?: {
      autoToast?: boolean;
    },
  ) => {
    await http.open('PUT/customer/detail', input, options);
    const contacts = select(customerContactMapBox);
    const contactId = select(customerContactListBox)
      .getList(input.customerId)
      .find((c) => {
        return contacts.mustGetItem(c).type === CustomerContactKinds.Owned;
      });
    dispatch([
      customerMapBox.mergeItem(input.customerId, input),
      customerContactMapBox.mergeItems(contactId ? [{ ...input, customerContactId: contactId }] : []),
    ]);
  },
);

export const clearCustomerList = action(
  (dispatch, select, source?: ClientFilterListSource, viewId: number = TEMPORARY_VIEW_ID) => {
    const accountId = select(currentAccountIdBox);
    const ownKey = CustomerListFilterRecord.ownKey(accountId, source, viewId);
    dispatch(businessCustomerListBox.deleteItem(ownKey));
  },
);

type UpdateCustomerPreferredTipInput = {
  customerId: number;
  preferredTip: Pick<
    OpenApiDefinitions['customer']['com.moego.server.customer.params.PreferredTipConfigParams'],
    'amount' | 'enable' | 'percentage' | 'tipType'
  >;
};

export const updateCustomerPreferredTip = action(async (dispatch, _select, input: UpdateCustomerPreferredTipInput) => {
  const preferredTip = await http.open('PUT/customer/preferredTip', {
    ...input.preferredTip,
    customerId: input.customerId,
  });
  dispatch(customerMapBox.mergeItem(input.customerId, { preferredTip }));
});

export const removeCustomer = action(async (_dispatch, _select, id: number) => {
  await http.open('DELETE/customer', { id });
});

export type AddCustomerWithPetsInput = OpenApiModels['POST/customer/withPet']['Req'];

export const addCustomerWithPets = action(async (dispatch, select, input: AddCustomerWithPetsInput) => {
  const preferredBusinessId = input.preferredBusinessId || select(currentBusinessIdBox) + '';
  const r = await http.open('POST/customer/withPet', { ...input, preferredBusinessId });
  if (r.data.addCofResult === false) {
    toastApi.neutral('Failed to add card on file for the client. Please try again with a different card.', 5000);
  }
  dispatch([
    customerMapBox.mergeItem(r.data.id, input),
    petMapBox.mergeItems(input?.petList?.map((p, i) => ({ ...p, petId: r.data.petIdList[i] })) ?? []),
    customerPetListBox.setList(r.data.id, r.data.petIdList),
  ]);
  return r;
});

export const getReviewRecordCount = action(async (_dispatch, _select, customerId: number) => {
  const r = await http.open('GET/message/review/booster/count', { customerId });
  return r.data;
});

export const getReviewRecordList = action(
  async (_dispatch, _select, customerId: number, pageNo: number, pageSize: number) => {
    const r = await http.open('GET/message/review/booster/record', { customerId, pageNo, pageSize });
    return id2NumberAndRequired(r.data);
  },
);

export const sendReviewBooster = action(async (_dispatch, _select, customerId: number, businessId?: number) => {
  const r = await http.open('POST/message/send/services/message', {
    targetType: 40,
    method: 1,
    customer: {
      customerId,
    },
    businessId,
  });
  return id2NumberAndRequired(r.data);
});

export const getReviewBoosterConfig = action(async (_dispatch, _select) => {
  const r = await http.open('GET/message/review/booster/get');
  return id2NumberAndRequired(r.data);
});

export const getAllClientInfo = createCustomerObservableAction(
  'customerGetAllClientInfo',
  async (dispatch, _select, clientId: number) => {
    return await Promise.all(
      dispatch([
        getCustomer(clientId),
        // 因为第一个已经拉了GET/customer/preferredTip，所以后面连个不用重复拉
        getCustomerDetail(clientId, { ignorePreferredTip: true }),
        getCustomerOverviewForMessage(clientId, { ignorePreferredTip: true }),
      ]),
    );
  },
);

export const updateCustomerPreferredBusiness = action(
  async (
    dispatch,
    _select,
    input: {
      customerId: number;
      preferredBusinessId: string;
    },
  ) => {
    await BusinessCustomerClient.updateCustomerPreferredBusiness({
      customerId: input.customerId + '',
      preferredBusinessId: input.preferredBusinessId,
    });

    dispatch(customerMapBox.mergeItem(input.customerId, input));
  },
);

/**
 * 获取当前company下的client总数
 */
export const getCompanyCustomerTotalCount = action(async () => {
  const { count } = await http.open('POST/customer/list/total', {
    pageNum: 1,
    pageSize: 1,
  });
  return count;
});
