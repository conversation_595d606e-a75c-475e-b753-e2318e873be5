/*
 * @since 2024-04-06 19:11:04
 * <AUTHOR> <<EMAIL>>
 */

import { type AddTodoRequest, type UpdateTodoRequest } from '@moego/api-web/moego/api/todo/v1/todo_api';
import { action } from 'amos';
import { TodoClient } from '../../middleware/clients';
import { currentAccountIdBox } from '../account/account.boxes';
import { get } from '../utils/utils';
import { todoMapBox, userTodoListBox } from './todo.boxes';

export const getTodoList = action(async (dispatch, select, userId: number = select(currentAccountIdBox)) => {
  const { todoList } = await TodoClient.listTodo({});
  dispatch([
    todoMapBox.mergeItems(todoList),
    userTodoListBox.setList(userId as unknown as string, todoList.map(get('id'))),
  ]);
}).config({
  queryDeps: (userId) => (userId === void 0 ? [currentAccountIdBox] : []),
});

export const getTodo = action(async (dispatch, select, todoId: string) => {
  const todo = await TodoClient.getTodo({ id: todoId });
  dispatch(todoMapBox.mergeItem(todoId, todo));
});

export const addTodo = action(
  async (dispatch, select, input: AddTodoRequest, userId: number = select(currentAccountIdBox)) => {
    const todo = await TodoClient.addTodo(input);
    dispatch([todoMapBox.mergeItem(todo.id, todo), userTodoListBox.unshiftList(userId as unknown as string, todo.id)]);
  },
);

export const updateTodo = action(async (dispatch, select, input: UpdateTodoRequest) => {
  const todo = await TodoClient.updateTodo(input);
  dispatch(todoMapBox.mergeItem(todo.id, todo));
});

export const deleteTodo = action(
  async (dispatch, select, todoId: string, userId: number = select(currentAccountIdBox)) => {
    await TodoClient.deleteTodo({ id: todoId });
    dispatch(userTodoListBox.deleteItem(userId as unknown as string, todoId));
  },
);
