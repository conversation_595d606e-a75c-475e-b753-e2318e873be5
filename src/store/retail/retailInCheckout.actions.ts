import { action } from 'amos';
import { http } from '../../middleware/api';
import { type OpenApiModels } from '../../openApi/schema';
import { type ID } from '../../types/common';
import { getCheckoutAvailableDiscountList } from '../discount/discount.actions';
import { getGroomingTicketInvoice } from '../grooming/grooming.actions';
import { autoApplyInvoiceMembership } from '../membership/membershipApply.actions';
import { invoiceRefundMapBox } from '../payment/payment.boxes';

export const getOrderRetailProducts = action(async (dispatch, select, orderId: number) => {
  const productList = await http.open('GET/retail/order/items/detail', { orderId });
  return productList;
});

export type ProductDetail = OpenApiModels['GET/retail/order/items/detail']['Res'][number];
export type EditOrderRetailProductItemInput = Pick<ProductDetail, 'orderItemId'> &
  Partial<Pick<ProductDetail, 'quantity' | 'lineDiscounts' | 'lineTaxes' | 'unitPrice' | 'staffId'>>;

export interface LineDiscountProps {
  id?: ID;
  discountType: string;
  discountAmount?: number;
  discountRate?: number;
  isDeleted?: boolean;
}

export interface LineTaxesProps {
  id?: number;
  taxId: number;
  taxRate: number;
  isDeleted?: boolean;
}

export type ProductFromSearchType = QueryProductListRes[0];

export type ProductType = Partial<Omit<ProductDetail, 'lineDiscounts' | 'lineTaxes'>> & {
  objectId: number;
  name: string;
  unitPrice: number;
  sku: string;
  stock: number;
  staffId: number;
  lineDiscounts?: LineDiscountProps[];
  lineTaxes?: LineTaxesProps[];
};

export type updateOrderRetailItemsInput = OpenApiModels['PUT/retail/order/items/v2']['Req'] & {
  orderId: number;
};

export type SetAllRetailProductOrServiceDiscountInput = OpenApiModels['PUT/retail/order/set-discount']['Req'] & {
  orderId: number;
};

export type UpdateInputRetailItems = OpenApiModels['PUT/retail/order/items/v2']['Req']['retailItems'];

export type QueryProductListRes = OpenApiModels['GET/retail/product/query']['Res']['resultList'];

export const queryRetailProducts = action(
  async (dispatch, select, input: OpenApiModels['GET/retail/product/query']['Req'], signal?: AbortSignal) => {
    const res = await http.open('GET/retail/product/query', { pageSize: 16, ...input }, { signal });
    return res;
  },
);

// add /update product to invoice、remove product from invoice
export const updateOrderRetailItems2Invoice = action(
  async (
    dispatch,
    select,
    input: updateOrderRetailItemsInput,
    opt?: {
      /** 是否自动 apply membership， */
      autoApplyMembership?: boolean;
    },
  ) => {
    const res = await http.open('PUT/retail/order/items/v2', { ...input, checkRefund: true });
    if (opt?.autoApplyMembership) {
      // 自动 apply membership, 该操作完成后会自动刷新 invoice
      await dispatch(autoApplyInvoiceMembership(input.orderId));
    } else {
      await dispatch(getGroomingTicketInvoice(input.orderId));
    }

    if (res?.refundChannel?.channelList?.length) {
      dispatch(
        invoiceRefundMapBox.mergeItem(input.orderId, {
          refundDetails: res?.refundChannel,
          callback: async () => await dispatch(updateOrderRetailItems2Invoice(input)),
        }),
      );
    }

    return res;
  },
);

// set discount of all product、service or the order
export const setAllRetailProductOrServiceDiscount = action(
  async (dispatch, select, input: SetAllRetailProductOrServiceDiscountInput, onRefund?: () => void) => {
    const res = await http.open('PUT/retail/order/set-discount/v2', { ...input, checkRefund: true });
    await dispatch(getGroomingTicketInvoice(input.orderId));

    if (res?.refundChannel?.channelList?.length) {
      dispatch(
        invoiceRefundMapBox.mergeItem(input.orderId, {
          refundDetails: res?.refundChannel,
          callback: async () => {
            await dispatch(setAllRetailProductOrServiceDiscount(input));
            onRefund?.();
          },
        }),
      );
    }

    return res;
  },
);

export const removeInvoiceDiscount = action(
  async (dispatch, select, { orderId, discountId }: { orderId: number; discountId: ID }) => {
    await dispatch(
      setAllRetailProductOrServiceDiscount({
        orderId,
        lineDiscounts: [
          {
            isDeleted: true,
            id: discountId,
          },
        ],
      } as SetAllRetailProductOrServiceDiscountInput),
    );
    dispatch(getCheckoutAvailableDiscountList({ invoiceId: orderId.toString() }));
  },
);
