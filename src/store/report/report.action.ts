import { type FetchDashboardDataRequest } from '@moego/api-web/moego/api/reporting/v2/dashboard_api';
import {
  type ExportReportDataRequest,
  type FetchReportDataRequest,
  type MarkReportFavoriteRequest,
  type QueryReportMetasRequest,
  type QueryReportPagesRequest,
} from '@moego/api-web/moego/api/reporting/v2/reports_api';
import { type QueryDashboardPagesRequest } from '@moego/api-web/moego/enterprise/report/v1/dashboard_api';
import { type Field } from '@moego/api-web/moego/models/reporting/v2/field_model';
import { type RangeValue } from '@moego/ui';
import { action } from 'amos';
import { type Dayjs } from 'dayjs';
import { Map } from 'immutable';
import { type FilterObj } from '../../container/ReportV2/components/ReportTable/ReportTableContext';
import { DashboardClient, ReportClient } from '../../middleware/clients';
import { createReportObservableAction } from '../observableServices/observableServices';
import { tableFilterBox } from './report.boxes';

export const updateFilter = action(
  async (
    dispatch,
    select,
    input: {
      diagramId: string;
      fieldKey: string;
      filterObj?: Partial<FilterObj>;
    },
  ) => {
    const { diagramId, fieldKey, filterObj } = input;
    const filterMap = select(tableFilterBox).getItem(diagramId)?.filterList || Map();
    const newMap = filterMap.set(fieldKey, filterObj as FilterObj);
    await dispatch(tableFilterBox.mergeItem(diagramId, { filterList: newMap }));
  },
);

export const updateFilters = action(
  async (
    dispatch,
    select,
    input: {
      diagramId: string;
      filters: {
        fieldKey: string;
        filterObj?: Partial<FilterObj>;
      }[];
    },
  ) => {
    const { diagramId, filters } = input;
    const filterList = select(tableFilterBox).getItem(diagramId)?.filterList || Map();
    const newFiltersMap = Map(filters.map(({ fieldKey, filterObj }) => [fieldKey, filterObj as FilterObj]));
    await dispatch(tableFilterBox.mergeItem(diagramId, { filterList: filterList.merge(newFiltersMap) }));
  },
);

export const updateGroupByList = action(
  async (
    dispatch,
    select,
    input: {
      diagramId: string;
      groupByList: Field[];
    },
  ) => {
    dispatch(tableFilterBox.mergeItem(input.diagramId, { groupByList: input.groupByList }));
  },
);

export const clearFilter = action(async (dispatch, select, diagramId: string) => {
  const filterMap = select(tableFilterBox).getItem(diagramId)?.filterList || Map();
  const newMap = filterMap.clear();
  await dispatch(tableFilterBox.mergeItem(diagramId, { filterList: newMap }));
});

export const deleteFilter = action(async (dispatch, select, diagramId: string, filedKey: string) => {
  const filterMap = select(tableFilterBox).getItem(diagramId)?.filterList || Map();
  const newMap = filterMap.delete(filedKey);
  await dispatch(tableFilterBox.mergeItem(diagramId, { filterList: newMap }));
});

export const updatePeriodTime = action(
  async (
    dispatch,
    select,
    input: {
      diagramId: string;
      time: RangeValue<Dayjs>;
    },
  ) => {
    await dispatch(
      tableFilterBox.mergeItem(input.diagramId, {
        currentPeriod: {
          startTime: input.time?.[0] || void 0,
          endTime: input.time?.[1] || void 0,
        },
      }),
    );
  },
);

export const queryDashboardPages = action(async (_dispatch, _select, input: QueryDashboardPagesRequest) => {
  return DashboardClient.queryDashboardPages(input);
});

export const fetchDashboardData = action(
  async (_dispatch, _select, params: FetchDashboardDataRequest, signal?: AbortSignal) => {
    return DashboardClient.fetchDashboardData(params, { signal });
  },
);

export const fetchReportData = action(
  async (_dispatch, _select, params: FetchReportDataRequest, signal?: AbortSignal) => {
    return ReportClient.fetchReportData(params, { signal });
  },
);

export const queryReportPages = action(
  async (_dispatch, _select, params: QueryReportPagesRequest, signal?: AbortSignal) => {
    return ReportClient.queryReportPages(params, { signal });
  },
);

export const markReportFavorite = createReportObservableAction(
  'markReportFavorite',
  async (_dispatch, _select, params: MarkReportFavoriteRequest, signal?: AbortSignal) => {
    return ReportClient.markReportFavorite(params, { signal });
  },
);

export const queryReportMetas = action(
  async (_dispatch, _select, params: QueryReportMetasRequest, signal?: AbortSignal) => {
    return ReportClient.queryReportMetas(params, { signal });
  },
);

export const exportReportData = createReportObservableAction(
  'exportReportData',
  async (_dispatch, _select, params: ExportReportDataRequest, signal?: AbortSignal) => {
    return ReportClient.exportReportData(params, { signal });
  },
);
