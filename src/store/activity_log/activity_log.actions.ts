/*
 * @since 2023-03-13 22:16:51
 * <AUTHOR> <<EMAIL>>
 */

import { type OptionalRepeated } from '@moego/api-web/createHttpClient';
import {
  type SearchActionPageRequest,
  type SearchActivityLogPageRequest,
  type SearchOperatorPageRequest,
  type SearchOwnerPageRequest,
  type SearchResourceTypePageRequest,
} from '@moego/api-web/moego/api/activity_log/v1/activity_log_api';
import { action } from 'amos';
import { ANY } from 'monofile-utilities/lib/consts';
import { ActivityLogClient } from '../../middleware/clients';
import { currentBusinessIdBox } from '../business/business.boxes';
import { type PagedInput } from '../utils/PagedList';
import { get } from '../utils/utils';
import { activityLogMapBox, businessActivityLogListBox } from './activity_log.boxes';

export const getActivityLogList = action(
  async (
    dispatch,
    select,
    input: PagedInput<OptionalRepeated<Omit<SearchActivityLogPageRequest, 'page' | 'pagination'>>>,
    businessId: number = select(currentBusinessIdBox),
  ) => {
    const state = select(businessActivityLogListBox.mustGetItem(businessId));
    const filter = state.getFilter(input);
    dispatch(businessActivityLogListBox.updateItem(businessId, (v) => v.applyStart(input)));
    try {
      const params: SearchActivityLogPageRequest = {
        ...filter,
        page: ANY,
        pagination: {
          pageNum: filter.pageNum,
          pageSize: filter.pageSize,
        },
      };
      const r = await ActivityLogClient.searchActivityLogPage(params);
      dispatch([
        businessActivityLogListBox.updateItem(businessId, (v) => {
          return v.applySuccess(
            r.activityLogs.map(get('id')),
            r.pagination!.total,
            r.pagination!.pageNum,
            filter.clear,
          );
        }),
        activityLogMapBox.mergeItems(r.activityLogs),
      ]);
    } catch {
      dispatch(businessActivityLogListBox.updateItem(businessId, (v) => v.applyFail(filter.pageNum)));
    }
  },
);

export const getActivityLogDetail = action(async (dispatch, select, id: string) => {
  const r = await ActivityLogClient.getActivityLogDetails({ id });
  dispatch(
    activityLogMapBox.mergeItems([...r.effects, { ...r.activityLog!, affectedResourceList: r.effects.map(get('id')) }]),
  );
});

export const searchActivityLogAction = action(
  async (_dispatch, select, input: Omit<SearchActionPageRequest, 'page'>) => {
    return ActivityLogClient.searchActionPage(input as SearchActionPageRequest);
  },
);

export const searchActivityLogOperator = action(
  async (_dispatch, select, input: Omit<SearchOperatorPageRequest, 'page'>) => {
    return ActivityLogClient.searchOperatorPage(input as SearchOperatorPageRequest);
  },
);

export const searchActivityLogOwner = action(async (_dispatch, select, input: Omit<SearchOwnerPageRequest, 'page'>) => {
  return ActivityLogClient.searchOwnerPage(input as SearchOwnerPageRequest);
});

export const searchActivityLogResourceType = action(
  async (_dispatch, select, input: Omit<SearchResourceTypePageRequest, 'page'>) => {
    return ActivityLogClient.searchResourceTypePage(input as SearchResourceTypePageRequest);
  },
);

export const clearActivityLogFilter = action(
  async (dispatch, select, businessId: number = select(currentBusinessIdBox)) => {
    dispatch(businessActivityLogListBox.updateItem(businessId, (v) => v.clearFilter()));
  },
);
