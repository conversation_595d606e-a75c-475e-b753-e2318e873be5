/*
 * @since 2020-09-07 10:18:53
 * <AUTHOR> <<EMAIL>>
 */

import { type SetTipsResponse } from '@moego/api-web/moego/api/order/v1/order_api';
import { type SaveSplitTipsRequest } from '@moego/api-web/moego/api/order/v1/split_tips_api';
import { MoeDecimal } from '@moego/finance-utils';
import { action } from 'amos';
import { ANY } from 'monofile-utilities/lib/consts';
import { type Overwrite } from 'utility-types';
import { AppointmentStatus } from '../../container/TicketDetail/AppointmentStatus';
import { http } from '../../middleware/api';
import { OrderClient, SplitTipsClient } from '../../middleware/clients';
import { type OpenApiModels } from '../../openApi/schema';
import { DATE_FORMAT_EXCHANGE } from '../../utils/DateTimeUtil';
import { overwriteParamsType } from '../../utils/api/api';
import { businessMapBox, currentBusinessIdBox } from '../business/business.boxes';
import { type MessageDetailMethod } from '../message/message.boxes';
import { invoiceRefundMapBox } from '../payment/payment.boxes';
import { petMapBox } from '../pet/pet.boxes';
import { serviceMapBox } from '../service/service.boxes';
import { staffMapBox } from '../staff/staff.boxes';
import { type PagedInput } from '../utils/PagedList';
import { type PartialRequiredProps } from '../utils/RecordMap';
import { ID_ANONYMOUS } from '../utils/identifier';
import { get } from '../utils/utils';
import {
  type AmountChangeType,
  GroomingCustomerTicketListFilterRecord,
  GroomingTicketNoShowStatus,
  type PublishableId,
  groomingCustomerTicketListBox,
  groomingDepositMapBox,
  groomingSplitTipsMapBox,
  groomingTicketInvoiceGuidMapBox,
  groomingTicketInvoiceMapBox,
  groomingTicketMapBox,
  upcomingAppointmentLinkMapBox,
} from './grooming.boxes';
import { selectGroomingCustomerTickets, selectGroomingTicketInvoice } from './grooming.selectors';

export type TicketInfo = Partial<OpenApiModels['GET/grooming/appointment/detail/pup']['Res']['data']>;

export type TicketChangeStatusParams = Partial<OpenApiModels['PUT/grooming/appointment/status']['Req']>;

export type TicketCancelParamsOrigin = Partial<OpenApiModels['PUT/grooming/appointment/cancel']['Req']>;

export type TicketCancelParams = Omit<TicketCancelParamsOrigin, 'noShow'> & {
  noShow?: boolean;
};

export const groomingGetCustomerTickets = action(
  async (
    dispatch,
    select,
    input: PagedInput & PartialRequiredProps<GroomingCustomerTicketListFilterRecord, 'customerId' | 'type'>,
    businessId: number = select(currentBusinessIdBox),
    signal?: AbortSignal,
  ) => {
    const state = select(selectGroomingCustomerTickets(input.customerId, input.type));
    const business = select(businessMapBox.mustGetItem(businessId));
    const ownKey = GroomingCustomerTicketListFilterRecord.ownKey(input.customerId, input.type);
    const now = business.localDate(Date.now());
    const filter = state.getFilter(
      Object.assign(input, {
        appointmentTime: now.format(DATE_FORMAT_EXCHANGE),
        time: now.hour() * 60 + now.minute(),
      }),
    );
    dispatch(groomingCustomerTicketListBox.updateItem(ownKey, (v) => v.applyStart(input)));
    try {
      const r = await http.open('POST/grooming/customer/appointment/list/v2', filter, { signal });
      r.data.list ||= [];
      const evaluationServiceList =
        r.data?.evaluationServiceList?.map((item) => ({ ...item, serviceId: Number(item.id) })) || [];
      dispatch([
        groomingCustomerTicketListBox.updateItem(ownKey, (v) =>
          v.applySuccess(r.data.list.map(get('id')), Number(r.data.total), filter.pageNum, filter.clear),
        ),
        groomingTicketMapBox.mergeItems(r.data.list),
        serviceMapBox.mergeItems(
          // 这里返回的是 company level 的 service 信息，所以不能什么都写入。price 如果有 biz override 的话，会被 company level 的覆盖
          // 所以这里只写入 serviceId 和 companyId
          (r.data.serviceList || []).map((v) => ({
            serviceId: v.id,
            companyId: String(v.companyId),
            name: v.name,
            description: v.description,
          })),
        ),
        serviceMapBox.mergeItems(evaluationServiceList),
        staffMapBox.mergeItems(r.data.staffList || []),
        petMapBox.mergeItems(r.data.petList || []),
      ]);
    } catch (e) {
      dispatch(groomingCustomerTicketListBox.updateItem(ownKey, (v) => v.applyFail(filter.pageNum)));
      throw e;
    }
  },
);

export const groomingRemoveTicket = action(async (dispatch, select, id: number) => {
  await http.open('DELETE/grooming/appointment', { ids: [id] });
});

export const groomingCancelTicket = action(async (dispatch, select, id: number, noShow: number) => {
  await http.open('PUT/grooming/appointment/cancel', { id, noShow });
  dispatch(groomingTicketMapBox.mergeItem(id, { noShow, noShowStatus: GroomingTicketNoShowStatus.Initial }));
});

export const groomingCreateNoShowInvoice = action(async (dispatch, select, id: number, amount: number) => {
  const r = await http.open('POST/grooming/invoice/noshow', { amount, groomingId: id });
  dispatch(
    groomingTicketMapBox.mergeItem(id, {
      noShowFee: amount,
      invoiceId: r.data.id,
      noShowStatus: GroomingTicketNoShowStatus.Created,
    }),
  );
  return r.data;
});

export const groomingCreatePreAuthNoShowInvoice = action(async (dispatch, _select, id: number, amount: number) => {
  const r = await http.open('POST/grooming/invoice/preauth/noshow', { amount, groomingId: id });
  dispatch(
    groomingTicketMapBox.mergeItem(id, {
      noShow: amount,
      invoiceId: r.data.id,
      noShowStatus: GroomingTicketNoShowStatus.Created,
    }),
  );
  return r.data;
});

export const getGroomingTicketInvoice = action(async (dispatch, select, orderId: PublishableId) => {
  const r = await http.open('GET/grooming/invoice/v2/order/detail', { orderId });
  dispatch(groomingTicketInvoiceMapBox.setItem(orderId, r));
  return r;
});

export const getGroomingTicketInvoiceQuickbookSyncStatus = action(async (dispatch, select, invoiceId: number) => {
  const r = await http.open('GET/grooming/qb/invoice/status', { invoiceId });
  dispatch(groomingTicketInvoiceMapBox.mergeItem(invoiceId, r));
});

export type SetTipsRes = SetTipsResponse;
export const setGroomingTicketTips = action(
  async (dispatch, select, invoiceId: PublishableId, value: number, lastModifiedTime: number | string | null) => {
    const invoice = select(selectGroomingTicketInvoice(invoiceId));

    const res = await OrderClient.setTips({
      value,
      // 可能情况：'amount' | 'percentage'，传空字符串或者不传，后台会默认用 'amount'，所以显式统一用 'amount'
      valueType: 'amount',
      invoiceId: invoice.id as unknown as string,
      lastModifiedTime: lastModifiedTime as unknown as string,
      checkRefund: true,
      omitResult: false,
    });

    await dispatch(getGroomingTicketInvoice(invoiceId));

    if (res?.refundChannel?.channelList?.length) {
      dispatch(
        invoiceRefundMapBox.mergeItem(invoiceId, {
          refundDetails: res?.refundChannel,
          callback: async () => await dispatch(setGroomingTicketTips(invoiceId, value, null)),
        }),
      );
    }
  },
);

export const setGroomingTicketDiscount = action(
  async (dispatch, select, invoiceId: number, value: number, valueType: AmountChangeType) => {
    await http.open('POST/grooming/invoice/set-discount', { omitResult: ANY, invoiceId, value, valueType });
    await dispatch(getGroomingTicketInvoice(invoiceId));
  },
);

// TODO(junbao): invoke with guid
export const placeGroomingTicketOrder = action(async (dispatch, select, invoiceId: PublishableId) => {
  if (typeof invoiceId === 'string') {
    invoiceId = select(groomingTicketInvoiceGuidMapBox.mustGet(invoiceId, ID_ANONYMOUS));
  }
  return await http.open('POST/grooming/invoice/place-order', { invoiceId });
});

export const getGroomingTicketGuid = action(
  async (dispatch, select, invoiceId: number, requiredProcessingFee?: boolean) => {
    const r = await http.open('GET/grooming/invoice/client/url', { invoiceId, requiredProcessingFee });
    dispatch(groomingTicketInvoiceMapBox.mergeItem(invoiceId, { guid: r.guid }));
    return r;
  },
);

export const getGroomingDepositGuid = action(
  async (dispatch, select, invoiceId: number, amount?: number, requiredProcessingFee?: boolean) => {
    const r = await http.open('POST/grooming/deposit/create', { invoiceId, amount, requiredProcessingFee });
    dispatch(groomingDepositMapBox.mergeItem(r.guid, { deGuid: r.guid, amount, status: 1 }));
    return r;
  },
);

export const revertGroomingInvoice = action(async (dispatch, select, invoiceId: number, groomingId: number) => {
  await http.open('PUT/grooming/invoice/revert', { groomingId });
  await dispatch(getGroomingTicketInvoice(invoiceId));
});

export const markGroomingTicketInvoiceFinished = action(async (dispatch, select, invoiceId: number) => {
  await http.open('POST/grooming/invoice/complete', { invoiceId });
  await dispatch(getGroomingTicketInvoice(invoiceId));
});

export const applyGroomingPackage = action(async (dispatch, select, invoiceId: number) => {
  const res = await http.open('POST/grooming/invoice/apply-package', { invoiceId, checkRefund: true });
  await dispatch(getGroomingTicketInvoice(invoiceId));

  if (res?.data?.refundChannel?.channelList?.length && invoiceId) {
    dispatch(
      invoiceRefundMapBox.mergeItem(invoiceId, {
        refundDetails: res?.data?.refundChannel,
        callback: async () => await dispatch(applyGroomingPackage(invoiceId)),
      }),
    );
  }

  return res;
});

export const removeGroomingPackage = action(
  async (dispatch, select, invoiceId: number, applyId: number, serviceId: number) => {
    const res = await http.open('DELETE/grooming/invoice/v2/apply-package', {
      invoiceId,
      applyPackageId: applyId,
      checkRefund: true,
    });
    await dispatch(getGroomingTicketInvoice(invoiceId));

    if (res?.data?.refundChannel?.channelList?.length && invoiceId) {
      dispatch(
        invoiceRefundMapBox.mergeItem(invoiceId, {
          refundDetails: res?.data?.refundChannel,
          callback: async () => await dispatch(removeGroomingPackage(invoiceId, applyId, serviceId)),
        }),
      );
    }

    return res;
  },
);

export const addGroomingServiceCharges = action(async (dispatch, _select, invoiceId: number, idList: string[]) => {
  await OrderClient.addServiceChargeToOrder({
    orderId: invoiceId.toString(),
    serviceChargeId: idList,
  });
  await dispatch(getGroomingTicketInvoice(invoiceId));
});

export const removeGroomingServiceCharges = action(async (dispatch, _select, invoiceId: number, idList: string[]) => {
  const res = await OrderClient.removeServiceChargeFromOrder({
    orderId: invoiceId.toString(),
    serviceChargeId: idList,
    checkRefund: true,
  });

  await dispatch(getGroomingTicketInvoice(invoiceId));

  if (res?.refundChannel?.channelList?.length) {
    dispatch(
      invoiceRefundMapBox.mergeItem(invoiceId, {
        refundDetails: res?.refundChannel,
        callback: async () => await dispatch(removeGroomingServiceCharges(invoiceId, idList)),
      }),
    );
  }

  return res;
});

export const sendUpcomingPreviewEmail = action(async (dispatch, select, customerId: number, email: string) => {
  await http.open('POST/message/send/grooming/upcoming', {
    customerId,
    email,
  });
});

export const getCustomerAppointmentCount = action(async (dispatch, select, customerId: number) => {
  return await http.open('GET/grooming/customer/appointment/count', { customerId });
});

// 这个不会存到 box
export const getCustomerUpcomingUrl = action(async (dispatch, select, customerId: number) => {
  // 接口 param 中的 appointmentDate & minutes 都被后端废弃了，所以这里只传 customerId
  const r = await http.open('GET/grooming/appointment/customer/upcoming/url', {
    customerId,
  });
  return r.data;
});

// 这个会存到 box
export const getUpcomingAppointmentLink = action(async (dispatch, select, customerId: number) => {
  const appointmentCount = await dispatch(getCustomerAppointmentCount(customerId));
  const upcomingAppointmentCount = appointmentCount?.upcoming ?? 0;

  /**
   * 判断是否有 upcoming appointment 需要看 upcomingAppointmentCount；
   * 而不管有没有 upcoming appointment，upcomingAppointmentLink 都是有值的。
   *
   * 因此这里先拉第一个接口，再条件拉取第二个接口
   * 1. 有则进一步调用接口拿 url
   * 2. 没有则直接返回空字符串
   */
  const upcomingAppointmentLink =
    upcomingAppointmentCount > 0 ? await dispatch(getCustomerUpcomingUrl(customerId)) : '';

  dispatch(
    upcomingAppointmentLinkMapBox.mergeItem(customerId, {
      customerId,
      upcomingAppointmentLink,
      upcomingAppointmentCount,
    }),
  );
});

export const getIsAvailablePackage = action(async (dispatch, select, invoiceId: number) => {
  return await http.open('GET/grooming/invoice/packageAvailable', { invoiceId });
});

export const getSplitTips = action(async (dispatch, select, orderId: number) => {
  const res = await http.open('GET/grooming/order/tipSplit/detail', { orderId });
  dispatch(groomingSplitTipsMapBox.mergeItem(orderId, res));
  return res;
});

export const setSplitTips = action(
  async (
    dispatch,
    select,
    input: Omit<SaveSplitTipsRequest, 'businessTipAmount'> & {
      businessTipAmount: number;
    },
  ) => {
    await SplitTipsClient.saveTipSplit({
      ...input,
      businessTipAmount: MoeDecimal.fromNumber(input.businessTipAmount),
    });
    dispatch(getSplitTips(+input.orderId!));
  },
);

export const previewSplitTips = action(
  async (
    _dispatch,
    _select,
    input: OpenApiModels['POST/grooming/order/tipSplit/preview']['Req'],
    abortSignal?: AbortSignal,
  ) => {
    const res = await http.open('POST/grooming/order/tipSplit/preview', input, {
      signal: abortSignal,
    });

    // 转为跟 /detail 接口一样的类型
    return res;
  },
);

type CalendarConflictCheckInput = OpenApiModels['PUT/grooming/appointment/conflict/check']['Req'];

export const checkCalendarConflict = action(async (dispatch, select, input: CalendarConflictCheckInput) => {
  const r = await http.open('PUT/grooming/appointment/conflict/calendar', input);
  return r;
});

export const sendReadyForPickupMessage = action(async (dispatch, select, groomingId: number) => {
  const r = await http.open('POST/message/send/services/message', {
    targetType: 20,
    extParams: {
      groomingId,
    },
  });
  return r;
});

export const changeGroomingTicketStatus = action(
  async (
    dispatch,
    select,
    params: Overwrite<
      TicketChangeStatusParams,
      {
        status: AppointmentStatus;
        messageMethodForPickupNotification?: MessageDetailMethod;
      }
    >,
  ) => {
    const r = await http.open(
      'PUT/grooming/appointment/status',
      overwriteParamsType<TicketChangeStatusParams, typeof params>(params),
    );
    return r;
  },
);

export const cancelGroomingTicket = action(async (dispatch, select, params: TicketCancelParamsOrigin) => {
  const r = await http.open('PUT/grooming/appointment/cancel', params);
  return r;
});

export const undoTicketStatus = action(async (dispatch, select, groomingId: number) => {
  const r = await http.open('PUT/grooming/appointment/status/revert', {
    groomingId,
  });
  return r;
});

export const putTicketToWaitingList = action(async (dispatch, select, ticketId: number) => {
  const r = await http.open('PUT/grooming/appointment/waiting', {
    id: ticketId,
  });
  return r;
});

export const sendReviewBooster = action(async (dispatch, select, groomingId: number, customerId: number) => {
  const res = await http.open('POST/message/send/services/message', {
    targetType: 40,
    method: 1,
    customer: {
      customerId,
    },
    extParams: { groomingId },
  });
  return res;
});

export const editCheckInTime = action(async (dispatch, select, appointmentId: number, checkInTime: number) => {
  const res = await http.open('PUT/grooming/v2/appointment/action-time', {
    appointmentId,
    actionTime: checkInTime,
    statusForTimeToChange: AppointmentStatus.CHECKED_IN,
  });

  return res;
});

export const editCheckOutTime = action(async (dispatch, select, appointmentId: number, checkOutTime: number) => {
  const res = await http.open('PUT/grooming/v2/appointment/action-time', {
    appointmentId,
    actionTime: checkOutTime,
    statusForTimeToChange: AppointmentStatus.FINISHED,
  });

  return res;
});
