import { type MassEmailSendRequest } from '@moego/api-web/moego/api/message/v1/marketing_email_service';
import { action } from 'amos';
import { http } from '../../middleware/api';
import { MarketingEmailClient } from '../../middleware/clients';
import { type OpenApiDefinitions } from '../../openApi/schema';
import { currentAccountIdBox } from '../account/account.boxes';
import { selectCurrentAccount } from '../account/account.selectors';
import { currentBusinessIdBox } from '../business/business.boxes';
import { currentCompanyIdBox } from '../company/company.boxes';
import {
  type SalesAgreementModel,
  type SalesAgreementSignModel,
  type SalesSetupDetailModel,
  salesAgreementMapBox,
  salesAgreementSignRecordMapBox,
  salesSetupDetailMapBox,
} from './sales.boxes';

export const getSalesSetupDetail = action(async (dispatch, _select, code: string) => {
  const data = (await http.open('GET/payment/platform-sales/record/code', { code })).data as SalesSetupDetailModel;
  dispatch(salesSetupDetailMapBox.mergeItem(code, data));
  return data;
});

export const getSalesAgreementDetail = action(async (dispatch, select, salesCode: string) => {
  const r = (
    await http.open('GET/payment/platform-sales/record/agreement', {
      code: salesCode,
    })
  ).data as SalesAgreementModel;
  dispatch(salesAgreementMapBox.mergeItem(r.id, r));
  return r;
});

export const getSalesAgreementSignRecord = action(async (dispatch, select, code: string) => {
  const { data } = await http.open('GET/payment/platform-sales/get/sign-record/code', { code });
  dispatch(
    salesAgreementSignRecordMapBox.mergeItem(data.companyId as number, data as Partial<SalesAgreementSignModel>),
  );
  return data;
});

export const signSalesAgreement = action(
  async (
    _dispatch,
    select,
    agreementId: number,
    // signature URL
    signature: string,
    accountId: number = select(currentAccountIdBox),
    companyId: number = select(currentCompanyIdBox),
    businessId: number = select(currentBusinessIdBox),
  ) => {
    const { email } = select(selectCurrentAccount());
    await http.open('PUT/payment/platform-sales/record/update', {
      accountId,
      companyId,
      businessId,
      agreementId,
      signature,
      email,
    });
  },
);

export const updateSalesTermStatus = action(
  async (
    _dispatch,
    select,
    input: OpenApiDefinitions['payment']['com.moego.server.payment.web.dto.UpdatePlatformSalesStatusDTO'],
  ) => {
    return await http.open('PUT/payment/platform-sales/record/status/update', input);
  },
);

export const sendMemberShipSellLinkByEmail = action(async (dispatch, select, input: MassEmailSendRequest) => {
  const { email, recipientFilter } = input;

  return await MarketingEmailClient.massEmailSend({
    email,
    recipientFilter,
  });
});
