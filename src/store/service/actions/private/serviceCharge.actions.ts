import {
  type AddCompanyServiceChargeParams,
  type ListSurchargeAssociatedFoodSourceParams,
  type UpdateCompanyServiceChargeParams,
} from '@moego/api-web/moego/api/order/v1/service_charge_company_api';
import { type SurchargeType } from '@moego/api-web/moego/models/order/v1/service_charge_enums';
import { action } from 'amos';
import { isBoolean } from 'lodash';
import { ServiceChargeClient, ServiceChargeCompanyClient } from '../../../../middleware/clients';
import { currentAccountIdBox } from '../../../account/account.boxes';
import { currentBusinessIdBox } from '../../../business/business.boxes';
import { type PartialRequired } from '../../../utils/RecordMap';
import {
  type ServiceChargeRecord,
  serviceChargeInactiveListBox,
  serviceChargeListBox,
  serviceChargeMapBox,
} from '../../service.boxes';

export const removeServiceCharge = action(
  async (
    dispatch,
    select,
    id: string,
    businessOrAccountId: number = select(currentBusinessIdBox),
    applyUpcomingAppt?: boolean,
  ) => {
    const serviceCharge = select(serviceChargeMapBox.getItem(id));
    if (!serviceCharge) {
      return;
    }
    await ServiceChargeClient.deleteServiceCharge({ id, applyUpcomingAppt });
    dispatch(serviceChargeMapBox.deleteItem(id));
    dispatch(serviceChargeListBox.deleteItem(businessOrAccountId, id));
    dispatch(serviceChargeInactiveListBox.deleteItem(businessOrAccountId, id));
  },
);

export const sortServiceChargeList = action(async (dispatch, select, idList: string[], bizOrAccountId?: number) => {
  await ServiceChargeClient.sortServiceCharge({ sortedId: idList });
  const businessId = select(currentBusinessIdBox);
  dispatch(serviceChargeListBox.setList(bizOrAccountId ?? businessId, idList));
  return idList;
});

/**
 * services settings 里，新的 AS 里，获取整个 company 下的 service charge
 */
export const getCompanyServiceChargeList = action(
  async (
    dispatch,
    select,
    input: { isActive?: boolean; businessIds: string[]; surchargeType: SurchargeType },
    _signal?: AbortSignal,
  ) => {
    const accountId = select(currentAccountIdBox);
    const { serviceCharge } = await ServiceChargeCompanyClient.getCompanyServiceChargeList({
      ...input,
    });
    dispatch(serviceChargeMapBox.mergeItems(serviceCharge));
    if (isBoolean(input.isActive)) {
      dispatch(
        (input.isActive ? serviceChargeListBox : serviceChargeInactiveListBox).setList(
          accountId,
          serviceCharge.map((i) => i.id),
        ),
      );
    }
    return serviceCharge;
  },
);

export const addCompanyServiceCharge = action(
  async (dispatch, _select, input: ServiceChargeRecord, isActive: boolean) => {
    const { id } = await ServiceChargeCompanyClient.addCompanyServiceCharge(input as AddCompanyServiceChargeParams);
    const newService = { ...input, id };

    // 直接调用 getCompanyServiceChargeList，因为 service charge list 不多
    await dispatch(getCompanyServiceChargeList({ isActive, businessIds: [], surchargeType: input.surchargeType }));

    return newService;
  },
);

export const updateCompanyServiceCharge = action(
  async (_dispatch, _select, input: PartialRequired<ServiceChargeRecord, 'id'>, _isActive: boolean) => {
    const res = await ServiceChargeCompanyClient.updateCompanyServiceCharge(input as UpdateCompanyServiceChargeParams);
    return res;
  },
);

export const getSurchargeAssociatedFoodSource = action(
  async (_dispatch, _select, input: ListSurchargeAssociatedFoodSourceParams) => {
    const result = await ServiceChargeCompanyClient.listSurchargeAssociatedFoodSource(input);
    return result;
  },
);
