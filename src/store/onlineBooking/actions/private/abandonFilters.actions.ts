import { action } from 'amos';
import { http } from '../../../../middleware/api';
import { type OpenApiModels } from '../../../../openApi/schema';
import { currentBusinessIdBox } from '../../../business/business.boxes';
import {
  AbandonFilterKeyForBE,
  type AbandonFilterProperty,
  abandonFiltersMapBox,
  abandonFiltersSummaryMapBox,
} from '../../abandonFilters.boxes';

export interface UpdateAbandonFilterParams {
  property: AbandonFilterProperty;
  values: string[];
}

export const updateAbandonFilter = action((dispatch, select, { property, values }: UpdateAbandonFilterParams) => {
  const businessId = select(currentBusinessIdBox);
  dispatch(abandonFiltersMapBox.mergeItem(businessId, { [property]: values }));
});

export type GetAbandonFiltersSummaryParams = OpenApiModels['GET/grooming/ob/v2/abandoned-client-filters']['Req'];

export const getAbandonFiltersSummary = action(
  async (
    dispatch,
    select,
    input: GetAbandonFiltersSummaryParams,
    businessId: number = select(currentBusinessIdBox),
  ) => {
    const resp = await http.open('GET/grooming/ob/v2/abandoned-client-filters', input);
    const lead_type = resp.find((item) => item?.filter === AbandonFilterKeyForBE.LeadType)?.options ?? [];
    const abandon_step = resp.find((item) => item?.filter === AbandonFilterKeyForBE.AbandonStep)?.options ?? [];
    const abandon_status = resp.find((item) => item?.filter === AbandonFilterKeyForBE.AbandonStatus)?.options ?? [];
    dispatch(
      abandonFiltersSummaryMapBox.mergeItem(businessId, {
        businessId,
        lead_type,
        abandon_step,
        abandon_status,
      }),
    );
    return resp;
  },
);
