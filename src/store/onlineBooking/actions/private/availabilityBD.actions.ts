import {
  type BatchCreateArrivalPickUpTimeOverrideParams,
  type BatchDeleteArrivalPickUpTimeOverrideParams,
  type BatchUpdateArrivalPickUpTimeOverrideParams,
  type CreateCapacityOverrideParams,
  type DeleteCapacityOverrideParams,
  type UpdateCapacityOverrideParams,
} from '@moego/api-web/moego/api/online_booking/v1/ob_availability_setting_api';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import {
  type BoardingServiceAvailabilityUpdateDef,
  type DateRangeDef,
  type DaycareServiceAvailabilityUpdateDef,
  type EvaluationServiceAvailabilityUpdateDef,
} from '@moego/api-web/moego/models/online_booking/v1/ob_availability_setting_defs';
import { DateLimitType } from '@moego/api-web/moego/models/online_booking/v1/ob_availability_setting_enums';
import { action } from 'amos';
import { isUndefined, pick } from 'lodash';
import { toastApi } from '../../../../components/Toast/Toast';
import { OBAvailabilitySettingClient } from '../../../../middleware/clients';
import { dateMessageToString, stringToDateMessage } from '../../../../utils/utils';
import { currentBusinessIdBox } from '../../../business/business.boxes';
import { createOnlineBookingObservableAction } from '../../../observableServices/observableServices';
import { type PartialRequired } from '../../../utils/RecordMap';
import { MostFarAvailableKind, SoonestAvailableKind } from '../../models/OnlineBookingPreference';
import { selectDraftAcceptClientType, selectDraftAcceptPetIds } from '../../settings/availability.selectors';
import {
  ArrivalPickupTimeRecord,
  type BDAvailabilitySettings,
  getBDAvailabilityBoxes,
} from '../../settings/availabilityBD.boxes';
import { selectBDDraftAvailability, selectBDRawAvailability } from '../../settings/availabilityBD.selectors';
import { setOBSharedClientAcceptId, setOBSharedPetAcceptIds } from './availability.actions';

export const getOnlineBookingBDAvailabilitySettings = createOnlineBookingObservableAction(
  'getOnlineBookingBDAvailabilitySettings',
  async (dispatch, select, serviceItemType: ServiceItemType, businessId: number = select(currentBusinessIdBox)) => {
    const { arrivalTimeOverrides, pickUpTimeOverrides } =
      await OBAvailabilitySettingClient.listArrivalPickUpTimeOverrides({
        businessId: `${businessId}`,
        serviceItemTypes: [serviceItemType],
      });

    if (serviceItemType === ServiceItemType.BOARDING) {
      const res = await OBAvailabilitySettingClient.getBoardingServiceAvailability({
        businessId: `${businessId}`,
      });
      const settingInfo: Partial<BDAvailabilitySettings> = {
        ...res.boardingServiceAvailability.bookingDateRange,
        specificEndDate: !isUndefined(res.boardingServiceAvailability.bookingDateRange.specificEndDate)
          ? dateMessageToString(res.boardingServiceAvailability.bookingDateRange.specificEndDate)
          : '',
        arrivalPickUpTimeRange: new ArrivalPickupTimeRecord(res.boardingServiceAvailability.arrivalPickUpTimeRange),
        acceptCustomerType: res.boardingServiceAvailability.acceptCustomerType,
        lodgingAvailability: res.boardingServiceAvailability.lodgingAvailability,
        arrivalTimeOverrides,
        pickUpTimeOverrides,
      };
      dispatch([
        setOBSharedPetAcceptIds(res.boardingServiceAvailability.acceptedPetTypes, serviceItemType),
        setOBSharedClientAcceptId(settingInfo.acceptCustomerType, serviceItemType),
        updateBDDraftAvailabilitySettings(serviceItemType, settingInfo),
        updateBDRawAvailabilitySettings(serviceItemType, settingInfo),
      ]);
    } else if (serviceItemType === ServiceItemType.DAYCARE) {
      const res = await OBAvailabilitySettingClient.getDaycareServiceAvailability({
        businessId: `${businessId}`,
      });
      const settingInfo: Partial<BDAvailabilitySettings> = {
        ...res.daycareServiceAvailability.bookingDateRange,
        specificEndDate: !isUndefined(res.daycareServiceAvailability.bookingDateRange.specificEndDate)
          ? dateMessageToString(res.daycareServiceAvailability.bookingDateRange.specificEndDate)
          : '',
        arrivalPickUpTimeRange: new ArrivalPickupTimeRecord(res.daycareServiceAvailability.arrivalPickUpTimeRange),
        acceptCustomerType: res.daycareServiceAvailability.acceptCustomerType,
        lodgingAvailability: res.daycareServiceAvailability.lodgingAvailability,
        capacityOverrides: res.daycareServiceAvailability.capacityOverrides,
        arrivalTimeOverrides,
        pickUpTimeOverrides,
      };
      dispatch([
        setOBSharedPetAcceptIds(res.daycareServiceAvailability.acceptedPetTypes, serviceItemType),
        setOBSharedClientAcceptId(settingInfo.acceptCustomerType, serviceItemType),
        updateBDDraftAvailabilitySettings(serviceItemType, settingInfo),
        updateBDRawAvailabilitySettings(serviceItemType, settingInfo),
      ]);
    } else if (serviceItemType === ServiceItemType.EVALUATION) {
      const res = await OBAvailabilitySettingClient.getEvaluationServiceAvailability({
        businessId: `${businessId}`,
      });
      const settingInfo = {
        ...res.availability.bookingDateRange,
        specificEndDate: !isUndefined(res.availability.bookingDateRange.specificEndDate)
          ? dateMessageToString(res.availability.bookingDateRange.specificEndDate)
          : '',
        arrivalPickUpTimeRange: new ArrivalPickupTimeRecord(res.availability.arrivalPickUpTimeRange),
        arrivalTimeOverrides,
        pickUpTimeOverrides,
      };
      // Evaluation 没有 pet type, customer type 限制
      dispatch([
        updateBDDraftAvailabilitySettings(serviceItemType, settingInfo),
        updateBDRawAvailabilitySettings(serviceItemType, settingInfo),
      ]);
    }
  },
);

export const updateBDDraftAvailabilitySettings = action(
  async (
    dispatch,
    select,
    serviceItemType: ServiceItemType,
    input: Partial<BDAvailabilitySettings>,
    businessId: number = select(currentBusinessIdBox),
  ) => {
    const updateBox = getBDAvailabilityBoxes(serviceItemType).draft;
    dispatch(updateBox.mergeItem(businessId, input));
  },
);

export const updateBDRawAvailabilitySettings = action(
  async (
    dispatch,
    select,
    serviceItemType: ServiceItemType,
    input: Partial<BDAvailabilitySettings>,
    businessId: number = select(currentBusinessIdBox),
  ) => {
    const updateBox = getBDAvailabilityBoxes(serviceItemType).raw;
    dispatch(updateBox.mergeItem(businessId, input));
  },
);

export const updateBDOnlineAvailabilitySettings = createOnlineBookingObservableAction(
  'updateBDOnlineAvailabilitySettings',
  async (dispatch, select, serviceItemType: ServiceItemType, businessId: number = select(currentBusinessIdBox)) => {
    const acceptPetTypes = select(selectDraftAcceptPetIds(serviceItemType));
    const acceptCustomerType = select(selectDraftAcceptClientType(serviceItemType));

    if (acceptPetTypes.length === 0 && serviceItemType !== ServiceItemType.EVALUATION) {
      toastApi.neutral('Please select at least one pet type');
      return false;
    }

    /**
     * 将 draft 的状态提交到后端
     */
    const draftSettings = select(selectBDDraftAvailability(serviceItemType));
    // 注意这里后端是 one of 的逻辑，所以需要根据设置的 date limit type 传递不同的值
    const bookingDateRangeEndParams: PartialRequired<DateRangeDef, 'endDateType'> =
      draftSettings.endDateType === DateLimitType.DATE_TYPE_OFFSET
        ? {
            endDateType: DateLimitType.DATE_TYPE_OFFSET,
            maxEndDateOffset: draftSettings.maxEndDateOffset ?? MostFarAvailableKind.HalfYear,
          }
        : {
            endDateType: DateLimitType.DATE_TYPE_SPECIFIC,
            specificEndDate: stringToDateMessage(draftSettings.specificEndDate!),
          };

    // 构造相关参数
    if (serviceItemType === ServiceItemType.BOARDING) {
      const params: BoardingServiceAvailabilityUpdateDef = {
        acceptCustomerType,
        acceptedPetTypes: acceptPetTypes,
        availableDateRange: {
          startDateType: DateLimitType.DATE_TYPE_OFFSET,
          maxStartDateOffset: draftSettings.maxStartDateOffset ?? SoonestAvailableKind.NextDay,
          ...bookingDateRangeEndParams,
        },
        arrivalPickUpTimeRange: draftSettings.arrivalPickUpTimeRange,
        lodgingAvailability: draftSettings.lodgingAvailability,
      };
      await OBAvailabilitySettingClient.updateBoardingServiceAvailability({
        businessId: `${businessId}`,
        boardingServiceAvailability: params,
      });
    } else if (serviceItemType === ServiceItemType.DAYCARE) {
      const params: DaycareServiceAvailabilityUpdateDef = {
        acceptCustomerType,
        acceptedPetTypes: acceptPetTypes,
        availableDateRange: {
          startDateType: DateLimitType.DATE_TYPE_OFFSET,
          maxStartDateOffset: draftSettings.maxStartDateOffset ?? SoonestAvailableKind.NextDay,
          ...bookingDateRangeEndParams,
        },
        arrivalPickUpTimeRange: draftSettings.arrivalPickUpTimeRange,
        lodgingAvailability: draftSettings.lodgingAvailability,
      };
      await OBAvailabilitySettingClient.updateDaycareServiceAvailability({
        businessId: `${businessId}`,
        daycareServiceAvailability: params,
      });
    } else if (serviceItemType === ServiceItemType.EVALUATION) {
      // Evaluation 没有 client / pet type 限制
      const params: EvaluationServiceAvailabilityUpdateDef = {
        availableDateRange: {
          startDateType: DateLimitType.DATE_TYPE_OFFSET,
          maxStartDateOffset: draftSettings.maxStartDateOffset ?? SoonestAvailableKind.NextDay,
          ...bookingDateRangeEndParams,
        },
        arrivalPickUpTimeRange: draftSettings.arrivalPickUpTimeRange,
      };
      await OBAvailabilitySettingClient.updateEvaluationServiceAvailability({
        businessId: `${businessId}`,
        availability: params,
      });
    }

    await dispatch(getOnlineBookingBDAvailabilitySettings(serviceItemType, businessId));
    return true;
  },
);

export const listArrivalPickUpTimeOverrides = action(
  async (dispatch, select, serviceItemType: ServiceItemType, businessId: number = select(currentBusinessIdBox)) => {
    const res = await OBAvailabilitySettingClient.listArrivalPickUpTimeOverrides({
      businessId: `${businessId}`,
      serviceItemTypes: [serviceItemType],
    });
    return res;
  },
);

export const resetBDAvailabilityDraftSettingsProperty = action(
  async (dispatch, select, serviceItemType: ServiceItemType, ...input: (keyof BDAvailabilitySettings)[]) => {
    const businessId: number = select(currentBusinessIdBox);
    const rawSettings = select(selectBDRawAvailability(serviceItemType));
    const selectedRawSettings = pick(rawSettings, input);
    const updateBox = getBDAvailabilityBoxes(serviceItemType).draft;
    dispatch([updateBox.mergeItem(businessId, selectedRawSettings)]);
  },
);

export const batchCreateArrivalPickUpTimeOverride = action(
  async (dispatch, select, input: Omit<BatchCreateArrivalPickUpTimeOverrideParams, 'businessId'>) => {
    const businessId = select(currentBusinessIdBox).toString();
    await OBAvailabilitySettingClient.batchCreateArrivalPickUpTimeOverride({ ...input, businessId });
    const res = await dispatch(listArrivalPickUpTimeOverrides(input.serviceItemType, businessId as unknown as number));
    return res;
  },
);

export const batchDeleteArrivalPickUpTimeOverride = action(
  async (
    dispatch,
    select,
    input: Omit<BatchDeleteArrivalPickUpTimeOverrideParams, 'businessId'>,
    serviceItemType: ServiceItemType,
  ) => {
    const businessId = select(currentBusinessIdBox).toString();
    await OBAvailabilitySettingClient.batchDeleteArrivalPickUpTimeOverride({ ...input, businessId });
    const res = await dispatch(listArrivalPickUpTimeOverrides(serviceItemType, businessId as unknown as number));
    return res;
  },
);

export const batchUpdateArrivalPickUpTimeOverride = action(
  async (
    dispatch,
    select,
    input: Omit<BatchUpdateArrivalPickUpTimeOverrideParams, 'businessId'>,
    serviceItemType: ServiceItemType,
  ) => {
    const businessId = select(currentBusinessIdBox).toString();
    await OBAvailabilitySettingClient.batchUpdateArrivalPickUpTimeOverride({ ...input, businessId });
    const res = await dispatch(listArrivalPickUpTimeOverrides(serviceItemType, businessId as unknown as number));
    return res;
  },
);

export const createCapacityOverride = action(
  async (dispatch, select, input: Omit<CreateCapacityOverrideParams, 'businessId'>) => {
    const businessId = select(currentBusinessIdBox).toString();
    const res = await OBAvailabilitySettingClient.createCapacityOverride({ ...input, businessId });
    return res;
  },
);

export const deleteCapacityOverride = action(
  async (dispatch, select, input: Omit<DeleteCapacityOverrideParams, 'businessId'>) => {
    const businessId = select(currentBusinessIdBox).toString();
    const res = await OBAvailabilitySettingClient.deleteCapacityOverride({ ...input, businessId });
    return res;
  },
);

export const updateCapacityOverride = action(
  async (dispatch, select, input: Omit<UpdateCapacityOverrideParams, 'businessId'>) => {
    const businessId = select(currentBusinessIdBox).toString();
    const res = await OBAvailabilitySettingClient.updateCapacityOverride({ ...input, businessId });
    return res;
  },
);
