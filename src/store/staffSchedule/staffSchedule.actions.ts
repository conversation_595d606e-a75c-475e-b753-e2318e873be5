import { action } from 'amos';
import dayjs, { type Dayjs } from 'dayjs';
import { toastApi } from '../../components/Toast/Toast';
import { http } from '../../middleware/api';
import { BusinessClient, StaffClient } from '../../middleware/clients';
import { type OpenApiModels } from '../../openApi/schema';
import { DATE_FORMAT_EXCHANGE } from '../../utils/DateTimeUtil';
import { currentBusinessIdBox } from '../business/business.boxes';
import { selectCurrentBusiness } from '../business/business.selectors';
import { type PartialRequired } from '../utils/RecordMap';
import {
  CalendarViewDataRecord,
  type CalendarViewListType,
  CalendarViewSlotDataRecord,
  bizScheduleMapBox,
  calendarViewDataMapBox,
  calendarViewSlotDataMapBox,
  currentWorkingHourWeekBox,
  staffScheduleWorkingHourMapBox,
  editingWorkingHourStaffIdBox,
  staffScheduleAreaOverrideMapBox,
  staffScheduleCalendarViewList,
  staffScheduleDateOverrideMapBox,
  staffScheduleServiceAreaMapBox,
  staffScheduleSlotOverrideMapBox,
  staffScheduleWorkingSlotMapBox,
  staffScheduleSlotFreeServicesMapBox,
} from './staffSchedule.boxes';
import { selectCalendarViewType } from './staffSchedule.selectors';
import {
  AvailabilityType,
  type DateOverrideDrawerSlotValue,
  type DateOverrideDrawerValue,
  MaxStaffEndDate,
  type StaffScheduleDateOverride,
  type StaffScheduleDraftData,
  type StaffScheduleServiceArea,
  type StaffScheduleSlotOverride,
  StaffScheduleType,
  type StaffScheduleWorkingHour,
  type StaffScheduleWorkingSlot,
} from './staffSchedule.types';
import { type EnumValues } from '../utils/createEnum';
import { isNormal } from '../utils/identifier';
import { cloneDeep, isUndefined } from 'lodash';
import {
  type TimeAvailabilityDay,
  type SlotAvailabilityDay,
} from '@moego/api-web/moego/models/organization/v1/staff_availability_models';
import { type WorkingHourValue } from '../staff/staff.boxes';
import { type WeekTimeScheduleValue } from '../../components/WeekTimeScheduleShiftManagement/types';
import {
  transformWeekDayList2WorkingHour,
  transformWeekDayList2WorkingSlot,
  transformWorkingHourMap2DayList,
  transformWorkingSlotMap2DayList,
} from './staffSchedule.utils';
import { type UpdateSlotFreeServicesParams } from '@moego/api-web/moego/api/organization/v1/staff_api';

export type GetWorkingRangeListInput = Omit<
  PartialRequired<OpenApiModels['POST/business/workingDaily/queryRange']['Req'], 'startDate' | 'endDate'>,
  'staffIdList'
>;

export const setEditingWorkingHourStaffId = action((dispatch, _select, staffId: number) => {
  dispatch(editingWorkingHourStaffIdBox.setState(staffId));
});

export const setWorkingHourCurrentWeek = action((dispatch, _select, newWeek: Dayjs = dayjs().startOf('week')) => {
  dispatch(currentWorkingHourWeekBox.setState(newWeek));
});

/** --------------------------- Staff Schedule By Time ---------------------------------- */
/** 获取 working hour 数据，包括 mobile grooming working area 数据 */
export const getStaffWorkingHour = action(async (dispatch, select, staffIdList: number[] = []) => {
  const business = select(selectCurrentBusiness());

  const { staffAvailabilityList } = await StaffClient.getStaffAvailability({
    businessId: `${business.id}`,
    availabilityType: AvailabilityType.BY_TIME,
    staffIdList: staffIdList.map((id) => String(id)),
  });

  const list = staffAvailabilityList
    .map(({ timeAvailabilityDayList, staffId, timeStartSunday: startDate, timeScheduleType: scheduleType }) => {
      if (timeAvailabilityDayList.length !== 28) {
        console.error(
          '[StaffClient.getStaffAvailability error] timeAvailabilityDayList length is not 28. This may indicate an issue with the data source or API response.',
          'Expected length: 28. Actual length:',
          timeAvailabilityDayList.length,
          ' Staff ID:',
          staffId,
        );

        return null;
      }

      const weekData = transformWeekDayList2WorkingHour(timeAvailabilityDayList);

      return {
        ...weekData,
        scheduleType,
        staffId: +staffId,
        startDate: startDate ? dayjs(startDate) : dayjs().startOf('date'),
        endDate: MaxStaffEndDate,
        origin: {
          ...cloneDeep(weekData),
          scheduleType,
        },
      };
    })
    .filter(Boolean) as StaffScheduleWorkingHour[];

  // 如果 staffIdList 是空，area 是不支持获取所有的，保险起见要从 staffAvailability 返回的数据来获取 area
  const realStaffIdList = list.map((item) => item.staffId);
  const areaList = await dispatch(getStaffWorkingArea(realStaffIdList));

  dispatch(staffScheduleWorkingHourMapBox.mergeItems(list));

  return [list, areaList];
});

export const updateStaffScheduleWorkingHour = action(
  (dispatch, select, staffId: number, params: Partial<StaffScheduleDraftData<StaffScheduleWorkingHour>>) => {
    if (!isUndefined(params.scheduleType)) {
      const { scheduleType } = params;
      const { origin: originRawData } = select(staffScheduleWorkingHourMapBox.mustGetItem(staffId));
      // 如果 schedule type 变了，要更新展示的 startDate，注意这里要跟后端的计算逻辑保持一致
      if (scheduleType !== originRawData?.scheduleType) {
        params.startDate = dayjs().startOf('week');
      } else {
        params.startDate = originRawData?.startDate;
      }
    }

    dispatch(staffScheduleWorkingHourMapBox.mergeItem(staffId, params));
  },
);

export const putStaffWorkingHour = action(
  async (dispatch, select, staffIdList: number[] = [select(editingWorkingHourStaffIdBox)]) => {
    const business = select(selectCurrentBusiness());
    const staffAvailabilityList = staffIdList.map((staffId) => {
      const raw = select(staffScheduleWorkingHourMapBox.mustGetItem(staffId)).toObject();
      const timeAvailabilityDayList = transformWorkingHourMap2DayList(raw);

      return {
        staffId: String(staffId),
        availabilityType: AvailabilityType.BY_TIME,
        isAvailable: true,
        timeScheduleType: raw.scheduleType,
        timeAvailabilityDayList,
        slotAvailabilityDayList: [],
      };
    });

    await StaffClient.updateStaffAvailability({
      businessId: `${business.id}`,
      staffList: [],
      staffAvailabilityList,
    });

    dispatch(syncStaffSchedule(staffIdList, AvailabilityType.BY_TIME));
  },
);

export const modifyStaffSchedule = action(
  (
    dispatch,
    select,
    params: Partial<StaffScheduleWorkingHour>,
    staffId: number = select(editingWorkingHourStaffIdBox),
  ) => {
    const { firstWeek, secondWeek, thirdWeek, forthWeek, ...rest } = params;
    dispatch(staffScheduleWorkingHourMapBox.mergeItem(staffId, params));
    dispatch(staffScheduleServiceAreaMapBox.mergeItem(staffId, rest));
  },
);

// area - just for mobile grooming
export const getStaffWorkingArea = action(async (dispatch, select, staffIdList: number[] = []) => {
  const business = select(selectCurrentBusiness());
  const isMobileGrooming = business.isMobileGrooming();

  if (!isMobileGrooming) return;

  const areaList = await Promise.all(
    staffIdList.map(async (staffId) => {
      const res = await http.open('GET/business/staff/working/area', { staffId });
      const { firstWeek, secondWeek, thirdWeek, forthWeek, scheduleType } = res;
      const startDate = res.startDate ? dayjs(res.startDate) : dayjs().startOf('date');

      return {
        ...res,
        staffId: +res.staffId,
        startDate,
        endDate: MaxStaffEndDate,
        origin: cloneDeep({
          firstWeek,
          secondWeek,
          thirdWeek,
          forthWeek,
          scheduleType,
          startDate,
        }),
      };
    }),
  );
  dispatch(staffScheduleServiceAreaMapBox.mergeItems(areaList));

  return areaList;
});

export const deleteStaffWorkingAreaOverride = action(async (dispatch, select, date: string, staffId: number) => {
  const business = select(selectCurrentBusiness());
  const isMobileGrooming = business.isMobileGrooming();
  if (!isMobileGrooming || !isNormal(staffId)) return;
  const staffScheduleOverrideAreaMap = select(staffScheduleAreaOverrideMapBox.mustGetItem(staffId));

  const { ongoing } = staffScheduleOverrideAreaMap.toObject();
  const area = ongoing.find((i) => i.overrideDate === date);

  if (area) {
    await http.open('DELETE/business/staff/override/area', { id: area.id });

    dispatch(
      staffScheduleAreaOverrideMapBox.mergeItem(staffId, {
        ongoing: ongoing.filter((i) => i.id !== area.id),
      }),
    );
  }
});

export const addStaffWorkingAreaOverride = action(
  async (_dispatch, select, staffId: number, params: DateOverrideDrawerValue) => {
    const { dates, value, workingArea } = params;
    const { timeRange = [] } = value ?? {};
    const business = select(selectCurrentBusiness());
    const isMobileGrooming = business.isMobileGrooming();
    if (!isMobileGrooming || !workingArea?.length || !dates?.length) return;

    await http.open(
      'POST/business/staff/override/area',
      dates.map((date) => ({
        staffId,
        workingArea: timeRange?.length ? workingArea : [],
        overrideDate: date.format(DATE_FORMAT_EXCHANGE),
      })),
    );
  },
);

/** 更新 service area 数据 */
export const updateStaffServiceArea = action(
  async (dispatch, select, staffId: number, input: Partial<StaffScheduleServiceArea>) => {
    const business = select(selectCurrentBusiness());
    const isMobileGrooming = business.isMobileGrooming();
    if (!isMobileGrooming) return;

    const obj = select(staffScheduleServiceAreaMapBox.mustGetItem(staffId));
    const raw = obj.toObject();
    const startDate = input.startDate ?? raw.startDate;
    const endDate = input.endDate ?? raw.endDate;

    const nextVal = {
      ...raw,
      ...input,
      staffId,
      startDate,
      endDate,
    };
    dispatch(staffScheduleServiceAreaMapBox.mergeItem(staffId, nextVal));

    return nextVal;
  },
);

export const pushStaffServiceArea = action(
  async (dispatch, select, staffIdList: number[] = [select(editingWorkingHourStaffIdBox)]) => {
    const business = select(selectCurrentBusiness());
    const isMobileGrooming = business.isMobileGrooming();
    if (!isMobileGrooming) return;

    await Promise.all(
      staffIdList.map((staffId) => {
        // 要保持 area 和 working hour 的数据一致性
        const { startDate, endDate, scheduleType } = select(
          staffScheduleWorkingHourMapBox.mustGetItem(staffId),
        ).toObject();
        const areaRaw = select(staffScheduleServiceAreaMapBox.mustGetItem(staffId)).toObject();

        return http.open('PUT/business/staff/working/area', {
          ...areaRaw,
          scheduleType,
          staffId,
          startDate: startDate.format(DATE_FORMAT_EXCHANGE),
          endDate: endDate.format(DATE_FORMAT_EXCHANGE),
        });
      }),
    );

    dispatch(syncStaffServiceArea(staffIdList));
  },
);

// override
export const getStaffDateOverride = action(async (dispatch, select, staffIdList: number[] = []) => {
  const business = select(selectCurrentBusiness());

  const { timeStaffMap = {} } = await StaffClient.getStaffAvailabilityOverride({
    businessId: `${business.id}`,
    availabilityType: AvailabilityType.BY_TIME,
    staffIdList: staffIdList.map(String),
  });

  // 如果 staffIdList 是空，area 是不支持获取所有的，保险起见要从 staffAvailability 返回的数据来获取 area
  const realStaffIdList = Object.keys(timeStaffMap);
  const areaList = await dispatch(getStaffWorkingAreaDateOverride(realStaffIdList.map(Number)));

  const handleOverrideData = (item: TimeAvailabilityDay): StaffScheduleDateOverride => {
    const { overrideDate, timeHourSettingList, timeDailySetting, isAvailable } = item;
    return {
      overrideDate,
      value: {
        isAvailable: isAvailable && timeHourSettingList.length > 0,
        timeRange: timeHourSettingList,
        limitationGroups: timeDailySetting.limitationGroups,
      },
    };
  };

  const overrideList = realStaffIdList.map((staffId) => {
    const { history = [], ongoing = [] } = timeStaffMap[staffId] || {};

    return {
      staffId: +staffId,
      history: history.map(handleOverrideData),
      ongoing: ongoing.map(handleOverrideData),
    };
  });

  dispatch(staffScheduleDateOverrideMapBox.mergeItems(overrideList));

  return {
    timeStaffMap,
    areaList,
  };
});

export const getStaffWorkingAreaDateOverride = action(async (dispatch, select, staffIdList: number[]) => {
  const business = select(selectCurrentBusiness());
  const isMobileGrooming = business.isMobileGrooming();
  if (!isMobileGrooming) {
    // 清空 area override 数据
    const resetAreaList = staffIdList.map((staffId) => ({
      staffId,
      history: [],
      ongoing: [],
    }));
    dispatch(staffScheduleAreaOverrideMapBox.mergeItems(resetAreaList));
    return [];
  }

  const areaList = await Promise.all(
    staffIdList.map(async (staffId) => {
      const area = await http.open('GET/business/staff/override/area', { staffId });

      return {
        staffId,
        history: area.history ?? [],
        ongoing: area.onGoing ?? [],
      };
    }),
  );

  dispatch(staffScheduleAreaOverrideMapBox.mergeItems(areaList));

  return areaList;
});

export const addStaffDateOverride = action(
  async (dispatch, select, staffId: number, params: DateOverrideDrawerValue, refetch = true) => {
    const { dates = [], value } = params;
    const { timeRange = [], limitationGroups = [], isAvailable } = value ?? {};
    const business = select(selectCurrentBusiness());
    const list = dates.map((date) => ({
      staffId,
      isAvailable: isAvailable && timeRange.length > 0,
      overrideDate: date.format(DATE_FORMAT_EXCHANGE),
      timeDailySetting: {
        limitationGroups,
      },
      timeHourSettingList: timeRange,
    }));

    await Promise.all([
      StaffClient.updateStaffAvailabilityOverride({
        businessId: `${business.id}`,
        staffId: `${staffId}`,
        overrideDays: [],
        slotOverrideDays: [],
        timeOverrideDays: list,
      }),

      dispatch([
        addStaffWorkingAreaOverride(staffId, params),
        staffScheduleDateOverrideMapBox.updateItem(staffId, (prev) => {
          const addedOngoingList = dates.map((date) => ({
            overrideDate: date.format(DATE_FORMAT_EXCHANGE),
            value,
          }));

          return prev.merge({
            ongoing: [...prev.ongoing, ...addedOngoingList],
          });
        }),
      ]),
    ]);

    refetch && (await dispatch(getCalendarViewData()));
    toastApi.success('Date override updated successfully.');
  },
);

// 接口已经支持多 date 删除，但是目前没有场景，并且 area 接口还不支持 multi date，所以每次只删单天
export const deleteStaffDateOverride = action(
  async (dispatch, select, date: string, staffId: number, refetch = true) => {
    const business = select(selectCurrentBusiness());

    await StaffClient.deleteStaffAvailabilityOverride({
      businessId: `${business.id}`,
      staffId: `${staffId}`,
      overrideDays: [date],
      availabilityType: AvailabilityType.BY_TIME,
    });

    await dispatch(deleteStaffWorkingAreaOverride(date, staffId));

    dispatch(
      staffScheduleDateOverrideMapBox.updateItem(staffId, (prev) =>
        prev.merge({
          ongoing: prev.ongoing.filter((i) => i.overrideDate !== date),
        }),
      ),
    );

    refetch && (await dispatch(getCalendarViewData()));
  },
);

export const modifyStaffDateOverride = action(
  async (dispatch, _select, day: string, staffId: number, params: DateOverrideDrawerValue) => {
    // 服务端之前的约定，先删除再添加实现比较简单
    await dispatch(deleteStaffDateOverride(day, staffId, false));
    await dispatch(addStaffDateOverride(staffId, params, false));
    await dispatch(getCalendarViewData());
  },
);

/** --------------------------- Staff Schedule By Slot ---------------------------------- */
export const getStaffWorkingSlot = action(async (dispatch, select, staffIdList: number[] = []) => {
  const business = select(selectCurrentBusiness());
  const { staffAvailabilityList } = await StaffClient.getStaffAvailability({
    businessId: `${business.id}`,
    availabilityType: AvailabilityType.BY_SLOT,
    staffIdList: staffIdList.map(String),
  });

  const list = staffAvailabilityList
    .map(({ slotAvailabilityDayList, staffId, slotStartSunday: startDate, scheduleType }) => {
      if (slotAvailabilityDayList.length !== 28) {
        console.error(
          '[StaffClient.getStaffAvailability error] slotAvailabilityDayList length is not 28. This may indicate an issue with the data source or API response.',
          'Expected length: 28. Actual length:',
          slotAvailabilityDayList.length,
          ' Staff ID:',
          staffId,
        );

        return null;
      }

      const weekSlotData = transformWeekDayList2WorkingSlot(slotAvailabilityDayList);

      return {
        ...weekSlotData,
        scheduleType,
        staffId: +staffId,
        startDate: startDate ? dayjs(startDate) : dayjs().startOf('date'),
        endDate: MaxStaffEndDate,
        origin: {
          ...cloneDeep(weekSlotData),
          scheduleType,
        },
      };
    })
    .filter(Boolean) as StaffScheduleWorkingSlot[];

  dispatch(staffScheduleWorkingSlotMapBox.mergeItems(list));

  return list;
});

export const getStaffSlotFreeServices = action(async (dispatch, select, staffIdList: number[] = []) => {
  const business = select(selectCurrentBusiness());

  const { defs } = await StaffClient.listSlotFreeServices({
    businessId: `${business.id}`,
    staffIds: staffIdList.map(String),
  });

  const slotFreeServicesList = defs.map(({ staffId, serviceIds }) => ({
    staffId: +staffId,
    serviceIds,
    origin: { serviceIds: [...serviceIds] },
  }));

  dispatch(staffScheduleSlotFreeServicesMapBox.mergeItems(slotFreeServicesList));

  return slotFreeServicesList;
});

export const updateSlotFreeServices = action(
  async (dispatch, select, params: UpdateSlotFreeServicesParams, syncToOrigin = false) => {
    await StaffClient.updateSlotFreeServices(params);

    dispatch(
      staffScheduleSlotFreeServicesMapBox.mergeItems(
        params.defs.map(({ staffId, serviceIds }) => ({
          staffId: +staffId,
          serviceIds,
          ...(syncToOrigin ? { origin: { serviceIds: [...serviceIds] } } : {}),
        })),
      ),
    );
  },
);

export const updateStaffScheduleWorkingSlot = action(
  (dispatch, select, staffId: number, params: Partial<StaffScheduleDraftData<StaffScheduleWorkingSlot>>) => {
    if (!isUndefined(params.scheduleType)) {
      const { scheduleType } = params;
      const { origin: originRawData } = select(staffScheduleWorkingSlotMapBox.mustGetItem(staffId));

      if (scheduleType !== originRawData?.scheduleType) {
        params.startDate = dayjs().startOf('week');
      } else {
        params.startDate = originRawData?.startDate;
      }
    }

    dispatch(staffScheduleWorkingSlotMapBox.mergeItem(staffId, params));
  },
);

export const putStaffWorkingSlot = action(async (dispatch, select, staffIdList: number[] = []) => {
  // 批量更新，根据 staffIdList 取现在的 draft 数据
  const business = select(selectCurrentBusiness());
  const staffAvailabilityList = staffIdList.map((staffId) => {
    const draft = select(staffScheduleWorkingSlotMapBox.mustGetItem(staffId)).toObject();
    const slotAvailabilityDayList = transformWorkingSlotMap2DayList(draft);

    return {
      isAvailable: true,
      staffId: String(staffId),
      scheduleType: Number(draft.scheduleType),
      slotAvailabilityDayList,
      timeAvailabilityDayList: [],
    };
  });

  await Promise.all([
    StaffClient.updateStaffAvailability({
      businessId: `${business.id}`,
      staffList: [],
      staffAvailabilityList,
    }),
    dispatch(
      updateSlotFreeServices({
        businessId: `${business.id}`,
        defs: staffIdList.map((staffId) => ({
          staffId: String(staffId),
          serviceIds: select(staffScheduleSlotFreeServicesMapBox.mustGetItem(staffId)).toObject().serviceIds,
        })),
      }),
    ),
  ]);

  dispatch(syncStaffSchedule(staffIdList, AvailabilityType.BY_SLOT));
});

// By slot override
/**
 * @param staffIdList 传空数组时，后端会返回所有 Staff 数据
 */
export const getStaffSlotOverride = action(async (dispatch, select, staffIdList: number[] = []) => {
  const business = select(selectCurrentBusiness());
  const { staffMap = {} } = await StaffClient.getStaffAvailabilityOverride({
    businessId: `${business.id}`,
    staffIdList: staffIdList.map(String),
    availabilityType: AvailabilityType.BY_SLOT,
  });

  const realStaffIdList = Object.keys(staffMap);
  const handleOverrideData = (data: SlotAvailabilityDay): StaffScheduleSlotOverride => {
    const { slotDailySetting, slotHourSettingList, isAvailable, ...rest } = data;
    return {
      ...rest,
      value: {
        isAvailable,
        slotHourSettingList,
        slotDailySetting,
      },
    };
  };

  const overrideList = realStaffIdList.map((staffId) => {
    const { history = [], ongoing = [] } = staffMap[staffId] || {};

    return {
      staffId: +staffId,
      history: history.map(handleOverrideData),
      ongoing: ongoing.map(handleOverrideData),
    };
  });

  dispatch(staffScheduleSlotOverrideMapBox.mergeItems(overrideList));

  return overrideList;
});

export const getAllSlotOverride = action(async (dispatch) => {
  await dispatch(getStaffSlotOverride());
});

export const addStaffSlotOverride = action(
  async (dispatch, select, staffId: number, params: DateOverrideDrawerSlotValue, refetch = true) => {
    const business = select(selectCurrentBusiness());

    const { dates = [], value } = params;

    if (!value) return;

    const list = dates.map((date) => ({
      ...value,
      staffId,
      isAvailable: !!value?.isAvailable,
      overrideDate: date.format(DATE_FORMAT_EXCHANGE),
    }));

    await StaffClient.updateStaffAvailabilityOverride({
      businessId: `${business.id}`,
      staffId: `${staffId}`,
      overrideDays: list as unknown as SlotAvailabilityDay[],
      slotOverrideDays: [],
      timeOverrideDays: [],
    });

    await dispatch(getStaffSlotOverride([staffId]));
    refetch && (await dispatch(getCalendarViewSlotData()));
    toastApi.success('Slot override updated successfully.');
  },
);

export const deleteStaffSlotOverride = action(
  async (dispatch, select, dates: string[], staffId: number, refetch = true) => {
    if (!select(staffScheduleSlotOverrideMapBox.mustGetItem(staffId)).isValidRecord) {
      await dispatch(getStaffSlotOverride([staffId]));
    }

    const business = select(selectCurrentBusiness());
    await StaffClient.deleteStaffAvailabilityOverride({
      businessId: `${business.id}`,
      staffId: `${staffId}`,
      overrideDays: dates,
      availabilityType: AvailabilityType.BY_SLOT,
    });

    await dispatch(getStaffSlotOverride([staffId]));
    refetch && (await dispatch(getCalendarViewSlotData()));
  },
);

export const modifyStaffSlotOverride = action(
  async (dispatch, _select, day: string, staffId: number, params: DateOverrideDrawerSlotValue) => {
    await dispatch(deleteStaffSlotOverride([day], staffId, false));
    await dispatch(addStaffSlotOverride(staffId, params, false));
    await dispatch(getCalendarViewSlotData());
  },
);
/** -------------------------------------------------------------------------------------------------------------------------- */

/** 同步数据 draft -> regular */
export const syncStaffServiceArea = action(async (dispatch, select, staffIdList: number[]) => {
  const business = select(selectCurrentBusiness());
  const isMobileGrooming = business.isMobileGrooming();
  if (!isMobileGrooming) return;

  const list = staffIdList.map((staffId) => select(staffScheduleServiceAreaMapBox.mustGetItem(staffId)).syncToOrigin());
  dispatch(staffScheduleServiceAreaMapBox.mergeItems(list));
});

/** 重置数据 regular -> draft */
export const resetStaffServiceArea = action(async (dispatch, select, staffIdList: number[]) => {
  const business = select(selectCurrentBusiness());
  const isMobileGrooming = business.isMobileGrooming();
  if (!isMobileGrooming) return;

  const list = staffIdList.map((staffId) => {
    return select(staffScheduleServiceAreaMapBox.mustGetItem(staffId)).reset();
  });

  dispatch(staffScheduleServiceAreaMapBox.mergeItems(list));
});

/** 同步数据 draft -> regular */
export const syncStaffSchedule = action(
  async (dispatch, select, staffIdList: number[], availabilityType: EnumValues<typeof AvailabilityType>) => {
    if (availabilityType === AvailabilityType.BY_TIME) {
      const list = staffIdList.map((staffId) => {
        const draft = select(staffScheduleWorkingHourMapBox.mustGetItem(staffId));
        return draft.syncToOrigin();
      });

      dispatch(staffScheduleWorkingHourMapBox.mergeItems(list));
    } else if (availabilityType === AvailabilityType.BY_SLOT) {
      const list = staffIdList.map((staffId) => {
        const draft = select(staffScheduleWorkingSlotMapBox.mustGetItem(staffId));
        return draft.syncToOrigin();
      });
      const slotFreeServicesList = staffIdList.map((staffId) => {
        const draft = select(staffScheduleSlotFreeServicesMapBox.mustGetItem(staffId));
        return draft.syncToOrigin();
      });

      dispatch([
        staffScheduleWorkingSlotMapBox.mergeItems(list),
        staffScheduleSlotFreeServicesMapBox.mergeItems(slotFreeServicesList),
      ]);
    }
  },
);

// 重置数据 regular -> draft
export const resetStaffSchedule = action(
  async (
    dispatch,
    select,
    staffIdList: number[],
    availabilityTypeList: EnumValues<typeof AvailabilityType>[] = AvailabilityType.values,
  ) => {
    const isBySlot = availabilityTypeList.includes(AvailabilityType.BY_SLOT);
    const isByTime = availabilityTypeList.includes(AvailabilityType.BY_TIME);

    if (isByTime) {
      const list = staffIdList.map((staffId) => {
        const draft = select(staffScheduleWorkingHourMapBox.mustGetItem(staffId));
        return draft.reset();
      });
      dispatch(staffScheduleWorkingHourMapBox.mergeItems(list));
    }

    if (isBySlot) {
      const list = staffIdList.map((staffId) => {
        const draft = select(staffScheduleWorkingSlotMapBox.mustGetItem(staffId));
        return draft.reset();
      });
      const slotFreeServicesList = staffIdList.map((staffId) => {
        const draft = select(staffScheduleSlotFreeServicesMapBox.mustGetItem(staffId));
        return draft.reset();
      });

      dispatch([
        staffScheduleWorkingSlotMapBox.mergeItems(list),
        staffScheduleSlotFreeServicesMapBox.mergeItems(slotFreeServicesList),
      ]);
    }
  },
);

export const getBizScheduleOpeningHour = action(
  async (dispatch, select, businessId: number = select(currentBusinessIdBox)) => {
    const { workingHours } = await BusinessClient.getWorkingHoursList({
      businessId: `${businessId}`,
    });
    dispatch(
      bizScheduleMapBox.mergeItem(businessId, {
        businessId,
        timeData: workingHours,
      }),
    );
  },
);

export const updateBizScheduleOpeningHour = action(
  async (dispatch, select, timeData: WeekTimeScheduleValue, businessId: number = select(currentBusinessIdBox)) => {
    await BusinessClient.updateWorkingHours({
      businessId: `${businessId}`,
      workingHours: timeData,
    });
    dispatch(bizScheduleMapBox.mergeItem(businessId, { timeData }));
  },
);

/** 批量更新 staff data */
export const batchCopyStaffScheduleWorkingHour = action(
  async (dispatch, select, sourceStaffId: number, targetStaffIdList: number[]) => {
    const business = select(selectCurrentBusiness());
    const sourceRaw = select(staffScheduleWorkingHourMapBox.mustGetItem(sourceStaffId)).toObject();

    // 把 raw 转成 28 周
    const timeAvailabilityDayList = transformWorkingHourMap2DayList(sourceRaw);
    // generate all target staff data
    const staffAvailabilityList = targetStaffIdList.map((targetStaffId) => {
      return {
        isAvailable: true,
        staffId: String(targetStaffId),
        timeScheduleType: +sourceRaw.scheduleType,
        timeAvailabilityDayList,
        slotAvailabilityDayList: [],
      };
    });

    await StaffClient.updateStaffAvailability({
      businessId: `${business.id}`,
      staffList: [],
      staffAvailabilityList,
    });

    targetStaffIdList.map((targetStaffId) => {
      const nextVal = {
        ...cloneDeep(sourceRaw),
        staffId: targetStaffId,
      };
      const { firstWeek, secondWeek, thirdWeek, forthWeek, scheduleType, startDate } = nextVal;
      const nextOriginVal = {
        firstWeek,
        secondWeek,
        thirdWeek,
        forthWeek,
        scheduleType,
        startDate,
      };

      dispatch(
        updateStaffScheduleWorkingHour(targetStaffId, {
          ...nextVal,
          origin: nextOriginVal,
        }),
      );
    });

    if (business.isMobileGrooming()) {
      // 一定要从 working hour 里获取 scheduleType
      const { startDate, endDate, scheduleType } = select(
        staffScheduleWorkingHourMapBox.mustGetItem(sourceStaffId),
      ).toObject();
      const sourceAreaRaw = select(staffScheduleServiceAreaMapBox.mustGetItem(sourceStaffId)).toObject();

      await Promise.all(
        targetStaffIdList.map((targetStaffId) =>
          http.open('PUT/business/staff/working/area', {
            ...sourceAreaRaw,
            staffId: targetStaffId,
            scheduleType,
            startDate: startDate.format(DATE_FORMAT_EXCHANGE),
            endDate: endDate.format(DATE_FORMAT_EXCHANGE),
          }),
        ),
      );

      dispatch(
        staffScheduleServiceAreaMapBox.mergeItems(
          targetStaffIdList.map((targetStaffId) => {
            const nextVal = {
              ...cloneDeep(sourceAreaRaw),
              staffId: targetStaffId,
            };

            const { firstWeek, secondWeek, thirdWeek, forthWeek } = nextVal;
            return {
              ...nextVal,
              scheduleType,
              startDate,
              endDate,
              origin: {
                firstWeek,
                secondWeek,
                thirdWeek,
                forthWeek,
                scheduleType,
                startDate,
              },
            };
          }),
        ),
      );
    }
  },
);

export const batchCopyStaffScheduleWorkingSlot = action(
  async (dispatch, select, sourceStaffId: number, targetStaffIdList: number[]) => {
    const business = select(selectCurrentBusiness());
    const sourceRaw = select(staffScheduleWorkingSlotMapBox.mustGetItem(sourceStaffId)).toObject();

    const slotAvailabilityDayList = transformWorkingSlotMap2DayList(sourceRaw);
    const staffAvailabilityList = targetStaffIdList.map((targetStaffId) => {
      return {
        isAvailable: true,
        staffId: String(targetStaffId),
        scheduleType: +sourceRaw.scheduleType,
        slotAvailabilityDayList,
        timeAvailabilityDayList: [],
      };
    });

    await StaffClient.updateStaffAvailability({
      businessId: `${business.id}`,
      staffList: [],
      staffAvailabilityList,
    });

    targetStaffIdList.map((targetStaffId) => {
      const nextVal = {
        ...cloneDeep(sourceRaw),
        staffId: targetStaffId,
      };
      const { firstWeek, secondWeek, thirdWeek, forthWeek, scheduleType, startDate } = nextVal;
      const nextOriginVal = {
        firstWeek,
        secondWeek,
        thirdWeek,
        forthWeek,
        scheduleType,
        startDate,
      };

      dispatch(
        updateStaffScheduleWorkingSlot(targetStaffId, {
          ...nextVal,
          origin: nextOriginVal,
        }),
      );
    });
  },
);

/** --------------------------- Calendar View Data ---------------------------------- */
export const getCalendarViewData = action(
  async (dispatch, select, startDate: Dayjs = select(currentWorkingHourWeekBox)) => {
    const business = select(selectCurrentBusiness());
    const isMobileGrooming = business.isMobileGrooming();
    const query = {
      startDate: startDate.format(DATE_FORMAT_EXCHANGE),
      endDate: startDate.add(6, 'day').format(DATE_FORMAT_EXCHANGE),
    };

    // 获取 calendar view 数据
    const [{ staffAvailabilityList }, area, overrideArea] = await Promise.all([
      StaffClient.getStaffCalenderView({
        availabilityType: AvailabilityType.BY_TIME,
        businessId: `${business.id}`,
        startDate: query.startDate,
      }),
      isMobileGrooming && http.open('POST/business/workingArea/shiftManagement', query),
      isMobileGrooming && http.open('POST/business/staff/override/area/shiftManagement', query),
    ]);

    const newVal = staffAvailabilityList.map((item) => {
      const {
        staffId,
        timeAvailabilityDayMap,
        timeScheduleType: scheduleType = StaffScheduleType.Every1Week,
        timeStartSunday: startDate,
      } = item;

      const workingHourDayMap = Object.entries(timeAvailabilityDayMap).reduce(
        (map, [date, value]) => {
          const { isAvailable, timeDailySetting, timeHourSettingList } = value;
          map[date] = {
            isAvailable: isAvailable && timeHourSettingList?.length > 0,
            timeRange: timeHourSettingList || [],
            limitationGroups: timeDailySetting.limitationGroups,
          };

          return map;
        },
        {} as Record<string, WorkingHourValue>,
      );

      return {
        staffId: +staffId,
        area: area ? area.find((a) => +a.staffId === +staffId)?.workingAreaRange : {},
        overrideArea: overrideArea ? overrideArea.find((a) => +a.staffId === +staffId)?.workingAreaRange : {},
        workingHour: workingHourDayMap,
        scheduleType,
        startDate: dayjs(startDate),
        ownKey: CalendarViewDataRecord.ownKey(business.id, +staffId),
      };
    });

    dispatch(calendarViewDataMapBox.mergeItems(newVal));

    return {
      area,
      overrideArea,
    };
  },
);

export const getCalendarViewSlotData = action(
  async (dispatch, select, startDate: Dayjs = select(currentWorkingHourWeekBox)) => {
    const business = select(selectCurrentBusiness());
    const { staffAvailabilityList } = await StaffClient.getStaffCalenderView({
      availabilityType: AvailabilityType.BY_SLOT,
      businessId: `${business.id}`,
      startDate: startDate.format(DATE_FORMAT_EXCHANGE),
    });

    // 构造 calendar 结构
    const newVal = staffAvailabilityList.map((item) => {
      const {
        staffId,
        isAvailable,
        slotAvailabilityDayMap,
        scheduleType = StaffScheduleType.Every1Week,
        slotStartSunday: startDate,
      } = item;

      return {
        staffId: Number(staffId),
        isAvailable,
        scheduleType,
        workingSlot: slotAvailabilityDayMap,
        startDate: dayjs(startDate),
        ownKey: CalendarViewSlotDataRecord.ownKey(business.id, Number(staffId)),
      };
    });

    dispatch(calendarViewSlotDataMapBox.mergeItems(newVal));
  },
);

export const toggleCalendarServiceArea = action((dispatch, select, key: CalendarViewListType, newVal: boolean) => {
  const business = select(selectCurrentBusiness());
  const currentType = select(selectCalendarViewType);
  if (newVal) {
    dispatch(staffScheduleCalendarViewList.pushList(business.id, key));
  } else {
    currentType.size > 1 && dispatch(staffScheduleCalendarViewList.deleteItem(business.id, key));
  }
});
/** -------------------------------------------------------------------------------------------------------------------------- */

export const mergeCACD = action(async () => {
  return await http.open('POST/business/workingArea/changeShiftManagement');
});
