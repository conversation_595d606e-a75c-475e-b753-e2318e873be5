/*
 * @since 2020-08-20 16:28:25
 * <AUTHOR> <<EMAIL>>
 */

import { action } from 'amos';
import { OrderedMap } from 'immutable';
import { type Timezone } from 'timezones.json';
import {
  LK_MOEGO_PAYROLL_ONBOARDING_STEP,
  MOEGO_GROOMING_REPORT_EDIT,
  MOEGO_SHIFT_MANAGEMENT_NEW_TIP,
} from '../../config/host/const';
import { type GetCompaniesExtraInfoResponse } from '@moego/api-web/moego/api/organization/v1/company_api';
import { lazyLoadCountryDataList, lazyLoadTimeZones } from '../../utils/lazyLoad';
import { flat } from '../utils/utils';
import {
  AFTER_LAST_PAYROLL_ONBOARDING_STEP,
  type PayrollOnboardingStep,
  countryListBox,
  layoutStateBox,
  onboardingBox,
  openInAppVisibleBox,
  switchMapBox,
  timezoneMapBox,
} from './common.boxes';

export const loadTimezoneList = action(async (dispatch, _select) => {
  const r = await lazyLoadTimeZones();
  dispatch(
    timezoneMapBox.setState(OrderedMap(flat(r.default.map((tz) => tz.utc.map((u) => [u, tz] as [string, Timezone]))))),
  );
});

export const toggleSwitch = action((dispatch, _select, field: string, opened: boolean, switching: boolean) => {
  dispatch(switchMapBox.mergeItem(field, { field, opened, switching }));
});

export const loadCountryDataList = action(async (dispatch) => {
  const r = await lazyLoadCountryDataList();
  const used = new Set<string>();
  r.currencies.all = r.currencies.all.filter((c) => {
    if (!c.symbol || used.has(c.code)) {
      return false;
    }
    used.add(c.code);
    return true;
  });
  dispatch(countryListBox.setState(r));
});

export const toggleOpenInAppVisible = action((dispatch, _select, visible: boolean) => {
  dispatch(openInAppVisibleBox.setState(visible));
});

export const setPayrollOnboardingStep = action((dispatch, _select, step: PayrollOnboardingStep) => {
  localStorage.setItem(LK_MOEGO_PAYROLL_ONBOARDING_STEP, step.toString());
  dispatch(onboardingBox.setState((s) => ({ ...s, payrollOnboardingStep: step })));
});

export const skipPayrollOnboarding = action(async (dispatch, _select) => {
  dispatch(setPayrollOnboardingStep(AFTER_LAST_PAYROLL_ONBOARDING_STEP));
});

export const goNextPayrollOnboarding = action(async (dispatch, select) => {
  const onboarding = select(onboardingBox);
  dispatch(setPayrollOnboardingStep((onboarding.payrollOnboardingStep + 1) as PayrollOnboardingStep));
});

export const setStaffShiftManagementNewDisappear = action((dispatch, _select, step: number = 1) => {
  localStorage.setItem(MOEGO_SHIFT_MANAGEMENT_NEW_TIP, step.toString());
  dispatch(onboardingBox.setState((s) => ({ ...s, staffShiftManagementNew: step })));
});

export const setGroomingReportEditDisappear = action((dispatch, _select) => {
  localStorage.setItem(MOEGO_GROOMING_REPORT_EDIT, '1');
  dispatch(onboardingBox.setState((s) => ({ ...s, groomingReportEdit: 1 })));
});

export const setCompanyExtraInfo = action(
  (dispatch, _select, info: GetCompaniesExtraInfoResponse['companyExtraInfoMap']) => {
    dispatch(layoutStateBox.setState((s) => ({ ...s, companyExtraInfo: info })));
  },
);

export const setCompanySortedList = action((dispatch, _select, list: number[]) => {
  dispatch(layoutStateBox.setState((s) => ({ ...s, companySortedList: list })));
});

export const setNavAsideStatus = action((dispatch, _select, status: boolean) => {
  dispatch(layoutStateBox.setState((s) => ({ ...s, showAside: status })));
});
