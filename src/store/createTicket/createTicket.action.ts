import { action } from 'amos';
import { isNil, omitBy } from 'lodash';
import { type TicketFieldKey } from '../../container/CreateTicket/hooks/useTicketFieldsEdit';
import { http } from '../../middleware/api';
import { MetadataApiClient } from '../../middleware/clients';
import { type OpenApiModels } from '../../openApi/schema';
import { jsonParse } from '../../utils/utils';
import { currentBusinessIdBox } from '../business/business.boxes';
import { type RecordProps } from '../utils/RecordMap';
import {
  type MultiStaffPreference,
  type MultiStaffPreferenceRecord,
  multiStaffPreferenceMapBox,
  petApplicableServiceMapBox,
  ticketFieldsEditBox,
} from './createTicket.boxes';
import { selectMultiStaffPreference } from './createTicket.selector';
import { MultiStaffPriceType, type PetServiceWithCategory } from './createTicket.types';

export function getDefaultMultiStaffPreference(): MultiStaffPreference {
  return {
    displayCommission: false,
    autoMemorizeWorkPattern: true,
    priceType: 0,
    workPattern: {
      workMode: MultiStaffPriceType.priceRatio,
      staffNumber: 2,
      priceRatio: [],
      duration: [],
      taskName: [],
    },
  };
}

export const getMultiStaffPreference = action(
  async (dispatch, select, businessId: number = select(currentBusinessIdBox)) => {
    const res = await MetadataApiClient.describeMetadata({
      key: 'multi_staff_setting',
    });
    const {
      values: { multi_staff_setting },
    } = res;
    const multiStaffPreference = jsonParse<MultiStaffPreference>(multi_staff_setting, getDefaultMultiStaffPreference());
    dispatch(multiStaffPreferenceMapBox.mergeItem(businessId, multiStaffPreference));
    return multiStaffPreference;
  },
);

export const updateMultiStaffPreference = action(
  async (dispatch, select, params: Partial<MultiStaffPreference>, signal?: AbortSignal) => {
    const preference = select(selectMultiStaffPreference());
    // FIXME(leo): why toJS()?
    const { businessId, ...preConfig } = preference.toJS() as RecordProps<MultiStaffPreferenceRecord>;
    const finalParams = {
      ...preConfig,
      ...omitBy(params, isNil),
    };
    const res = await MetadataApiClient.updateMetadata(
      {
        key: 'multi_staff_setting',
        value: JSON.stringify(finalParams),
      },
      {
        signal,
      },
    );
    dispatch(multiStaffPreferenceMapBox.mergeItem(businessId, finalParams));
    return res;
  },
);

type EstCommissionParams = OpenApiModels['GET/business/payroll/calculation/service']['Req'];

export const calcEstCommission = action(async (dispatch, select, params: EstCommissionParams) => {
  return await http.open('GET/business/payroll/calculation/service', params);
});

export const editWaitingTicketAction = action(
  async (dispatch, select, params: OpenApiModels['PUT/grooming/appointment/waiting/still']['Req']) => {
    return await http.open('PUT/grooming/appointment/waiting/still', params);
  },
);

export const createWaitingTicketAction = action(
  async (dispatch, select, params: OpenApiModels['POST/grooming/appointment/waiting']['Req']) => {
    return await http.open('POST/grooming/appointment/waiting', params);
  },
);

export const createTicketAction = action(
  async (dispatch, select, params: OpenApiModels['POST/grooming/appointment']['Req']) => {
    return await http.open('POST/grooming/appointment', params);
  },
);

export const updateTicketAction = action(
  async (dispatch, select, params: OpenApiModels['PUT/grooming/appointment']['Req']) => {
    return await http.open('PUT/grooming/appointment', params);
  },
);

export const getGroomingAppointmentDetails = action(
  async (dispatch, select, params: OpenApiModels['GET/grooming/appointment']['Req']) => {
    return await http.open('GET/grooming/appointment', params);
  },
);

export type UpdateRepeatTicketParams = OpenApiModels['PUT/grooming/v2/appointment/repeat']['Req'];
export const updateRepeatTicketAction = action(async (dispatch, select, params: UpdateRepeatTicketParams) => {
  return await http.open('PUT/grooming/v2/appointment/repeat', params);
});

export const updateRepeatTicketAlertNotesAction = action(
  async (
    dispatch,
    select,
    params: OpenApiModels['PUT/grooming/appointment/alertNotes/repeat']['Req'],
    type: number,
  ) => {
    return await http.open('PUT/grooming/appointment/alertNotes/repeat', params, { query: { type } });
  },
);

export const updateRepeatTicketColorCodeAction = action(
  async (dispatch, select, params: OpenApiModels['PUT/grooming/appointment/color/repeat']['Req']) => {
    return await http.open('PUT/grooming/appointment/color/repeat', params);
  },
);

export const updateTicketCommentsAction = action(
  async (dispatch, select, params: OpenApiModels['PUT/grooming/appointment/comments']['Req']) => {
    return await http.open('PUT/grooming/appointment/comments', params);
  },
);

export const updateTicketFieldsEditAction = action(
  async (dispatch, select, newVal: TicketFieldKey | TicketFieldKey[]) => {
    const businessId = select(currentBusinessIdBox);
    const args = ([] as TicketFieldKey[]).concat(newVal);
    return dispatch(ticketFieldsEditBox.pushList(businessId, ...args));
  },
);

export const resetTicketFieldsEditAction = action(async (dispatch, select) => {
  const businessId = select(currentBusinessIdBox);
  return dispatch(ticketFieldsEditBox.setList(businessId, []));
});

export const setPetApplicableServices = action(
  async (dispatch, select, petId: number, services: PetServiceWithCategory[]) => {
    dispatch(petApplicableServiceMapBox.mergeItem(petId, { services }));
  },
);
