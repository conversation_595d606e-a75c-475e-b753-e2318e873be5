import {
  type GetAppointmentsAfterEmailResponse,
  type GetEmailListResponse,
  type GetRecipientListResponse,
  type MassEmailSendRequest,
  type SendTestEmailRequest,
} from '@moego/api-web/moego/api/message/v1/marketing_email_service';
import {
  type AttachmentDef,
  type RecipientFilterDef,
} from '@moego/api-web/moego/models/message/v1/marketing_email_defs';
import {
  type MarketingEmailModel,
  type MarketingEmailTemplateModelBriefView,
} from '@moego/api-web/moego/models/message/v1/marketing_email_models';
import { action } from 'amos';
import { type DeepPartial } from 'monofile-utilities/lib/types';
import {
  OperatorKeyMap,
  OperatorMap,
} from '../../container/Client/ClientList/componentsLegacy/ClientFilters/ClientFilters.config';
import { transformFilterValues } from '../../container/Client/ClientList/componentsLegacy/ClientFilters/ClientFilters.utils';
import { http } from '../../middleware/api';
import { MarketingEmailClient } from '../../middleware/clients';
import { ComMoegoCommonParamsFilterParamsProperty as FilterParamsProperty } from '../../openApi/customer-schema';
import { type OpenApiModels } from '../../openApi/schema';
import { currentAccountIdBox } from '../account/account.boxes';
import { selectCurrentBusiness } from '../business/business.selectors';
import { currentCompanyIdBox } from '../company/company.boxes';
import { type AddFilterModel, ClientFilterListSourceMap } from '../customer/clientFilters.boxes';
import { type GetCustomerListInput } from '../customer/customer.actions';
import { CustomerListFilterRecord, businessCustomerListBox } from '../customer/customer.boxes';
import { StaffKinds, staffMapBox } from '../staff/staff.boxes';
import { selectBusinessStaffs } from '../staff/staff.selectors';
import { type PageParams } from '../utils/PagedList';
import { ID_INVALID } from '../utils/identifier';
import {
  type EmailCampaignFilterParams,
  EmailCampaignReportRecord,
  type EmailCampaignTemplateFilters,
  type EmailCampaignTemplateFiltersRes,
  emailAvailableInfo,
  emailCampaignFilterParams,
  emailCampaignTemplateFilters,
  emailCampaignsStatusBox,
  emailReportListBox,
  emailReportMapBox,
} from './emailCampaign.boxes';
import {
  EmailCampaignLocalFilterTemplateList,
  EmailCampaignPerformance,
  EmailCampaignStatus,
  createSpecificCustomerFilter,
} from './emailCampaign.util';

export type EmailCampaignRecord = GetEmailListResponse['list'][number];

export type EmailCampaignReportDetail = MarketingEmailModel;

export type EmailCampaignReportRecipient = GetRecipientListResponse['recipients'][number];

export type EmailCampaignReportBooking = GetAppointmentsAfterEmailResponse['appointments'][number];

export type EmailCampaignRecipientRecord = GetRecipientListResponse['recipients'][number];

export type TestEmailDetail = SendTestEmailRequest;

export type EmailDetail = MassEmailSendRequest['email'];

// get attachments type
export type AttachmentItem = AttachmentDef;

export type TemplateListItem = MarketingEmailTemplateModelBriefView;

export type SendEmailReq = MassEmailSendRequest;

export type TemplateInfo = MarketingEmailTemplateModelBriefView;

export const setEmailCampaignFilterParams = action(
  async (dispatch, select, params: Partial<EmailCampaignFilterParams>) => {
    const old = select(emailCampaignFilterParams);
    dispatch([emailCampaignFilterParams.setState({ ...old, ...params })]);
  },
);

export const resetEmailCampaignFilterParams = action(async (dispatch, select) => {
  // keep page size
  const { pageSize } = select(emailCampaignFilterParams);
  dispatch([emailCampaignFilterParams.setState({ type: EmailCampaignStatus.All, keyword: '', pageSize })]);
});

export const getEmailCampaignList = action(async (dispatch, select, pageParams: PageParams) => {
  const params = select(emailCampaignFilterParams);
  const res = await MarketingEmailClient.getEmailList({
    status: params.type,
    subject: params.keyword,
    pagination: { pageNo: String(pageParams.pageNo), pageSize: String(pageParams.pageSize) },
  });
  return res;
});

export const getEmailCampaignReport = action(async (dispatch, select, id: string) => {
  const res = await MarketingEmailClient.getEmailDetail({ id });
  return res;
});

export const getEmailCampaignRecipients = action(
  async (dispatch, select, id: string, pageParams: PageParams, status: number) => {
    const type = status === EmailCampaignPerformance.Sent ? EmailCampaignPerformance.All : status;
    const res = MarketingEmailClient.getRecipientList({
      id,
      pagination: { pageNo: String(pageParams.pageNo), pageSize: String(pageParams.pageSize) },
      status: type,
    });
    return res;
  },
);

export const getEmailCampaignBookings = action(async (dispatch, select, id: string, pageParams: PageParams) => {
  const res = MarketingEmailClient.getAppointmentsAfterEmail({
    id,
    pagination: { pageNo: String(pageParams.pageNo), pageSize: String(pageParams.pageSize) },
  });
  return res;
});

export const cancelEmailCampaignSend = action(async (dispatch, select, id: string) => {
  const res = await MarketingEmailClient.cancelScheduleEmail({ id });
  return res;
});

export const sendEmailCampaignNow = action(async (dispatch, select, id: string) => {
  const res = await MarketingEmailClient.sendNow({ id });
  return res;
});

export const getEmailCampaignAvailableCount = action(async (dispatch, _select) => {
  const res = await MarketingEmailClient.getAvailableEmailsCount({});
  const { availableEmails, usedEmails } = res;
  dispatch(
    emailAvailableInfo.setState({
      availableEmails: Number(availableEmails),
      usedEmails: Number(usedEmails),
    }),
  );
  return res;
});

export const getEmailCountReport = action(async (dispatch, _select) => {
  const res = await http.open('GET/message/send/email/count/report', {});
  const { data = [] } = res;
  dispatch([
    emailReportMapBox.mergeItemsDeep(
      data.map((i) => ({ ...i, ownKey: EmailCampaignReportRecord.ownKey(i.companyId, i.cycleBeginTime) })),
    ),
    emailReportListBox.setList(
      data[0]?.companyId ?? ID_INVALID,
      data.map((i) => EmailCampaignReportRecord.ownKey(i.companyId, i.cycleBeginTime)),
    ),
  ]);
  return res;
});

export const rescheduleEmailCampaign = action(async (dispatch, select, id: string, sendTime: string) => {
  const res = await MarketingEmailClient.rescheduleEmail({ id, sendTime });
  return res;
});

export const sendTestEmail = action(async (dispatch, select, detail: TestEmailDetail) => {
  const res = await MarketingEmailClient.sendTestEmail(detail);
  return res;
});

export const sendEmail = action(
  async (_dispatch, select, detail: DeepPartial<EmailDetail>, draftId?: string, input?: GetCustomerListInput) => {
    const sendObject: SendEmailReq = {
      email: detail as EmailDetail,
      draftId,
    };
    if (input) {
      const accountId = select(currentAccountIdBox);
      const ownKey = CustomerListFilterRecord.ownKey(accountId, ClientFilterListSourceMap.MassEmail);
      const state = select(businessCustomerListBox.mustGetItem(ownKey));
      const filter = state.getFilter(input);
      sendObject.recipientFilter = {
        clientFilter: input.specificCustomerList
          ? JSON.stringify(createSpecificCustomerFilter(input.specificCustomerList))
          : JSON.stringify({
              filters: filter.filters,
              queries: filter.queries,
            }),
      } as RecipientFilterDef;
    }
    const res = await MarketingEmailClient.massEmailSend(sendObject);
    return res;
  },
);

export const saveEmailAsDraft = action(
  async (dispatch, select, detail: DeepPartial<EmailDetail>, draftId?: string, input?: GetCustomerListInput) => {
    const sendObject: SendEmailReq = {
      email: detail as EmailDetail,
      draftId,
    };
    if (input) {
      const accountId = select(currentAccountIdBox);
      const ownKey = CustomerListFilterRecord.ownKey(accountId, ClientFilterListSourceMap.MassEmail);
      const state = select(businessCustomerListBox.mustGetItem(ownKey));
      const filter = state.getFilter(input);
      sendObject.recipientFilter = {
        clientFilter: JSON.stringify({
          filters: filter.filters,
          queries: filter.queries,
        }),
      } as RecipientFilterDef;
    }
    const res = await MarketingEmailClient.saveEmailDraft(sendObject);
    return res;
  },
);

export const getEmailTemplateList = action(async (dispatch, select, pageParams: PageParams) => {
  const res = await MarketingEmailClient.getMarketingEmailTemplateList({
    pagination: { pageNo: String(pageParams.pageNo), pageSize: String(pageParams.pageSize) },
  });
  return res;
});

export const getEmailTemplateDetail = action(async (dispatch, select, id: string) => {
  const res = await MarketingEmailClient.getMarketingEmailTemplateDetail({ id });
  return res;
});

export const delEmail = action(async (dispatch, select, id: string) => {
  const res = await MarketingEmailClient.deleteEmail({ id });
  return res;
});

export const getEmailReply = action(async (dispatch, select, id: string) => {
  const res = await MarketingEmailClient.viewEmailReply({ id });
  return res;
});

export const calculateEmailCampaignCredit = action(async (dispatch, select, count: number) => {
  const res = await MarketingEmailClient.calculateCreditCost({ recipientCount: `${count}` });
  return parseInt(res.creditCost);
});

export const setTemplateFilterBox = action(async (dispatch, select, filters: AddFilterModel[]) => {
  if (Array.isArray(filters)) {
    dispatch(
      emailCampaignTemplateFilters.setState(() => {
        return { filters };
      }),
    );
  }
});

export const setEmailTemplateFilters = action(async (dispatch, select, filterStr: string, templateId: string) => {
  if (filterStr === '{}' || !filterStr) {
    if (!templateId) {
      dispatch(setTemplateFilterBox([]));
      return;
    }
    // set template default filter
    const business = select(selectCurrentBusiness);
    const staffList = select(selectBusinessStaffs);
    const staffMap = select(staffMapBox);
    if (templateId === EmailCampaignLocalFilterTemplateList.Zipcode && business.addressZipcode) {
      const extraFilter: AddFilterModel = {
        source: ClientFilterListSourceMap.MassEmail,
        property: FilterParamsProperty.zipcode,
        operator: OperatorMap.In,
        values: [business.addressZipcode],
      };
      dispatch(setTemplateFilterBox([extraFilter]));
      return;
    } else if (templateId === EmailCampaignLocalFilterTemplateList.StarEmployee && business.ownerAccountId) {
      const [owner] = staffList.filter((staffId) => {
        const staff = staffMap.mustGetItem(staffId);
        return staff.employeeCategory === StaffKinds.Owner;
      });
      if (owner) {
        const extraFilter: AddFilterModel = {
          source: ClientFilterListSourceMap.MassEmail,
          property: FilterParamsProperty.preferred_groomer,
          operator: OperatorMap.In,
          values: [owner],
        };
        dispatch(setTemplateFilterBox([extraFilter]));
        return;
      }
    }
    dispatch(setTemplateFilterBox([]));
    return;
  }
  // if here, save the template filter from backend
  dispatch(setEmailPresevedFilters(filterStr));
});

export const setEmailPresevedFilters = action(async (dispatch, select, filterStr: string) => {
  if (!filterStr) {
    dispatch(setTemplateFilterBox([]));
    return;
  }
  try {
    const filterRes = JSON.parse(filterStr) as EmailCampaignTemplateFilters | EmailCampaignTemplateFiltersRes;
    let filterList: AddFilterModel[] = [];

    // here, the template default filter & email's filter are different
    if (Array.isArray(filterRes.filters)) {
      filterList = filterRes.filters;
    } else {
      filterList = filterRes.filters.filters;
    }

    filterList = filterList
      .map((filter) => {
        filter.operator = filter.operator ? OperatorKeyMap[filter.operator] : undefined;
        filter.source = ClientFilterListSourceMap.MassEmail;
        return filter;
      })
      .map((filter) => transformFilterValues(filter))
      .filter((item) => !!item.operator) as AddFilterModel[];

    dispatch(setTemplateFilterBox(filterList));
  } catch (e) {
    // JSON parse error, do nothing
    console.error(e);
  }
});

type PurchaseEmailParams = OpenApiModels['POST/payment/email/buyEmailPackage']['Req'];

export const purchaseEmailCredit = action(
  async (dispatch, select, params: PurchaseEmailParams, companyId: number = select(currentCompanyIdBox)) => {
    const res = await http.open('POST/payment/email/buyEmailPackage', {
      ...params,
      companyId,
    });
    return res;
  },
);

export const getEmailCampaignStatus = action(async (dispatch, select) => {
  const companyId = select(currentCompanyIdBox);
  dispatch(
    emailCampaignsStatusBox.mergeItem(companyId, {
      companyId,
      loading: true,
    }),
  );
  const {
    pagination: { total },
  } = await dispatch(getEmailCampaignList({ pageNo: 1, pageSize: 10 }));
  const hasCampaigns = +total > 0;
  dispatch(
    emailCampaignsStatusBox.mergeItem(companyId, {
      companyId,
      loading: false,
      isInitialized: true,
      hasCampaigns,
    }),
  );
});
