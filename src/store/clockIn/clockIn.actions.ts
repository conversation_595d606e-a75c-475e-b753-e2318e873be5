import { type GetClockInOutStaffsParams } from '@moego/api-web/moego/api/organization/v1/staff_api';
import { type UpdateClockInOutSettingDef } from '@moego/api-web/moego/models/organization/v1/clock_in_out_setting_defs';
import { action } from 'amos';
import dayjs from 'dayjs';
import { http } from '../../middleware/api';
import { CompanyClient, StaffClient } from '../../middleware/clients';
import { type OpenApiModels } from '../../openApi/schema';
import { DATE_FORMAT_EXCHANGE } from '../../utils/DateTimeUtil';
import { currentAccountIdBox } from '../account/account.boxes';
import { currentBusinessIdBox } from '../business/business.boxes';
import { currentCompanyIdBox } from '../company/company.boxes';
import { staffMapBox } from '../staff/staff.boxes';
import { clockInOutStaffMapBox, companyClockInSettingMapBox, staffClockInOutListMapBox } from './clockIn.boxes';

export const getCompanyClockInOutSetting = action(async (dispatch, select) => {
  const companyId = select(currentCompanyIdBox);
  const { clockInOutSetting } = await CompanyClient.getClockInOutSetting({});
  dispatch([companyClockInSettingMapBox.mergeItem(companyId, clockInOutSetting)]);
});

export const getTodayRecord = action(async (dispatch, select, input: string) => {
  const companyId = select(currentCompanyIdBox);
  const { data } = await http.open('GET/business/staff/clockInOut', {
    date: input,
  });

  const clockInOutSetting = (await CompanyClient.getClockInOutSetting({})).clockInOutSetting;

  const now = dayjs().format(DATE_FORMAT_EXCHANGE);
  dispatch([
    clockInOutStaffMapBox.mergeItems(
      data?.staffStatusList
        ? data.staffStatusList.map((i) => ({
            staffId: i.staffId,
            isClockIn: i.isClockIn,
            isClockOut: i.isClockOut,
            clockInTime: +i.clockInTime,
            clockOutTime: +i.clockOutTime,
            ownKey: `${i.staffId}-${now}`,
            date: now,
            requireAccessCode: i.requireAccessCode,
          }))
        : [],
    ),
    staffMapBox.mergeItems(
      data?.staffStatusList
        ? data.staffStatusList.map((i) => ({
            ...i,
            accessCodeCopy: i.accessCode,
            id: i.staffId,
          }))
        : [],
    ),
    companyClockInSettingMapBox.mergeItem(companyId, clockInOutSetting),
  ]);
});

export const staffClockInOut = action(
  async (dispatch, select, input: OpenApiModels['PUT/business/clockInOut']['Req']) => {
    await http.open('PUT/business/clockInOut', input);
    const currentBusinessId = select(currentBusinessIdBox);
    input.operateType ??= 1;
    if (input.businessId && input.businessId !== currentBusinessId) return;
    dispatch(
      clockInOutStaffMapBox.mergeItem(`${input.staffId}-${input.date}`, {
        isClockIn: input.operateType === 1 || input.operateType === 2,
        isClockOut: input.operateType === 2,
        [input.operateType === 1 ? 'clockInTime' : 'clockOutTime']: new Date().valueOf() / 1000,
      }),
    );
  },
);

export const updateCompanyClockInOutSetting = action(async (dispatch, select, input: UpdateClockInOutSettingDef) => {
  const companyId = select(currentCompanyIdBox);
  await CompanyClient.updateClockInOutSetting({
    clockInOutSetting: input,
  });
  dispatch([companyClockInSettingMapBox.mergeItem(companyId, input)]);
});

export const updateClockInOutAccessCode = action(
  async (dispatch, select, input: OpenApiModels['PUT/business/clockInOut/setting']['Req']) => {
    await http.open('PUT/business/clockInOut/setting', input);
    dispatch(
      staffMapBox.mergeItem(input.staffId!, { accessCode: input.newAccessCode, accessCodeCopy: input.newAccessCode }),
    );
  },
);

export const changeClockInOutAccessCode = action((dispatch, select, input: { code: string; staffId: number }) => {
  dispatch([
    staffMapBox.mergeItem(input.staffId, {
      accessCode: input.code,
    }),
  ]);
});

export const modifyClockInOutTime = action(
  async (dispatch, select, input: OpenApiModels['PUT/business/clockInOut/log']['Req']) => {
    const res = await http.open('PUT/business/clockInOut/log', input);
    return res.code === 200;
  },
);

export const addClockInOutRecord = action(
  async (dispatch, select, input: OpenApiModels['POST/business/clockInOut/log']['Req']) => {
    await http.open('POST/business/clockInOut/log', input);
  },
);

export const deleteClockInOutRecord = action(
  async (dispatch, select, input: OpenApiModels['DELETE/business/clockInOut/log']['Req']) => {
    await http.open('DELETE/business/clockInOut/log', input);
  },
);

export const getClockInOutStaffs = action(async (dispatch, select, input: GetClockInOutStaffsParams) => {
  const accountId = select(currentAccountIdBox);
  const { clockInOutStaffs } = await StaffClient.getClockInOutStaffs(input);
  dispatch(
    staffClockInOutListMapBox.mergeItem(accountId, {
      accountId,
      clockInOutStaffList: clockInOutStaffs,
    }),
  );
  return clockInOutStaffs;
});

export const getClockInOutLog = action(
  async (dispatch, select, input: OpenApiModels['GET/business/clockInOut/log']['Req']) => {
    const res = await http.open('GET/business/clockInOut/log', input);
    return res;
  },
);
