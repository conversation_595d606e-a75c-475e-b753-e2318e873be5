import {
  type CreateAppointmentParams,
  type CreateAppointmentParamsPetBelonging,
} from '@moego/api-web/moego/api/appointment/v1/appointment_api';
import { type RescheduleEvaluationServiceParams } from '@moego/api-web/moego/api/appointment/v1/appointment_schedule_api';
import { type DeletePetEvaluationParams } from '@moego/api-web/moego/api/appointment/v1/pet_detail_api';
import {
  type ListEvaluationStatusByPetServiceParams,
  type ListPetEvaluationHistoryParams,
  type UpdatePetEvaluationParams,
} from '@moego/api-web/moego/api/business_customer/v1/business_pet_evaluation_api';
import {
  type CreateEvaluationParams,
  type GetApplicableEvaluationListParams,
  type UpdateEvaluationParams,
} from '@moego/api-web/moego/api/offering/v1/evaluation_api';
import { AppointmentNoteType } from '@moego/api-web/moego/models/appointment/v1/appointment_note_enums';
import { type PetEvaluationModel } from '@moego/api-web/moego/models/business_customer/v1/business_pet_evaluation_models';
import { type ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { action } from 'amos';
import { isNumber } from 'lodash';
import { createAppointment } from '../../container/Appt/store/appt.api';
import {
  AppointmentScheduleClient,
  AppointmentServiceClient,
  BusinessPetEvaluationClient,
  EvaluationClient,
  PetDetailClient,
} from '../../middleware/clients';
import { abortSubmit } from '../../utils/abortSubmit';
import { currentBusinessIdBox } from '../business/business.boxes';
import { type ApptDetailModalStateBox, apptDetailModalStatusBox } from '../calendarLatest/calendar.boxes';
import { currentCompanyIdBox } from '../company/company.boxes';
import { GroomingTicketSource } from '../grooming/grooming.boxes';
import { isNormal } from '../utils/identifier';
import {
  type EvaluationApptDetailDrawerBox,
  type EvaluationApptDetailDrawerEditBox,
  businessEvaluationListBox,
  businessEvaluationMapBox,
  businessListWithApplicableEvaluationMapBox,
  companyEvaluationListBox,
  companyEvaluationMapBox,
  evaluationApptDetailDrawerBox,
  evaluationApptDetailDrawerEditBox,
  evaluationQuickAddFieldsBox,
  petEvaluationActivitiesMapBox,
} from './evaluation.boxes';
import { type EvaluationQuickAddFields } from './evaluation.types';
import { createEvaluationListOwnId, transformEvaluationActivities } from './evaluation.utils';

/**
 * AS 之后才能调用的接口，获取当前 company 的 evaluation 设置
 */
export const getEvaluationSetting = action(async (dispatch, select) => {
  const companyId = select(currentCompanyIdBox);
  const res = await EvaluationClient.getEvaluationList({});

  dispatch([
    companyEvaluationMapBox.mergeItems(res.evaluations),
    companyEvaluationListBox.setList(
      companyId,
      res.evaluations.map((item) => item.id),
    ),
  ]);
  return res.evaluations;
});

export const createEvaluationSetting = action(
  async (dispatch, select, input: CreateEvaluationParams['evaluationDef']) => {
    const companyId = select(currentCompanyIdBox);
    const res = await EvaluationClient.createEvaluation({ evaluationDef: input });

    dispatch([
      companyEvaluationMapBox.mergeItem(res.evaluationModel.id, res.evaluationModel),
      companyEvaluationListBox.pushList(companyId, res.evaluationModel.id),
    ]);
  },
);

export const updateEvaluationSetting = action(
  async (dispatch, select, input: UpdateEvaluationParams['evaluationDef'] & { id: string }) => {
    const res = await EvaluationClient.updateEvaluation({ id: input.id, evaluationDef: input });
    dispatch([companyEvaluationMapBox.mergeItem(res.evaluationModel.id, res.evaluationModel)]);
  },
);

export const deleteEvaluationService = action(async (dispatch, select, id: string) => {
  await EvaluationClient.deleteEvaluation({ id });
  const companyId = select(currentCompanyIdBox);
  dispatch([companyEvaluationMapBox.deleteItem(id), companyEvaluationListBox.deleteItem(companyId, id)]);
});

export const setEvaluationApptDetailDrawerEdit = action(
  async (dispatch, select, input: Partial<EvaluationApptDetailDrawerEditBox>) => {
    dispatch(evaluationApptDetailDrawerEditBox.setState((pre) => ({ ...pre, ...input })));
  },
);

export const setEvaluationApptDetailDrawerStep = action(
  async (dispatch, select, input: Partial<EvaluationApptDetailDrawerEditBox['stepInfo']>) => {
    dispatch(
      evaluationApptDetailDrawerEditBox.setState((pre) => ({ ...pre, stepInfo: { ...pre.stepInfo, ...input } })),
    );
  },
);

export const setEvaluationApptDetailDrawer = action(
  async (dispatch, select, input: Partial<EvaluationApptDetailDrawerBox>) => {
    const preBoxState = select(evaluationApptDetailDrawerBox);
    dispatch(evaluationApptDetailDrawerBox.setState({ ...preBoxState, ...input, scene: input.scene ?? 'overview' }));
  },
);

export const setEvaluationApptDetailModalState = action(
  async (dispatch, select, input: Partial<ApptDetailModalStateBox>) => {
    const preBoxState = select(apptDetailModalStatusBox);
    dispatch(apptDetailModalStatusBox.setState({ ...preBoxState, ...input }));
  },
);

export type UpdateEvaluationQuickAddInput =
  | Partial<EvaluationQuickAddFields>
  | ((config: EvaluationQuickAddFields) => Partial<EvaluationQuickAddFields>);

export const updateEvaluationQuickAddFields = action(async (dispatch, select, input: UpdateEvaluationQuickAddInput) => {
  const nextConfig = typeof input === 'function' ? input(select(evaluationQuickAddFieldsBox)) : input;
  dispatch(evaluationQuickAddFieldsBox.setState((pre) => ({ ...pre, ...nextConfig })));
});

export const rescheduleEvaluationService = action(
  async (dispatch, select, input: RescheduleEvaluationServiceParams) => {
    const res = await AppointmentScheduleClient.rescheduleEvaluationService(input);
    return res;
  },
);
export interface CreateNewEvaluationParams {
  businessId: string;
  customerId: string;
  petId: string;
  evaluationInfo: EvaluationQuickAddFields['evaluationInfo'];
}

/**
 * 创建新的 evaluation
 */
export const submitNewEvaluation = action(async (dispatch, select, businessId = select(currentBusinessIdBox)) => {
  const {
    customerId,
    petIds,
    evaluationInfo,
    colorCode,
    petBelongings: OriginBelongings,
    alertNotes,
    ticketComment,
  } = select(evaluationQuickAddFieldsBox);

  if (!evaluationInfo.startDate || !isNumber(evaluationInfo.startTime)) {
    abortSubmit('evaluation startDate or startTime is required');
  }

  const petBelongings = petIds
    .map((petId) => {
      const [belongings] = OriginBelongings?.[petId] || [];
      if (belongings && belongings?.name && isNormal(petId)) {
        return {
          petId: petId.toString(),
          name: belongings.name,
          area: belongings.area,
          photoUrl: belongings.photoUrl,
        };
      }
      return undefined;
    })
    .filter(Boolean) as CreateAppointmentParamsPetBelonging[];

  const petDetails = evaluationInfo.petServiceList.map((details) => {
    return {
      petId: details.petId.toString(),
      services: [],
      addOns: [],
      evaluations: [
        {
          ...details,
          staffId: details.staffId || undefined,
          startDate: evaluationInfo.startDate!,
          startTime: evaluationInfo.startTime!,
        },
      ],
    };
  });

  const params: CreateAppointmentParams = {
    businessId: businessId + '',
    appointment: {
      customerId: customerId + '',
      source: GroomingTicketSource.Web,
      colorCode,
      allPetsStartAtSameTime: false,
    },
    notes: [
      {
        note: alertNotes,
        type: AppointmentNoteType.ALERT_NOTES,
      },
      {
        note: ticketComment,
        type: AppointmentNoteType.COMMENT,
      },
    ].filter((note) => note.note),
    petDetails: petDetails,
    petBelongings: petBelongings,
  };

  const res = await dispatch(createAppointment(params));
  return res.appointmentId;
});

export const getApplicableEvaluationList = action(
  async (dispatch, select, params: GetApplicableEvaluationListParams) => {
    const res = await EvaluationClient.getApplicableEvaluationList(params);
    return res.evaluations;
  },
);

export const getPetLastEvaluationApptList = action(
  async (dispatch, select, petId: string, customerId: string, businessId: string) => {
    const res = await AppointmentServiceClient.getInProgressEvaluationAppointment({
      petId,
      businessId,
      customerId,
    });
    return res.appointmentId;
  },
);

export const getBusinessApplicableEvaluationList = action(
  async (
    dispatch,
    select,
    signal?: AbortSignal,
    serviceItemType?: ServiceItemType,
    businessId: number = select(currentBusinessIdBox),
  ) => {
    const res = await EvaluationClient.getApplicableEvaluationList(
      {
        businessId: businessId + '',
        serviceItemType: serviceItemType || undefined,
      },
      {
        signal,
      },
    );

    const listOwnId = createEvaluationListOwnId(businessId, serviceItemType);
    dispatch([
      businessEvaluationMapBox.mergeItems(res.evaluations),
      businessEvaluationListBox.setList(
        listOwnId,
        res.evaluations.map((item) => item.id),
      ),
    ]);

    return res.evaluations;
  },
);

export const getBusinessListWithApplicableEvaluation = action(
  async (dispatch, select, companyId: number = select(currentCompanyIdBox)) => {
    const res = await EvaluationClient.getBusinessListWithApplicableEvaluation({});
    dispatch(
      businessListWithApplicableEvaluationMapBox.mergeItem(companyId as unknown as string, {
        companyId: companyId as unknown as string,
        businessList: res.businesses.map((item) => item.id),
      }),
    );
    return res.businesses;
  },
);

export const preCreateEvaluationServiceCheck = action(
  async (dispatch, select, startDate?: string, businessId: string = select(currentBusinessIdBox) + '') => {
    const res = await PetDetailClient.preCreateEvaluationServiceCheck({
      startDate,
      businessId,
    });
    return res.petNumForEvaluation;
  },
);

export interface PetEvaluation extends Omit<PetEvaluationModel, 'companyId' | 'createdAt' | 'updatedAt'> {
  evaluationName: string;
}

export const listPetEvaluation = action(async (_dispatch, _select, params: ListPetEvaluationHistoryParams) => {
  const res = await BusinessPetEvaluationClient.listPetEvaluation(params);
  const petEvaluations: PetEvaluation[] = res.petEvaluations.map(({ id, petId, evaluationId, evaluationStatus }) => {
    const evaluationName = res.evaluations.find((item) => item.id === evaluationId)?.name ?? '';
    return {
      id,
      petId,
      evaluationId,
      evaluationName,
      evaluationStatus,
    };
  });
  return petEvaluations;
});

export const updatePetEvaluation = action(async (_dispatch, _select, params: UpdatePetEvaluationParams) => {
  const res = await BusinessPetEvaluationClient.updatePetEvaluation(params);
  return res;
});

export const listEvaluationHistory = action(async (dispatch, select, params: ListPetEvaluationHistoryParams) => {
  const res = await BusinessPetEvaluationClient.listPetEvaluationHistory(params);
  const data = transformEvaluationActivities(res, select);
  dispatch(petEvaluationActivitiesMapBox.setItem(params.petId, { petId: params.petId, activities: data }));

  return data;
});

export const listEvaluationStatusByPetService = action(
  async (dispatch, select, params: ListEvaluationStatusByPetServiceParams, signal?: AbortSignal) => {
    const res = await BusinessPetEvaluationClient.listEvaluationStatusByPetService(params, { signal });
    return res;
  },
);

export const deletePetEvaluation = action(async (dispatch, select, params: DeletePetEvaluationParams) => {
  const res = await PetDetailClient.deletePetEvaluation(params);
  return res;
});
