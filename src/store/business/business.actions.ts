/*
 * @since 2020-08-19 11:09:52
 * <AUTHOR> <<EMAIL>>
 */

import { action } from 'amos';
import { asMap } from 'monofile-utilities/lib/as-map';
import { http } from '../../middleware/api';
import { BusinessClient } from '../../middleware/clients';
import { type OpenApiModels } from '../../openApi/schema';
import { id2NumberAndRequired } from '../../utils/api/api';
import { omitEmpty } from '../../utils/misc';
import { currentAccountIdBox } from '../account/account.boxes';
import { getServiceDetailList } from '../service/actions/public/service.actions';
import { ServiceType } from '../service/category.boxes';
import { ServiceActive, ServiceStatus } from '../service/service.boxes';
import { currentStaffIdBox, staffMapBox } from '../staff/staff.boxes';
import { ID_ANONYMOUS, isNormal } from '../utils/identifier';
import {
  businessMapBox,
  businessOptionsBox,
  businessPayrollExceptionIdListBox,
  businessPayrollExceptionMapBox,
  businessPayrollSettingMapBox,
  currentBusinessIdBox,
} from './business.boxes';
import { selectWorkingLocationIdList } from './location.selectors';
import { getTaxList } from './tax.actions';
import { taxMapBox } from './tax.boxes';

export type BusinessExtraInfo = OpenApiModels['GET/business/company/extraInfo']['Res'];

// createTime&updateTime 在open api上被转化成了string类型.本质上还是number
export const updateBusiness = action(async (dispatch, select, input: OpenApiModels['PUT/business/info']['Req']) => {
  await http.open('PUT/business/info', input as any);
  const businessId = select(currentBusinessIdBox);
  const options = select(businessOptionsBox);
  dispatch(
    businessMapBox.mergeItem(
      businessId,
      omitEmpty({
        ...input,
        id: businessId,
        calendarFormat: options.calendarFormatList.find((d) => d.type === input.calendarFormatType)?.label,
        dateFormat: options.dateFormatList.find((d) => d.type === input.dateFormatType)?.label,
        numberFormat: options.numberFormatList.find((d) => d.type === input.numberFormatType)?.label,
        unitOfWeight: options.unitOfWeightList.find((d) => d.type === input.unitOfWeightType)?.label,
        unitOfDistance: options.unitOfDistanceList.find((d) => d.type === input.unitOfDistanceType)?.label,
      }),
    ),
  );
});

export const advancedUpdateBusiness = action(
  async (
    dispatch,
    select,
    input: Partial<OpenApiModels['PUT/business/company/business']['Req']>,
    businessId: number = select(currentBusinessIdBox),
  ) => {
    await http.open('PUT/business/company/business', { ...input, businessId });
    dispatch(
      businessMapBox.mergeItem(
        businessId,
        omitEmpty({
          ...input,
          id: businessId,
        }),
      ),
    );
  },
);

export const updateMoeGoPayRestrictedBannerDismissed = action(
  (dispatch, select, dismissed: boolean, businessId: number = select(currentBusinessIdBox)) => {
    dispatch(businessMapBox.mergeItem(businessId, { payRestrictedBannerDismissed: dismissed }));
  },
);

export const getBusinessOptions = action(async (dispatch) => {
  const r = await http.open('GET/business/options');
  dispatch(
    businessOptionsBox.merge({
      ...r.data.preference,
      currencyMap: asMap(r.data.preference.currencyList || [], 'currencyCode'),
    }),
  );
});

/**
 * @deprecated 优先使用 getLocationDetail 获取 business 信息，注意 getTaxList 不会在 getLocationDetail 中获取
 */
export const getBusinessDetail = action(async (dispatch, _select) => {
  const {
    data: { tax, info, preference },
  } = await http.open('GET/business/setting');
  dispatch([
    currentBusinessIdBox.setState(info.id),
    businessMapBox.mergeItem(info.id, { ...info, ...preference }),
    taxMapBox.mergeItems(tax.map((item) => ({ ...id2NumberAndRequired(item), businessId: `${item.businessId}` }))),
  ]);
  dispatch(getTaxList());
});

export const setCurrentBusiness = action(async (dispatch, select, id: number) => {
  await BusinessClient.switchBusiness({
    businessId: `${id}`,
  });
  dispatch(currentBusinessIdBox.setState(id));
});

export type AddBusinessInput = OpenApiModels['POST/business/create']['Req'];

export const uploadFile = action(async (dispatch, select, file: Blob, restOptions?: Record<string, any>) => {
  const formData = new FormData();
  formData.append('file', file);
  const r = await http.open('POST/business/upload/uploadFile', void 0, { formData, ...restOptions });
  return r.data;
});

export const base64ToBlob = (fileName: string, base64Data: string) => {
  const arr = base64Data.split(',');
  // eslint-disable-next-line sonarjs/slow-regex
  const fileType = arr[0].match(/:(.*?);/)![1];
  const bstr = atob(arr[1]);
  let l = bstr.length;
  const u8Arr = new Uint8Array(l);
  while (l--) {
    u8Arr[l] = bstr.charCodeAt(l);
  }
  try {
    return new File([u8Arr], fileName, { type: fileType, lastModified: Date.now() });
  } catch {
    return new Blob([u8Arr]);
  }
};

export const getBusinessPayrollSetting = action(async (dispatch, select) => {
  const payrollSetting = await http.open('GET/business/payroll/setting/business');
  const accountId = select(currentAccountIdBox);
  dispatch(
    businessPayrollSettingMapBox.mergeItem(accountId, {
      splitTipsMethod: payrollSetting.splitTipsMethod,
      newPayrollEnable: payrollSetting.newPayrollEnable,
    }),
  );
  return payrollSetting;
});

export const updateBusinessPayrollSetting = action(
  async (dispatch, select, input: OpenApiModels['PUT/business/payroll/setting/business']['Req']) => {
    const payrollSetting = await http.open('PUT/business/payroll/setting/business', input);
    const accountId = select(currentAccountIdBox);

    dispatch(
      businessPayrollSettingMapBox.mergeItem(accountId, {
        ...payrollSetting,
      }),
    );

    return payrollSetting;
  },
);

export const getBusinessPayrollExceptionList = action(async (dispatch, select) => {
  const exceptionList = await http.open('GET/business/payroll/setting/exception/list');
  const businessId = select(currentBusinessIdBox);

  if (exceptionList.length > 0) {
    const newExceptionList = await dispatch(getShiftedExceptionList(exceptionList));
    dispatch([
      businessPayrollExceptionIdListBox.setList(
        businessId,
        newExceptionList.map((e) => e.id),
      ),
      businessPayrollExceptionMapBox.mergeItems(newExceptionList),
    ]);

    return newExceptionList;
  }
  return [];
});

export const createBusinessPayrollException = action(
  async (dispatch, select, input: OpenApiModels['POST/business/payroll/setting/exception']['Req']) => {
    const exception = await http.open('POST/business/payroll/setting/exception', input);
    const businessId = select(currentBusinessIdBox);

    const [newException] = await dispatch(getShiftedExceptionList([exception]));

    dispatch([
      businessPayrollExceptionIdListBox.pushList(businessId, exception.id),
      businessPayrollExceptionMapBox.mergeItem(exception.id, newException),
    ]);
    return newException;
  },
);

export const updateBusinessPayrollException = action(
  async (dispatch, _select, input: OpenApiModels['POST/business/payroll/setting/exception']['Req']) => {
    const exception = await http.open('POST/business/payroll/setting/exception', input);

    const [newException] = await dispatch(getShiftedExceptionList([exception]));

    dispatch(businessPayrollExceptionMapBox.mergeItem(exception.id, newException));
    return newException;
  },
);

export const updateBusinessPayrollExceptionList = action(
  async (dispatch, select, input: OpenApiModels['POST/business/payroll/setting/exception/list']['Req']) => {
    const exceptionList = await http.open('POST/business/payroll/setting/exception/list', input);

    if (exceptionList.length > 0) {
      const newExceptionList = await dispatch(getShiftedExceptionList(exceptionList));
      const businessId = select(currentBusinessIdBox);
      dispatch([
        businessPayrollExceptionIdListBox.setList(
          businessId,
          newExceptionList.map((e) => e.id),
        ),
        businessPayrollExceptionMapBox.mergeItems(newExceptionList),
      ]);

      return newExceptionList;
    }
    return [];
  },
);

export const deleteBusinessPayrollException = action(
  async (dispatch, select, id: number, businessId: number = select(currentBusinessIdBox)) => {
    const isDeleted = await http.open('DELETE/business/payroll/setting/exception', { id });
    if (isDeleted) {
      const exceptionIdList = select(businessPayrollExceptionIdListBox.getList(businessId));
      const exceptionId = exceptionIdList.find((exceptionId) => exceptionId === id);
      if (exceptionId) {
        dispatch([
          businessPayrollExceptionIdListBox.deleteItem(businessId, exceptionId),
          businessPayrollExceptionMapBox.deleteItem(exceptionId),
        ]);
      }
    }

    return isDeleted;
  },
);

export const getShiftedExceptionList = action(
  async (dispatch, _select, exceptionList: OpenApiModels['GET/business/payroll/setting/exception/list']['Res']) => {
    const serviceIdList = exceptionList.map((exception) => exception.serviceId);
    const serviceDetailList = await dispatch(getServiceDetailList(serviceIdList));
    const deletedServiceId = serviceDetailList.filter((s) => s.status === ServiceStatus.Deleted).map((s) => s.id);

    const newExceptionList = exceptionList.map((e) => {
      const service = serviceDetailList.find((s) => s.id === e.serviceId);
      return {
        ...e,
        serviceName: service?.name ?? '',
        isServiceDeleted: deletedServiceId.includes(e.serviceId),
        serviceType: service?.type ?? ServiceType.Service,
        isServiceInactive: service?.inactive ?? ServiceActive.Active,
      };
    });

    return newExceptionList;
  },
);

export const getBusinessExtraInfo = action(async (_dispatch, _select) => {
  const r = await http.open('GET/business/company/extraInfo');
  return r;
});

/**
 * 切换系统当前使用的 businessId
 */
export const switchBusiness = action(async (dispatch, select, businessId: number) => {
  const currentBusinessId = select(currentBusinessIdBox);
  if (currentBusinessId !== businessId) {
    await dispatch(setCurrentBusiness(businessId));
    // 直接用 getAccountInfo 消耗太大了，里面调了一吨接口
    const { account, business, staff, preference } = await http.open('GET/business/account/v2/info');
    dispatch([
      currentBusinessIdBox.setState(businessId),
      businessMapBox.mergeItems(business ? [{ ...business, ...preference }] : []),
      staffMapBox.mergeItems(
        staff ? [{ ...staff, id: staff.staffId, businessId: business.id, accountId: account.accountId }] : [],
      ),
      currentStaffIdBox.setState(staff ? staff.staffId : ID_ANONYMOUS),
    ]);
  }
});

/**
 * 根据working location，判断是否有权限切换到指定business
 */
export const switchBusinessWithCheckWorkingLocation = action(async (dispatch, select, bizId?: number | string) => {
  const businessId = typeof bizId === 'string' ? +bizId : bizId;
  if (!isNormal(businessId)) {
    return;
  }

  const workingLocationIds = select(selectWorkingLocationIdList());
  if (workingLocationIds.includes('' + businessId)) {
    await dispatch(switchBusiness(businessId));
  }
});
