import {
  type CompanyQuestionRecordParams,
  type CreateCompanyParams,
} from '@moego/api-web/moego/api/organization/v1/company_api';
import { type UpdateCompanyPreferenceSettingDef } from '@moego/api-web/moego/models/organization/v1/company_defs';
import { action } from 'amos';
import { isNil } from 'lodash';
import { http } from '../../middleware/api';
import { CompanyClient } from '../../middleware/clients';
import { type OpenApiModels } from '../../openApi/schema';
import { id2NumberAndRequired } from '../../utils/api/api';
import { currentAccountIdBox } from '../account/account.boxes';
import { businessMapBox, businessOptionsBox, currentBusinessIdBox } from '../business/business.boxes';
import { type A2PStatus, ShowA2PAlertBarStatusList } from '../message/a2p.config';
import { StaffKinds } from '../staff/staff.boxes';
import { selectStaff } from '../staff/staff.selectors';
import { ID_ANONYMOUS, isNormal } from '../utils/identifier';
import { get } from '../utils/utils';
import {
  type BillingStatus,
  EnterpriseMapBox,
  billingMapBox,
  companyAccountOwnedListBox,
  companyBillingListBox,
  companyBusinessListBox,
  companyListBox,
  companyMapBox,
  companySubscriptionHistoryListBox,
  companySubscriptionHistoryMapBox,
  currentCompanyIdBox,
  emailBillingMapBox,
  enterpriseCompanyAccountOwnedListBox,
  msgBillingMapBox,
  themeBox,
} from './company.boxes';
import { selectCompanies, selectCompanyBusinessList } from './company.selectors';
import { getPlanStatus } from './company.utils';
import { CreditCardRecord, creditCardListBox, creditCardMapBox } from './creditCard.boxes';
interface EnterpriseCompanyBillingData {
  [key: string]: TableDataForCompanyListAboutEnterprise[];
}

export type TCompanyBusiness =
  OpenApiModels['GET/payment/subscription/getCompaniesAndBillingData']['Res']['data'][number]['businesses'][number];

export type TableDataForCompanyListAboutEnterprise = {
  companyId: number;
  enterpriseId: string;
  companyName: string;
  locationNum: number;
  vansNum: number;
  payerType: number;
  isOwner: boolean;
  isEnterpriseOwner: boolean;
  subscriptionAmount: number;
  price: number;
  planStatus: string;
  sms: {
    amount: number;
    isAutoReload: boolean;
    prevPurchaseAmount: number;
  };
  campaign: {
    amount: number;
    isAutoReload: boolean;
  };
  planTier: {
    planVersion: number;
    premiumType: number;
  };
};

export const getAccountCompanyList = action(
  async (dispatch, select, accountId: number = select(currentAccountIdBox)) => {
    const { data } = await http.open('GET/payment/subscription/getCompaniesAndBillingData', {});

    const companyList = data.slice().map((company) => ({
      ...company,
      ...company.companyBillingData?.planTier,
      companyBillingData: {
        ...company.companyBillingData,
        downgradePlanData: {
          ...company.companyBillingData?.downgradePlanData,
          ...company.companyBillingData?.downgradePlanData?.planTier,
        },
        currentPlanTier: company.companyBillingData?.currentPlanTier,
      },
      isEnterpriseCompany: isNormal(company.enterpriseOwnerId),
      isEnterpriseOwner: company.enterpriseOwnerId === String(accountId),
      isOwner: company.companyOwnerId === String(accountId),
      a2pAlertBarVisible: ShowA2PAlertBarStatusList.includes(company.businessLine?.a2p?.a2pStatus as A2PStatus),
      businessLine: {
        ...company.businessLine,
        phoneNumberList: company.businessLine?.phoneNumbers,
      },
      companyUsedStaffCount: company.businesses.reduce((prev, cur) => prev + cur.usedStaffCount, 0),
    })) as any[];

    const ownedCompanyList = data.filter((company) => {
      return company.businesses.every((biz) => {
        return biz.employeeCategory === StaffKinds.Owner;
      });
    });

    const ownedEnterpriseCompanyList = data.filter((company) => {
      return company.businesses.every((biz) => {
        return biz.employeeCategory === StaffKinds.EnterpriseOwner;
      });
    });

    const tableDataForCompanyListAboutEnterprise: TableDataForCompanyListAboutEnterprise[] = data.map((company) => {
      const { companyBillingData, messageReportItem, emailReportItem } = company;
      const { msgBilling, emailBilling, payerType, planTier } = companyBillingData || {};
      const {
        subscriptionAmount = 0,
        purchaseAmount = 0,
        purchaseLeftover = 0,
        used2wayMessage = 0,
        usedAutoMessage = 0,
        usedCall = 0,
      } = messageReportItem || {};
      return {
        companyId: company.companyId,
        enterpriseId: company.enterpriseId,
        companyName: company.companyName,
        locationNum: company.locationNum,
        vansNum: company.vansNum,
        payerType,
        isOwner: company.companyOwnerId === String(accountId),
        isEnterpriseOwner: company.enterpriseOwnerId === String(accountId),
        subscriptionAmount: company.subscriptionAmount,
        price: company.companyBillingData?.price,
        planStatus: getPlanStatus(company),
        planTier,
        sms: {
          amount:
            subscriptionAmount + purchaseAmount + purchaseLeftover - used2wayMessage - usedAutoMessage - usedCall || 0,
          isAutoReload: !!msgBilling?.autoReload,
          prevPurchaseAmount: msgBilling?.amount || 0,
        },
        campaign: {
          amount: emailReportItem?.availableEmails,
          isAutoReload: !!emailBilling?.autoReload,
        },
      };
    });

    const enterpriseCompanyBillingData: EnterpriseCompanyBillingData = tableDataForCompanyListAboutEnterprise
      .filter((company) => company.enterpriseId)
      .reduce((prev: EnterpriseCompanyBillingData, cur: TableDataForCompanyListAboutEnterprise) => {
        prev[cur.enterpriseId] = prev[cur.enterpriseId] || [];
        prev[cur.enterpriseId].push(cur);
        return prev;
      }, {});

    const newEnterpriseCompanyBillingData = Object.keys(enterpriseCompanyBillingData).map((key) => {
      return {
        enterpriseId: key,
        data: enterpriseCompanyBillingData[key],
      };
    });

    dispatch([
      companyMapBox.mergeItems(companyList),
      EnterpriseMapBox.mergeItems(newEnterpriseCompanyBillingData),
      companyListBox.setList(accountId, [
        ...data.filter((i) => i.companyBillingData?.companyId).map((i) => i.companyId),
        ...data.filter((i) => !i.companyBillingData?.companyId).map((i) => i.companyId),
      ]),
      companyAccountOwnedListBox.setList(
        accountId,
        ownedCompanyList.map((company) => company.companyId),
      ),
      enterpriseCompanyAccountOwnedListBox.setList(
        accountId,
        ownedEnterpriseCompanyList.map((company) => company.companyId),
      ),
    ]);

    data.forEach((company) => {
      dispatch([
        companyBusinessListBox.setList(
          company.companyId,
          company.businesses.map((b) => b.businessId),
        ),
        businessMapBox.mergeItems(
          company.businesses.map((biz) => ({
            ...biz,
            id: biz.businessId,
          })),
        ),
      ]);

      if (company.companyBillingData?.cardList) {
        dispatch([
          creditCardMapBox.mergeItems(
            company.companyBillingData.cardList.map((card) => ({
              ...card,
              expMonth: Number(card.expMonth),
              expYear: Number(card.expYear),
            })),
          ),
          creditCardListBox.setList(
            CreditCardRecord.ownKey(company.companyId, accountId),
            company.companyBillingData.cardList.map((card) => card.cardId),
          ),
        ]);
      }
      if (company.companyBillingData?.charges) {
        dispatch([
          companyBillingListBox.setList(
            company.companyId,
            company.companyBillingData.charges.map((c) => c.chargeId),
          ),
          billingMapBox.mergeItems(
            company.companyBillingData.charges.map((charge) => ({
              ...charge,
              status: charge.status as BillingStatus,
              created: Number(charge.created),
            })),
          ),
          msgBillingMapBox.mergeItem(
            company.companyId,
            Object.assign({
              companyId: company.companyId,
              ...company.companyBillingData.msgBilling,
            }),
          ),
          emailBillingMapBox.mergeItem(
            company.companyId,
            Object.assign({
              companyId: company.companyId,
              ...company.companyBillingData.emailBilling,
            }),
          ),
        ]);
      }
    });

    return { companyList, ownedCompanyList, ownedEnterpriseCompanyList };
  },
);

export const getCompanyList = action(async (dispatch, select, signal?: AbortSignal) => {
  const { companyStaffs } = await CompanyClient.queryCompanyStaffByAccount(
    {},
    {
      signal,
    },
  );
  const accountId = select(currentAccountIdBox);

  dispatch([
    companyMapBox.mergeItems(
      companyStaffs.map((company) => ({
        ...company.company,
        companyId: Number(company.company.id),
        enterpriseId: String(company.company.enterpriseId || ID_ANONYMOUS),
        id: Number(company.company.id),
        accountId: Number(company.company.accountId),
      })),
    ),
    companyListBox.setList(
      accountId,
      companyStaffs.map((company) => Number(company.company.id)),
    ),
  ]);

  return companyStaffs;
});

export const getCompanyInfo = action(
  async (dispatch, select, companyId: number, accountId: number = select(currentAccountIdBox)) => {
    const { data: company } = await http.open('GET/business/company/byCompanyId', {
      companyId,
    });
    dispatch([
      companyMapBox.mergeItem(
        companyId,
        Object.assign({
          ...company,
          companyName: company.name,
          enterpriseId: Number(company.enterpriseId || ID_ANONYMOUS),
        }),
      ),
      companyListBox.setList(
        accountId,
        [company].map((c) => c.id),
      ),
    ]);
    return company;
  },
);

export const getEnterpriseTheme = action(async (dispatch, _select) => {
  const preferenceSettingData = await CompanyClient.getCompanyPreferenceSetting({});
  dispatch([
    themeBox.setState({
      logoPath: preferenceSettingData?.preferenceSetting?.logoPath ?? '',
      themeColor: preferenceSettingData?.preferenceSetting?.themeColor ?? '',
    }),
  ]);
});

export const getCompanyPreferenceSetting = action(async (dispatch, select) => {
  // 该函数目前和getInfo 一样，为匿名函数，所以需要做空保护
  const preferenceSettingData = await CompanyClient.getCompanyPreferenceSetting({});
  const options = select(businessOptionsBox);

  dispatch([
    companyMapBox.mergeItemDeep(select(currentCompanyIdBox), {
      ...(preferenceSettingData?.preferenceSetting ?? {}),
      // companyRecord 这里只要有 country name 就行了
      country: preferenceSettingData?.preferenceSetting?.country?.name,
      dateFormat: options.dateFormatList.find(
        (d) => d.type === preferenceSettingData?.preferenceSetting?.dateFormatType,
      )?.label,
      unitOfWeight: options.unitOfWeightList.find(
        (d) => d.type === preferenceSettingData?.preferenceSetting?.unitOfWeightType,
      )?.label,
      unitOfDistance: options.unitOfDistanceList.find(
        (d) => d.type === preferenceSettingData?.preferenceSetting?.unitOfDistanceType,
      )?.label,
    }),
  ]);
  return preferenceSettingData?.preferenceSetting ?? {};
});

export const updateCompanyPreferenceSetting = action(
  async (dispatch, select, input: UpdateCompanyPreferenceSettingDef) => {
    await CompanyClient.updateCompanyPreferenceSetting({ preferenceSetting: input });
    const businessId = select(currentBusinessIdBox);
    const options = select(businessOptionsBox);

    dispatch([
      companyMapBox.mergeItemDeep(select(currentCompanyIdBox), {
        ...input,
      }),
      businessMapBox.mergeItem(
        businessId,
        Object.assign({
          ...input,
          id: businessId,
          dateFormat: options.dateFormatList.find((d) => d.type === input.dateFormatType)?.label,
          unitOfWeight: options.unitOfWeightList.find((d) => d.type === input.unitOfWeightType)?.label,
          unitOfDistance: options.unitOfDistanceList.find((d) => d.type === input.unitOfDistanceType)?.label,
        }),
      ),
    ]);

    return input;
  },
);

export type TBillingDataItem = OpenApiModels['GET/payment/subscription/getBillingData']['Res']['data'][number];

export const getCompanysSubscription = action(
  async (dispatch, select, input: number, _accountId: number = select(currentAccountIdBox)) => {
    const { data } = await http.open('GET/payment/subscription/getBillingData', {
      companyIds: input as any,
    });
    const companyBillingData = data[0] ?? null;
    if (companyBillingData) {
      const company = select(companyMapBox.mustGetItem(input));
      dispatch([
        companyMapBox.mergeItemDeep(
          input,
          Object.assign({
            ...company,
            ...companyBillingData?.planTier,
            companyBillingData: {
              ...company.companyBillingData,
              ...companyBillingData,
              downgradePlanData: {
                ...company.companyBillingData.downgradePlanData,
                ...companyBillingData?.downgradePlanData,
                ...companyBillingData?.downgradePlanData?.planTier,
              },
            },
          }),
        ),
      ]);
    }
    return companyBillingData;
  },
);

export const getCompanyCardInfo = action(
  async (dispatch, select, input: OpenApiModels['GET/payment/subscription/getCompanyCardInfo']['Req']['companyId']) => {
    const { data } = await http.open('GET/payment/subscription/getCompanyCardInfo', {
      companyId: input,
    });
    return {
      companyCardInfo: data,
    };
  },
);

export const toggleFreeAlertVisible = action(
  (dispatch, select, visible: boolean, companyId: number = select(currentCompanyIdBox)) => {
    dispatch([
      companyMapBox.mergeItemDeep(companyId, {
        freeAlertBarVisible: visible,
      }),
    ]);
  },
);

export const cancelDowngrade = action(async (dispatch, select, companyId: number = select(currentCompanyIdBox)) => {
  await http.open('POST/payment/subscription/revertDowngrade', {}, { query: { companyId } });
  const company = select(companyMapBox.mustGetItem(companyId));
  dispatch(
    companyMapBox.mergeItem(companyId, {
      companyBillingData: {
        ...company.companyBillingData,
        isDowngrade: false,
        downgradePlanData: null,
      },
    }),
  );
});

export const toggleA2PAlertBarVisible = action(
  (dispatch, select, visible: boolean, companyId: number = select(currentCompanyIdBox)) => {
    dispatch([
      companyMapBox.mergeItem(companyId, {
        a2pAlertBarVisible: visible,
      }),
    ]);
  },
);

export const getCompanySubscriptionHistory = action(
  async (dispatch, select, companyId: number = select(currentCompanyIdBox)) => {
    const staff = select(selectStaff);
    if (!staff.isOwner()) {
      return [];
    }
    const res = await http.open('GET/payment/subscription/history', {
      companyId,
    });

    dispatch([
      companySubscriptionHistoryListBox.setList(companyId, res.map(get('id'))),
      companySubscriptionHistoryMapBox.mergeItems(id2NumberAndRequired(res)),
    ]);

    return res;
  },
);

export const switchCompany = action(async (dispatch, _select, companyId: number, businessId?: number) => {
  await CompanyClient.switchCompany({
    companyId: `${companyId}`,
    businessId: businessId ? `${businessId}` : undefined,
  });

  if (isNil(businessId)) {
    // 如果没有传 businessId，后台会默认选择第一个有权限的 businessId，所以这里要重新获取一下
    const { business, company } = await http.open('GET/business/account/v2/info');
    dispatch([currentCompanyIdBox.setState(company.id), currentBusinessIdBox.setState(business.id)]);
  } else {
    dispatch([currentCompanyIdBox.setState(companyId), currentBusinessIdBox.setState(businessId)]);
  }
});

/**
 * 只调用接口切换 session 中储存的 CID 和 BID，不修改当前页面的状态
 * 没有判断 CID 和 BID 的合法性
 */
export const switchSessionCompany = action(async (dispatch, select, companyId: string, businessId?: string) => {
  await CompanyClient.switchCompany({
    companyId: `${companyId}`,
    businessId: businessId ? `${businessId}` : undefined,
  });
});

export const getCompanyExtraInfo = action(async (_dispatch, _select) => {
  const { companyExtraInfoMap } = await CompanyClient.getCompaniesExtraInfo({});
  return companyExtraInfoMap;
});

export const modifyCompanyAccountSorting = action(async (_dispatch, _select, input: { ids: string[] }) => {
  await CompanyClient.sortCompany(input);
});

export const createCompany = action(async (_dispatch, _select, input: CreateCompanyParams) => {
  const r = await CompanyClient.createCompany(input);
  return r;
});

export const fillQuestionnaire = action(async (dispatch, select, input: CompanyQuestionRecordParams) => {
  await CompanyClient.companyQuestionRecordSave(input);
  dispatch(
    companyMapBox.mergeItem(Number(input.companyId), {
      isFillQuestion: true,
    }),
  );
});

export const getCompanyQuestionRecord = action(
  async (
    _dispatch,
    select,
    input?: {
      companyId?: number;
      autoToast?: boolean;
    },
  ) => {
    const id = input?.companyId ?? select(currentCompanyIdBox);
    return await CompanyClient.getCompanyQuestionRecord({ companyId: `${id}` }, { autoToast: input?.autoToast });
  },
);

export const checkCompanyAndBusinessValid = action((_dispatch, select, companyId: number, businessId?: number) => {
  const isCompanyValid = select(selectCompanies).includes(companyId);
  if (!isCompanyValid) {
    return false;
  }
  if (businessId) {
    const isBusinessValid = select(selectCompanyBusinessList(companyId)).includes(businessId);
    return isBusinessValid;
  }
  return true;
});
