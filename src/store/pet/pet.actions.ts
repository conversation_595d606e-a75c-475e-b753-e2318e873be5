/*
 * @since 2021-02-08 10:40:30
 * <AUTHOR> <<EMAIL>>
 */

import { type SearchPetForQuickCheckInParams } from '@moego/api-web/moego/api/offering/v1/pet_api';
import { type Dispatchable, action } from 'amos';
import ReactDOM from 'react-dom';
import { type DeepPartial } from 'monofile-utilities/lib/types';
import { PetDeactivateReason } from '../../components/PetInfo/hooks/useClientPets';
import { http } from '../../middleware/api';
import { PetServiceClient } from '../../middleware/clients';
import { type OpenApiModels } from '../../openApi/schema';
import { currentAccountIdBox } from '../account/account.boxes';
import { currentBusinessIdBox } from '../business/business.boxes';
import {
  type SaveCustomerActiveSubscriptionListParam,
  saveCustomerActiveSubscriptionList,
} from '../membership/membership.actions';
import { get, group, mapAssign, uniq } from '../utils/utils';
import { appearanceColorListBox, appearanceColorMapBox } from './appearanceColor.boxes';
import { petDeactivateReasonListBox } from './deactivatedReason.boxes';
import { customerPetListBox, petMapBox, petSummaryListBox } from './pet.boxes';
import { businessPetBehaviorListBox, petBehaviorMapBox } from './petBehavior.boxes';
import { PetBreedRecord, businessPetBreedListBox, petBreedMapBox } from './petBreed.boxes';
import { businessPetCodeListBox, petCodeCommentMapBox, petCodeMapBox } from './petCode.boxes';
import { businessPetFixedListBox, petFixedMapBox } from './petFixed.boxes';
import { businessPetHairLengthListBox, petHairLengthMapBox } from './petHairLength.boxes';
import { petNoteListBox, petNoteMapBox } from './petNote.boxes';
import { petPhotoListBox, petPhotoMapBox } from './petPhoto.boxes';
import { PetTypeRecord, businessPetTypeListBox, petTypeMapBox } from './petType.boxes';
import { petVaccineMapBox } from './petVaccine.boxes';
import { petVaccineBindingListBox, petVaccineBindingMapBox } from './petVaccineBinding.boxes';
import { cancelDelayRender, delayRender } from '../../utils/dom';

export const getPetSummary = action(
  async (
    dispatch,
    select,
    reqData?: DeepPartial<OpenApiModels['POST/customer/list/total']['Req']>,
    businessId: number = select(currentBusinessIdBox),
  ) => {
    const r = await http.open('POST/customer/list/total', reqData as OpenApiModels['POST/customer/list/total']['Req']);
    dispatch(
      petSummaryListBox.setList(
        businessId,
        [
          {
            petTypeId: -1,
            typeName: 'Total clients',
            count: r.count,
          },
          // 新接口 petCountList 可能为 null，以前只会返回 []
        ].concat(r.petCountList?.map((petCount) => ({ ...petCount, typeName: petCount.typeName + 's' })) || []),
      ),
    );
  },
);

export const getPetOptions = action(async (dispatch, select) => {
  const {
    data: {
      petColorList = [],
      petTypeList = [],
      hairLengthList = [],
      petBehaviorList = [],
      petBreedList = [],
      petCodeList = [],
      petFixedList = [],
      petDeactivateReasonList = [],
    },
  } = await http.open('GET/customer/pet/options');
  const currentAccountId = select(currentAccountIdBox);
  dispatch([
    appearanceColorMapBox.mergeItems(petColorList),
    appearanceColorListBox.setList(currentAccountId, petColorList.map(get('colorId'))),
    petBehaviorMapBox.mergeItems(petBehaviorList),
    businessPetBehaviorListBox.setList(currentAccountId, petBehaviorList.map(get('id'))),
    petHairLengthMapBox.mergeItems(hairLengthList),
    businessPetHairLengthListBox.setList(currentAccountId, hairLengthList.map(get('id'))),
    petBreedMapBox.mergeItems(petBreedList),
    businessPetBreedListBox.setLists(
      group(
        petBreedList,
        (r) => PetBreedRecord.ownKey(currentAccountId, r.petTypeId),
        (r) => r.id,
      ),
    ),
    petFixedMapBox.mergeItems(petFixedList),
    businessPetFixedListBox.setList(currentAccountId, petFixedList.map(get('id'))),
    petDeactivateReasonListBox.setList(currentAccountId, petDeactivateReasonList),
    petCodeMapBox.mergeItems(petCodeList.map((c) => ({ ...c, id: c.petCodeId }))),
    businessPetCodeListBox.setList(currentAccountId, petCodeList.map(get('petCodeId'))),
    petTypeMapBox.mergeItems(
      petTypeList.map((t) => ({
        ...t,
        isAvailable: 1,
        ownId: PetTypeRecord.ownId(t.petTypeId, currentAccountId),
      })),
    ),
    businessPetTypeListBox.pushList(
      currentAccountId,
      ...petTypeList.map((t) => PetTypeRecord.ownId(t.petTypeId, currentAccountId)),
    ),
  ] as Dispatchable[]);
});

export const addPet = action(
  async (dispatch, _select, input: Omit<OpenApiModels['POST/customer/pet']['Req'], 'vaccineList'>) => {
    const r = await http.open('POST/customer/pet', input);
    dispatch([
      petMapBox.setItem(r.data, { ...input, petId: r.data }),
      customerPetListBox.pushList(input.customerId!, r.data),
    ]);
    return r;
  },
);

export const getPetList = action(async (dispatch, _select, customerId: number) => {
  const r = await http.open('GET/customer/pet/list', { customerId });
  dispatch([petMapBox.mergeItems(r.data), customerPetListBox.setList(customerId, r.data.map(get('petId')))]);
  return r;
});

const BatchTaskIdList = new Set<number>();
// amos auto batch 之后就不需要在这里 processBatch 了，但原版仍需要，所以先用灰度的方式增加大小，让 auto batch 版本一次 batch

/**
 * 查询pet相关数据,支持批量查询
 */
export const getPetDetail = action(async (dispatch, _select, petIds: number | number[]) => {
  const BATCH_SIZE = 10000;
  const data = await http.open('GET/customer/pet/detail/list', {
    // get请求arr需要转化为1,2,3 而不是默认的petId[0]=xxx&petId[1]=xxx
    petIdList: (Array.isArray(petIds) ? petIds : [petIds]).join() as any,
  });

  // 取消之前的任务
  BatchTaskIdList.forEach((id) => cancelDelayRender(id));
  BatchTaskIdList.clear();

  // 将数据分批处理
  const processBatch = (startIndex: number) => {
    const endIndex = Math.min(startIndex + BATCH_SIZE, data.length);
    ReactDOM.unstable_batchedUpdates(() => {
      for (let i = startIndex; i < endIndex; i++) {
        const petRow = data[i];
        const {
          petDetail,
          petPhotoList = [],
          medicalInfo,
          vaccineBindingList = [],
          petCodeList = [],
          petCodeBindingList = [],
          petNoteList = [],
          incidentReportList = [],
          playgroup,
        } = petRow;

        const petCodeIdList = uniq(petCodeList.map((p) => p.petCodeId));
        const petId = petDetail.petId;
        const incidentReportIds = incidentReportList.map((item) => item.id);

        dispatch([
          petMapBox.mergeItem(petId, {
            ...petDetail,
            ...medicalInfo,
            petCodeIdList,
            incidentReportIds,
            playgroupId: playgroup?.playgroupId ? String(playgroup.playgroupId) : '',
          }),
          petNoteMapBox.mergeItems(petNoteList.map((p) => ({ ...p, petNoteId: p.id }))),
          petCodeMapBox.mergeItems(petCodeList.map((p) => ({ ...p, id: p.petCodeId }))),
          petNoteListBox.setList(petDetail.petId, petNoteList.map(get('id'))),
          petPhotoMapBox.mergeItems(petPhotoList.map((p) => ({ ...p, petId: petDetail.petId }))),
          petPhotoListBox.setList(petDetail.petId, petPhotoList.map(get('petPhotoId'))),
          petVaccineBindingMapBox.mergeItems(mapAssign(vaccineBindingList, { petId: petDetail.petId })),
          petVaccineBindingListBox.setList(petDetail.petId, vaccineBindingList.map(get('vaccineBindingId'))),
          petVaccineMapBox.mergeItems(vaccineBindingList.map((v) => ({ id: v.vaccineId, name: v.vaccineName }))),
        ]);

        dispatch(
          petCodeCommentMapBox.mergeItems(
            petCodeList.map((p) => ({
              petIdAddCodeId: `${petId}+${p.petCodeId}`,
              comment: petCodeBindingList.find((b) => b.codeId === p.petCodeId)?.comment,
            })),
          ),
        );
      }
    });

    if (endIndex < data.length) {
      const id = delayRender(() => {
        processBatch(endIndex);
        BatchTaskIdList.delete(id as number);
      });
      BatchTaskIdList.add(id as number);
    }
  };

  processBatch(0);
  return data;
});

export const updatePet = action(
  async (dispatch, _select, input: Omit<OpenApiModels['PUT/customer/pet']['Req'], 'vaccineList'>) => {
    await http.open('PUT/customer/pet', input);
    dispatch(petMapBox.mergeItem(input.id as number, input));
  },
);

export const markPassAway = action(
  async (
    dispatch,
    _select,
    petId: number,
    lifeStatus: number,
    deactivateReason: string = PetDeactivateReason.PASS_AWAY,
  ) => {
    await http.open('PUT/customer/pet/lifeStatus', { id: petId, lifeStatus, deactivateReason });
    dispatch(petMapBox.mergeItem(petId, { lifeStatus, deactivateReason }));
  },
);

export const removePet = action(async (dispatch, select, id: number) => {
  await http.open('DELETE/customer/pet', { id });
  dispatch(customerPetListBox.deleteItem(select(petMapBox).mustGetItem(id).customerId, id));
});

export const updatePetInfo = action(
  async (_dispatch, _select, input: OpenApiModels['PUT/customer/pet/info']['Req'], signal?: AbortSignal) => {
    const res = await http.open('PUT/customer/pet/info', input, { signal });
    return res;
  },
);

export const getPetListInQuickCheckIn = action(
  async (dispatch, _select, input: SearchPetForQuickCheckInParams, signal?: AbortSignal) => {
    const res = await PetServiceClient.searchPetForQuickCheckIn(input, { signal });

    // TODO<vision,p2> 这里数据有缺漏，只返回了部分pet信息，因为后端内部调用的是老接口，后续最好与 petMapBox 的结构体保持一致
    dispatch(
      petMapBox.mergeItems(
        res.pets.map(({ pet }) => ({ ...pet, petId: Number(pet.id), customerId: Number(pet.customerId) })),
      ),
    );

    // 保存 membership 信息
    const membershipList: SaveCustomerActiveSubscriptionListParam[] = res.pets.map(
      ({ client, membershipSubscriptions }) => {
        return {
          customerId: client.id,
          membershipSubscriptions: membershipSubscriptions.membershipSubscriptions,
        };
      },
    );

    dispatch(saveCustomerActiveSubscriptionList(membershipList));

    return res;
  },
);
