package com.moego.server.grooming.dto;

import com.moego.server.business.dto.CertainAreaDTO;
import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.payment.dto.PreAuthDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
@Schema(description = "appointment service details")
public class GroomingPetServiceListInfoDTO {

    private String appointmentDate;
    private Integer petDetailId;
    private Integer customerAddressId;
    private Integer ticketId;
    private Integer staffId;
    private Integer serviceId;
    private Integer serviceType;
    private Integer serviceTime;
    private Boolean noStartTime;
    private Long startTime;
    private Integer petId;
    private Integer petTypeId;
    private Integer isBlock;
    private Integer repeatId;

    @Schema(description = "1: 已完全支付; 2: 未支付； 3：部分支付 ")
    private Integer isPaid;

    private Long checkInTime;

    private Long checkOutTime;
    private Long createTime;

    private Integer createdById;
    private String createdByLastName;
    private String createdByFirstName;

    // service name
    private String serviceName;
    // service price
    private BigDecimal servicePrice;
    private String serviceColorCode;
    /**
     * service category name
     */
    private String serviceCategoryName;

    @Deprecated // deprecated by ZhangDong on ERP-1747, use appointmentStatus instead
    private Integer status;

    private Byte bookOnlineStatus;
    private Integer customerId;
    private String colorCode;

    // ticket comments
    private String ticketComments;
    private String alertNotes;

    // pet name & breed
    private String petName;
    private String petBreed;
    private String petAvatar;
    private String weight;
    private String coatType;
    private List<PetCodeInfoDTO> moePetCodeInfos;

    private Boolean isNewCustomer;
    // client full name
    private String customerLastName;
    private String customerFirstName;
    private String clientColor;
    // Client phone number
    private String clientPhoneNumber;
    private String customerAvatar;
    private String primaryContactFirstName;
    private String primaryContactLastName;
    private Integer primaryContactId;

    // client full address
    private String address1;
    private String address2;
    private String country;
    private String state;

    // client area
    private List<CertainAreaDTO> areas;

    // City
    private String city;
    // Zipcode
    private String zipcode;

    private String lat;
    private String lng;

    private String clientFullAddress;

    private BigDecimal remainingAmount;
    private BigDecimal totalAmount;
    private Integer invoiceId;

    // 新增已支付金额、定金金额、退款金额，用于显示支付状态
    @Schema(description = "已支付金额")
    private BigDecimal paidAmount;

    @Schema(description = "OB已支付的金额")
    private BigDecimal prepaidAmount;

    @Schema(description = "退款金额")
    private BigDecimal refundAmount;

    @Schema(description = "预支付金额占总金额的比例")
    private Double prepayRate;

    @Schema(description = "预支付记录状态，参考BookOnlineDepositConst")
    private Byte prepayStatus;

    private List<GroomingServiceOperationDTO> operationList;

    private PreAuthDTO preAuthInfo;

    @Schema(description = "预约状态")
    private AppointmentStatusEnum appointmentStatus;

    private Boolean isAutoAccept;
    private AutoAssignDTO autoAssign;

    private Integer serviceTypeInclude;

    private List<Integer> serviceItems;

    private Boolean hasPetParentAppAccount;

    private AppointmentTrackingViewDTO appointmentTracking;
}
