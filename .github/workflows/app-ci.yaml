name: App CI workflow

on:
  push:
    branches:
      - "*"
  workflow_dispatch:
    inputs:
      app-dir:
        description: "Directory to check for changes (backend or frontend)"
        type: choice
        required: true
        default: "backend"
        options:
          - backend
          - frontend
      app-name:
        description: "The name of the app"
        required: true
        default: "tools"
      env:
        description: "Environment to run tests against"
        type: choice
        required: true
        default: "testing"
        options:
          - testing
          - staging
      sync-cd:
        type: boolean
        required: false
        default: true
        description: "Sync the app to ArgoCD"
      skip-deploy:
        type: boolean
        description: "Skip the deploy step"
        required: false
        default: false

jobs:
  # 独立运行的lint检查，不阻塞CI流程
  lint:
    name: Code Quality Checks
    uses: ./.github/workflows/lint.yaml
    secrets: inherit

  parse-changed-apps:
    runs-on: ubuntu-24.04
    container:
      image: ghcr.io/moegolibrary/moego-ci-builder:latest
      credentials:
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    outputs:
      CHANGED_APPS_BACKEND: ${{ steps.get_changed_apps.outputs.CHANGED_APPS_BACKEND }}
      CHANGED_APPS_FRONTEND: ${{ steps.get_changed_apps.outputs.CHANGED_APPS_FRONTEND }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup git safe directory
        run: |
          git config --global --add safe.directory "$(pwd)"

      - name: Get changed apps
        id: get_changed_apps
        shell: bash
        run: |
          changed_apps_backend=""
          changed_apps_frontend=""
          # if triggered manually, only build a single app
          if [ "${{ github.event_name }}" == "workflow_dispatch" ]; then
            if [ "${{ inputs.app-dir }}" == "backend" ]; then
              changed_apps_backend=${{ inputs.app-name }}
            elif [ "${{ inputs.app-dir }}" == "frontend" ]; then
              changed_apps_frontend=${{ inputs.app-name }}
            fi
          else
            changed_apps_backend=$(bash scripts/.get_changed_apps.sh backend)
            changed_apps_frontend=$(bash scripts/.get_changed_apps.sh frontend)
          fi

          echo "CHANGED_APPS_BACKEND=${changed_apps_backend}" >> $GITHUB_OUTPUT
          echo "CHANGED_APPS_FRONTEND=${changed_apps_frontend}" >> $GITHUB_OUTPUT

          echo "changed_apps_backend=${changed_apps_backend}"
          echo "changed_apps_frontend=${changed_apps_frontend}"

  build-frontend:
    needs:
      - parse-changed-apps
    if: needs.parse-changed-apps.outputs.CHANGED_APPS_FRONTEND != ''
    runs-on: ubuntu-24.04
    container:
      image: ghcr.io/moegolibrary/moego-ci-builder:latest
      credentials:
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    env:
      CHANGED_APPS: ${{ needs.parse-changed-apps.outputs.CHANGED_APPS_FRONTEND }}
    outputs:
      CHANGED_APPS: ${{ needs.parse-changed-apps.outputs.CHANGED_APPS_FRONTEND }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup GitHub
        uses: MoeGolibrary/moego-actions-tool/.github/actions/setup-github/@production
        with:
          ADMIN_TOKEN_GITHUB: ${{ secrets.ADMIN_TOKEN_GITHUB }}

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-west-2
        continue-on-error: false

      - name: Build
        shell: bash
        env:
          NODE_OPTIONS: --max_old_space_size=8192
        run: |
          for app in $CHANGED_APPS; do
            echo "Building ${app}"
            if [ -d "frontend/app/${app}" ] && [ "${app}" == "devops-console" ]; then
              cd frontend/app/${app}
              node -v
              npm -v
              npm i
              npm run build
              docker build \
                --build-arg APP_NAME=moego-devops-console \
                -t moego-devops-console \
                -f ./ci/Dockerfile .

            else
              # TODO: implement frontend app build in the future
              echo "Skipping ${app} because it is not a frontend app"
            fi
          done

      - name: Prepare AWS ECR && push docker image
        run: |
          aws ecr get-login-password --region us-west-2 | docker login --username AWS --password-stdin 693727988157.dkr.ecr.us-west-2.amazonaws.com

          for app in $CHANGED_APPS; do
            if [ -d "frontend/app/${app}" ]; then

              spec_name=$(yq '.spec.name' frontend/app/${app}/metadata.yaml)

              if ! aws ecr describe-repositories --region us-west-2 --repository-names ${spec_name} > /dev/null 2>&1 ; then
                echo "${YELLOW}ECR repository ${spec_name} does not exist, creating...${NC}"
                aws ecr create-repository --region us-west-2 --repository-name ${spec_name}
              fi

              # set lifecycle policy 
              aws ecr put-lifecycle-policy --region us-west-2 --repository-name ${spec_name} --lifecycle-policy-text file://backend/docker/ecr-lifecycle-policy.json

              # image tags 
              branch_tag=${{ github.ref_name }}
              sha_tag=sha-${{ github.sha }}

              # tag and push docker image
              for tag in ${branch_tag} ${sha_tag}; do
                docker tag ${spec_name} 693727988157.dkr.ecr.us-west-2.amazonaws.com/${spec_name}:${tag}
                docker push 693727988157.dkr.ecr.us-west-2.amazonaws.com/${spec_name}:${tag}
                echo -e "${GREEN}Pushed docker image to ECR: 693727988157.dkr.ecr.us-west-2.amazonaws.com/${spec_name}:${tag}${NC}"
              done
            fi
          done

  build-backend:
    needs:
      - parse-changed-apps
    if: needs.parse-changed-apps.outputs.CHANGED_APPS_BACKEND != ''
    runs-on: ubuntu-24.04
    container:
      image: ghcr.io/moegolibrary/moego-ci-builder:latest
      credentials:
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    env:
      CHANGED_APPS: ${{ needs.parse-changed-apps.outputs.CHANGED_APPS_BACKEND }}
    outputs:
      CHANGED_APPS: ${{ needs.parse-changed-apps.outputs.CHANGED_APPS_BACKEND }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup git safe directory
        run: |
          git config --global --add safe.directory "$(pwd)"

      - name: Setup GitHub
        uses: MoeGolibrary/moego-actions-tool/.github/actions/setup-github/@production
        with:
          ADMIN_TOKEN_GITHUB: ${{ secrets.ADMIN_TOKEN_GITHUB }}

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-west-2
        continue-on-error: false

      - name: Cache Bazel Dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.cache/bazel-repo
            ~/.cache/bazel/external
          key: "${{ runner.os }}-bazel-deps-${{ hashFiles('MODULE.bazel', 'WORKSPACE', 'go.mod', 'go.sum') }}"
          restore-keys: |
            ${{ runner.os }}-bazel-deps-

      - name: Cache Bazel Build
        uses: actions/cache@v4
        with:
          path: |
            ~/.cache/bazel
            !~/.cache/bazel/*/sandbox
            !~/.cache/bazel/*/temp
            !~/.cache/bazel/*/execroot/*/bazel-out/*/bin/*.runfiles
          key: "${{ runner.os }}-bazel-build-${{ hashFiles('MODULE.bazel', 'go.mod') }}-${{ hashFiles('backend/**/*.go', 'backend/**/*.proto', '**/BUILD.bazel') }}"
          restore-keys: |
            ${{ runner.os }}-bazel-build-${{ hashFiles('MODULE.bazel', 'go.mod') }}-
            ${{ runner.os }}-bazel-build-

      - name: Init
        shell: bash
        run: |
          mkdir -p /tmp/bazel-sandbox
          source scripts/common.sh
          go env -w 'GOPRIVATE=github.com/MoeGolibrary/*'
          
          find ~/.cache/bazel -name "*.tmp" -delete 2>/dev/null || true
          find ~/.cache/bazel -type d -name "sandbox" -exec rm -rf {} + 2>/dev/null || true

      - name: Test
        if: env.CHANGED_APPS != ''
        run: |
          make otest

      - name: Build
        shell: bash
        if: env.CHANGED_APPS != '' && success()
        run: |
          for app in $CHANGED_APPS; do
            if [ -d "backend/app/$app" ]; then
              echo -e "${YELLOW}building ${app} ...${NC}"

              # get app metadata 
              spec_name=$(yq '.spec.name' backend/app/${app}/metadata.yaml)
              echo -e "${YELLOW}spec_name: ${spec_name}${NC}"

              # build app
              need_build=$(yq '.spec.need_build' backend/app/${app}/metadata.yaml 2>/dev/null || echo "true")

              if [[ "$need_build" == "false" ]]; then
                echo "Skipping build because spec.need_build is false"
              else
                make obuild dir=//backend/app/${app}
                cp $(bazelisk --bazelrc=bazel/.bazelrc info bazel-bin)/backend/app/${app}/${app}_/${app} backend/app/${app}/${app}
                ls -l backend/app/${app}
              fi
              
              # build docker image
              if [ -f "backend/app/${app}/Dockerfile" ]; then
                docker build \
                  --build-arg APP_NAME=${app} \
                  --build-arg BIN_DIR=$(bazelisk --bazelrc=bazel/.bazelrc info bazel-bin) \
                  --build-arg DD_GIT_REPOSITORY_URL=$(git config --get remote.origin.url) \
                  --build-arg DD_GIT_COMMIT_SHA=$(git rev-parse HEAD) \
                  -t ${spec_name} \
                  -f backend/app/${app}/Dockerfile .
              else
                docker build \
                  --build-arg APP_NAME=${app} \
                  --build-arg BIN_DIR=$(bazelisk --bazelrc=bazel/.bazelrc info bazel-bin) \
                  --build-arg DD_GIT_REPOSITORY_URL=$(git config --get remote.origin.url) \
                  --build-arg DD_GIT_COMMIT_SHA=$(git rev-parse HEAD) \
                  -t ${spec_name} \
                  -f ./backend/docker/Dockerfile .
              fi
            fi
          done

      - name: Prepare AWS ECR && push docker image
        if: env.CHANGED_APPS != '' && success()
        run: |
          aws ecr get-login-password --region us-west-2 | docker login --username AWS --password-stdin 693727988157.dkr.ecr.us-west-2.amazonaws.com 

          for app in $CHANGED_APPS; do
            if [ -d "backend/app/${app}" ]; then

              spec_name=$(yq '.spec.name' backend/app/${app}/metadata.yaml)

              if ! aws ecr describe-repositories --region us-west-2 --repository-names ${spec_name} > /dev/null 2>&1 ; then
                echo "${YELLOW}ECR repository ${spec_name} does not exist, creating...${NC}"
                aws ecr create-repository --region us-west-2 --repository-name ${spec_name}
              fi

              # image tags 
              branch_tag=${{ github.ref_name }}
              sha_tag=sha-${{ github.sha }}

              # set lifecycle policy 
              aws ecr put-lifecycle-policy --region us-west-2 --repository-name ${spec_name} --lifecycle-policy-text file://backend/docker/ecr-lifecycle-policy.json

              # tag and push docker image
              for tag in ${branch_tag} ${sha_tag}; do
                docker tag ${spec_name} 693727988157.dkr.ecr.us-west-2.amazonaws.com/${spec_name}:${tag}
                docker push 693727988157.dkr.ecr.us-west-2.amazonaws.com/${spec_name}:${tag}
                echo -e "${GREEN}Pushed docker image to ECR: 693727988157.dkr.ecr.us-west-2.amazonaws.com/${spec_name}:${tag}${NC}"
              done
            fi
          done

  deploy-frontend:
    needs:
      - build-frontend
    if: github.event_name == 'push' || inputs.skip-deploy != 'true'
    name: Deploy Frontend Service
    uses: ./.github/workflows/app-deploy.yml
    secrets: inherit
    with:
      app-dir: "frontend"
      apps: ${{ needs.build-frontend.outputs.CHANGED_APPS }}
      env: ${{ inputs.env || 'testing' }}
      image-tag: "sha-${{ github.sha }}"
      sync-cd: ${{ github.event_name == 'push' || inputs.sync-cd }}

  deploy-backend:
    needs:
      - build-backend
    if: github.event_name == 'push' || inputs.skip-deploy != 'true'
    name: Deploy Backend Service
    uses: ./.github/workflows/app-deploy.yml
    secrets: inherit
    with:
      app-dir: "backend"
      apps: ${{ needs.build-backend.outputs.CHANGED_APPS }}
      env: ${{ inputs.env || 'testing' }}
      image-tag: "sha-${{ github.sha }}"
      sync-cd: ${{ github.event_name == 'push' || inputs.sync-cd }}

  check-proto-changes:
    if: github.event_name == 'push' || inputs.skip-deploy != 'true'
    name: Check proto changes
    runs-on: ubuntu-24.04
    container:
      image: ghcr.io/moegolibrary/moego-ci-builder:latest
      credentials:
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Filter Out Changed Files
        id: filter_changed_files
        run: |
          # 检查变更的文件
          echo "Checking for changes that require npm publish..."

          git config --global --add safe.directory "$(pwd)"

          # 确保有完整的Git历史（避免fetch到当前分支）
          CURRENT_BRANCH=$(git branch --show-current)
          if [ "$CURRENT_BRANCH" != "main" ]; then
            git fetch origin main:main || echo "Could not fetch main branch, will use available history"
          else
            echo "Already on main branch, no need to fetch"
          fi

          # 尝试获取变更文件列表
          if git rev-parse HEAD^ >/dev/null 2>&1; then
            # 有父提交，检查当前提交的变更
            git diff --name-only HEAD^ HEAD > changed_files.txt
            echo "Compared with parent commit"
          else
            # 没有父提交，尝试检查与main分支的差异
            if git rev-parse main >/dev/null 2>&1; then
              git diff --name-only main HEAD > changed_files.txt
              echo "Compared with main branch (no parent commit found)"
            else
              # 如果main分支也不存在，检查所有变更
              echo "No parent commit or main branch found, checking all changes"
              git diff --name-only > changed_files.txt || echo "" > changed_files.txt
            fi
          fi

          # 如果与main分支的差异还没有包含，则添加
          if git rev-parse HEAD^ >/dev/null 2>&1 && git rev-parse main >/dev/null 2>&1; then
            git diff --name-only main HEAD >> changed_files.txt
          fi

          # 统计相关变更
          HAS_PROTO_CHANGES=$(grep -c 'backend/proto/' changed_files.txt || true)
          HAS_PACKAGE_JSON_CHANGES=$(grep -c -E 'package.json|yarn.lock|package-lock.json' changed_files.txt || true)
          HAS_NPM_PATCHES_CHANGES=$(grep -c 'patches/' changed_files.txt || true)
          HAS_TEMPLATE_CHANGES=$(grep -c 'template/' changed_files.txt || true)
          HAS_BUFGEN_CHANGES=$(grep -c -E 'buf.gen.yaml|buf.yaml|buf.lock' changed_files.txt || true)
          HAS_CHANGES=$(( HAS_PROTO_CHANGES + HAS_PACKAGE_JSON_CHANGES + HAS_NPM_PATCHES_CHANGES + HAS_TEMPLATE_CHANGES + HAS_BUFGEN_CHANGES ))

          echo "Change summary:"
          echo "  - Proto changes: $HAS_PROTO_CHANGES"
          echo "  - Package.json changes: $HAS_PACKAGE_JSON_CHANGES"
          echo "  - NPM patches changes: $HAS_NPM_PATCHES_CHANGES"
          echo "  - Template changes: $HAS_TEMPLATE_CHANGES"
          echo "  - Buf config changes: $HAS_BUFGEN_CHANGES"
          echo "  - Total relevant changes: $HAS_CHANGES"

          echo "HAS_CHANGES=${HAS_CHANGES}" >> $GITHUB_OUTPUT

          if [ $HAS_CHANGES -eq 0 ]; then
            echo "No changes requiring npm publish detected, skipping publish."
          else
            echo "Changes detected that require npm publish."
          fi
    outputs:
      HAS_CHANGES: ${{ steps.filter_changed_files.outputs.HAS_CHANGES }}

  publish-npm:
    needs:
      - check-proto-changes
    if: needs.check-proto-changes.outputs.HAS_CHANGES != '0'
    runs-on: ubuntu-24.04
    container:
      image: ghcr.io/moegolibrary/moego-ci-builder:latest
      credentials:
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Install Node Dependencies
        run: |
          yarn install

      - name: Build proto
        run: |
          make proto

      - name: Generate serviceClient
        run: yarn sync-applications

      - name: Publish npm
        env:
          BRANCH_NAME: ${{ github.ref_name }}
          GITHUB_SHA: ${{ github.sha }}
          NPM_PUBLISHER_USR: ${{ vars.NPM_PUBLISHER_USR }}
          NPM_PUBLISHER_PSW: ${{ secrets.NPM_PUBLISHER_PSW }}
        run: |
          bash scripts/npm_publish.sh
